using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp
{
    public interface IWhatsAppService
    {
        Task<BaseResponse> SendToWhatsApp(SendToWhatsAppRequest req);
        Task<BaseResponse> SendToWhatsAppExp(SendToWhatsAppRequest req);
        Task<BaseResponse> SendToWhatsAppCash(SendToWhatsAppRequest req);
        Task<BaseResponse> SendPDFToWhatsApp(SendPDFToWhatsAppRequest req);
        Task<BaseResponse> SendToWhatsAppAlert(SendToWhatsAppRequest req);
        Task<BaseResponse> SendToWhatsAppOTP(SendToWhatsAppRequest req);
        Task<BaseResponse> SendToWhatsAppCKR(SendToWhatsAppRequest req);
    }
}
