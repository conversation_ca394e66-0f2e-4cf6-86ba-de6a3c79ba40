﻿using StackExchange.Redis;
using System;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Shared
{
    internal class RedisClass
    {
        private Lazy<ConnectionMultiplexer> lazyConnection;

        public RedisClass(string host, int port)
        {
            this.lazyConnection = new Lazy<ConnectionMultiplexer>(() =>
            {
                return ConnectionMultiplexer.Connect(host + ":" + port.ToString());
            });
        }

        public ConnectionMultiplexer Connection
        {
            get
            {
                return lazyConnection.Value;
            }
        }

        public void Dispose()
        {
            lazyConnection = null;
        }
    }
}
