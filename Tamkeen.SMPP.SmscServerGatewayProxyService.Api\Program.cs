﻿using Autofac.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Org.BouncyCastle.Utilities;
using Serilog;
using System.Configuration;
using System.Net;
using System.IO;
using Microsoft.Extensions.Configuration;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using OpenTelemetry.Instrumentation.AspNetCore;
using OpenTelemetry.Instrumentation.Http;


namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            #region RunProgramWithTry

            try
            {
                ServicePointManager.ServerCertificateValidationCallback +=
                    (sender, cert, chain, sslPolicyErrors) => true;
                var logConfig = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddEnvironmentVariables()
                    .Build();
                var logPath = logConfig["Serilog:LogPath"] ?? "c:/logs/TamkeenSMPPSmscServerProxyService";
                Log.Logger = new LoggerConfiguration()
                    .WriteTo.Console()
                    .WriteTo.File(Path.Combine(logPath, "starup-log.txt"))
                    .CreateLogger();
                Log.Logger.Information("Application Environment: {Environment}", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));
                string aspenv = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Test";
                if (aspenv == "Kube" || aspenv == "Docker")
                {
                    var configuration = new ConfigurationBuilder()
                       .SetBasePath(Directory.GetCurrentDirectory())
                       .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                       .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json", optional: true)
                       .AddEnvironmentVariables() // Enable environment variable substitution
                       .Build();
                    var resolvedConfig = new ConfigurationBuilder()
                        .AddInMemoryCollection(configuration.AsEnumerable()
                            .Select(kvp =>
                            {
                                if (kvp.Value != null && kvp.Value.StartsWith("${") && kvp.Value.EndsWith("}"))
                                {
                                    var envVar = kvp.Value.Trim('$', '{', '}');
                                    var resolvedValue = Environment.GetEnvironmentVariable(envVar) ?? kvp.Value;
                                    return new KeyValuePair<string, string>(kvp.Key, resolvedValue);
                                }
                                return kvp; // No modification needed if value doesn't match the condition
                            })
                            .Cast<KeyValuePair<string, string?>>()) // Explicitly cast to match nullability
                        .Build();
                    //Log resolved configuration
                    foreach (var kvp in resolvedConfig.AsEnumerable())
                    {
                        Log.Logger.Information("{Key}: {Value}", kvp.Key, kvp.Value);
                    }
                    CreateHostBuilder(args, resolvedConfig).Build().Run();
                }
                else
                {
                    CreateHostBuilder(args).Build().Run();
                }
                Log.Information("Starting the application...");
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application start-up failed");
            }
            finally
            {
                Log.CloseAndFlush();
            }
            #endregion
            //// Enforce modern TLS protocols
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

            //// Bypass SSL validation (use only in development environments)
            //ServicePointManager.ServerCertificateValidationCallback +=
            //    (sender, certificate, chain, sslPolicyErrors) => true;

            
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseServiceProviderFactory(new AutofacServiceProviderFactory()) // إضافة Autofac
                .ConfigureServices((context, services) =>
                {
                    services.AddOpenTelemetry()
                        .ConfigureResource(builder => builder.AddService("SmscServerGatewayProxyService.Api"))
                        .WithTracing(tracing => tracing
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation()
                            .AddJaegerExporter())
                        .WithMetrics(metrics => metrics
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation()
                            .AddPrometheusExporter());
                })
                //.UseSerilog((hostContext, loggerConfiguration) => // تهيئة Serilog
                //{
                //    loggerConfiguration.ReadFrom.Configuration(hostContext.Configuration);
                //})
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>(); // استخدام ملف Startup
                });

        public static IHostBuilder CreateHostBuilder(string[] args, IConfiguration configuration) =>
            Host.CreateDefaultBuilder(args)
                .UseServiceProviderFactory(new AutofacServiceProviderFactory()) // إضافة Autofac
                //.UseSerilog((hostContext, loggerConfiguration) => // تهيئة Serilog
                //{
                //    loggerConfiguration.ReadFrom.Configuration(hostContext.Configuration);
                //})
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddConfiguration(configuration); // Use resolved configuration
                })
                .ConfigureServices((context, services) =>
                {
                    services.AddOpenTelemetry()
                        .ConfigureResource(builder => builder.AddService("SmscServerGatewayProxyService.Api"))
                        .WithTracing(tracing => tracing
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation()
                            .AddJaegerExporter())
                        .WithMetrics(metrics => metrics
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation()
                            .AddPrometheusExporter());
                })
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>(); // استخدام ملف Startup
                });
    }
}
