﻿using Autofac.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Org.BouncyCastle.Utilities;
using Serilog;
using System.Configuration;
using System.Net;
using VaultSharp;
using VaultSharp.V1.AuthMethods;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Configuration;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            
            // إضافة HashiCorp Vault
            ConfigureVault(builder.Configuration);
            
            try
            {
                ServicePointManager.ServerCertificateValidationCallback +=
                    (sender, cert, chain, sslPolicyErrors) => true;
                    
                Log.Logger = new LoggerConfiguration()
                    .WriteTo.Console()
                    .WriteTo.File("c:/logs/TamkeenSMPPSmscServerProxyService/startup-log.txt")
                    .CreateLogger();
                    
                Log.Logger.Information("Application Environment: {Environment}", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));
                
                string aspenv = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Test";
                
                if (aspenv == "Kube" || aspenv == "Docker" || aspenv == "vault")
                {
                    var configuration = BuildConfigurationWithVault();
                    CreateHostBuilder(args, configuration).Build().Run();
                }
                else
                {
                    CreateHostBuilder(args).Build().Run();
                }
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application start-up failed");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static void ConfigureVault(IConfiguration configuration)
        {
            var vaultUrl = configuration["Vault:Address"];
            var vaultToken = configuration["Vault:Token"];
            
            if (!string.IsNullOrEmpty(vaultUrl) && !string.IsNullOrEmpty(vaultToken))
            {
                // تكوين Vault Client
                IAuthMethodInfo authMethod = new TokenAuthMethodInfo(vaultToken);
                var vaultClientSettings = new VaultClientSettings(vaultUrl, authMethod);
                IVaultClient vaultClient = new VaultClient(vaultClientSettings);
                
                // حفظ Vault Client في DI Container
                var services = new ServiceCollection();
                services.AddSingleton(vaultClient);
            }
        }

        private static IConfiguration BuildConfigurationWithVault()
        {
            var configBuilder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true, reloadOnChange: true)
                .AddJsonFile("appsettings.vault.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            var tempConfig = configBuilder.Build();

            // التحقق من وجود إعدادات Vault
            var vaultAddress = tempConfig["Vault:Address"];
            var vaultToken = tempConfig["Vault:Token"];

            if (!string.IsNullOrEmpty(vaultAddress) && !string.IsNullOrEmpty(vaultToken))
            {
                try
                {
                    // قراءة إعدادات Vault مع قيم افتراضية آمنة
                    var vaultMountPoint = tempConfig["Vault:MountPoint"] ?? "secret";
                    var vaultSecretPath = tempConfig["Vault:SecretPath"] ?? "data/tamkeen-smpp";
                    var ignoreSslErrors = SafeParseBool(tempConfig["Vault:IgnoreSslErrors"], false);

                    Log.Logger?.Information(
                        "Configuring Vault provider - Address: {VaultAddress}, MountPoint: {MountPoint}, SecretPath: {SecretPath}, IgnoreSSL: {IgnoreSSL}",
                        vaultAddress, vaultMountPoint, vaultSecretPath, ignoreSslErrors);

                    // التحقق من صحة URL
                    if (!Uri.TryCreate(vaultAddress, UriKind.Absolute, out var vaultUri))
                    {
                        Log.Logger?.Warning("Invalid Vault address format: {VaultAddress}, skipping Vault configuration", vaultAddress);
                        return configBuilder.Build();
                    }

                    // إضافة Vault Configuration Provider
                    var vaultSource = new VaultConfigurationSource
                    {
                        VaultAddress = vaultAddress,
                        VaultToken = vaultToken,
                        SecretPath = vaultSecretPath,
                        MountPoint = vaultMountPoint,
                        IgnoreSslErrors = ignoreSslErrors
                    };

                    configBuilder.Add(vaultSource);

                    Log.Logger?.Information("Vault configuration provider added successfully");
                }
                catch (Exception ex)
                {
                    Log.Logger?.Error(ex, "Failed to configure Vault provider: {ErrorMessage}", ex.Message);
                    Log.Logger?.Warning("Continuing without Vault configuration - using fallback values");
                    // Continue without Vault if it fails
                }
            }
            else
            {
                Log.Logger?.Information("Vault configuration not found or incomplete, skipping Vault provider");
            }

            var finalConfig = configBuilder.Build();

            // التحقق من صحة التكوين النهائي
            try
            {
                ValidateBasicConfiguration(finalConfig);
            }
            catch (Exception ex)
            {
                Log.Logger?.Error(ex, "Configuration validation failed: {ErrorMessage}", ex.Message);
            }

            return finalConfig;
        }

        /// <summary>
        /// تحليل آمن للقيم المنطقية
        /// </summary>
        private static bool SafeParseBool(string value, bool defaultValue)
        {
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            return value.ToLowerInvariant() switch
            {
                "true" or "1" or "yes" or "on" or "enabled" => true,
                "false" or "0" or "no" or "off" or "disabled" => false,
                _ => defaultValue
            };
        }

        /// <summary>
        /// التحقق من صحة التكوين الأساسي
        /// </summary>
        private static void ValidateBasicConfiguration(IConfiguration configuration)
        {
            var issues = new List<string>();

            // فحص الأقسام المطلوبة
            var requiredSections = new[] { "SmscSettings", "FcmWhatsappSettings" };
            foreach (var section in requiredSections)
            {
                if (!configuration.GetSection(section).Exists())
                {
                    issues.Add($"Missing required section: {section}");
                }
            }

            // فحص القيم الحرجة
            var criticalValues = new[]
            {
                "FcmWhatsappSettings:DalUrl",
                "TamkeenLoggingConfig:ProjectName"
            };

            foreach (var key in criticalValues)
            {
                if (string.IsNullOrEmpty(configuration[key]))
                {
                    issues.Add($"Missing critical configuration: {key}");
                }
            }

            if (issues.Count > 0)
            {
                Log.Logger?.Warning("Configuration validation issues found: {Issues}", string.Join(", ", issues));
            }
            else
            {
                Log.Logger?.Information("Basic configuration validation passed");
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseServiceProviderFactory(new AutofacServiceProviderFactory()) // إضافة Autofac
                //.UseSerilog((hostContext, loggerConfiguration) => // تهيئة Serilog
                //{
                //    loggerConfiguration.ReadFrom.Configuration(hostContext.Configuration);
                //})
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>(); // استخدام ملف Startup
                });

        public static IHostBuilder CreateHostBuilder(string[] args, IConfiguration configuration) =>
            Host.CreateDefaultBuilder(args)
                .UseServiceProviderFactory(new AutofacServiceProviderFactory()) // إضافة Autofac
                                                                                //.UseSerilog((hostContext, loggerConfiguration) => // تهيئة Serilog
                                                                                //{
                                                                                //    loggerConfiguration.ReadFrom.Configuration(hostContext.Configuration);
                                                                                //})
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddConfiguration(configuration); // Use resolved configuration
                })
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>(); // استخدام ملف Startup
                });
    }
}
