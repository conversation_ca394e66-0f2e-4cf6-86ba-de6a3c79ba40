# SMS Samples

This directory contains sample code previously embedded in `SendSmscController`. These examples demonstrate how to route SMS messages based on the destination number and how to fall back to WhatsApp when the destination is not local.

```csharp
// Example of routing SMS locally or via WhatsApp
if (request.smsDstType == SmsDstType.local)
{
    // Determine the operator from the destination number
    string dstAdr = request.dstAdr.Length == 12 ? request.dstAdr.Substring(3, 2) : request.dstAdr.Substring(0, 2);
    switch (dstAdr)
    {
        case "77":
        case "78":
            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.YemenMobile);
            break;
        case "71":
            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.SabaFonTrans);
            break;
        case "73":
            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.YouTrans);
            break;
        case "70":
            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.YGsm);
            break;
        default:
            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.Other);
            break;
    }
    sendSmsSmscRequest.dstAdr = request.dstAdr;
    sendSmsSmscRequest.smsText = request.smsText;
    sendSmsSmscRequest.deliveryReceipt = SMSCDeliveryReceipt.SuccessOrFailure;

    var command = sendSmsSmscRequest.Adapt<SendSmsSmscCommand>();
    var response = await serviceManager.SendSmsAsync(sendSmsSmscRequest.transceiverSystemId, command, cancellationToken);
    return Ok(new { response.resultCode, response.resultMessage });
}
else
{
    var sendToWhatsAppRequest = new SendToWhatsAppRequest
    {
        To = request.dstAdr,
        Message = request.smsText
    };

    var whatsappResponse = await whatsAppService.SendToWhatsApp(sendToWhatsAppRequest);
    return Ok(whatsappResponse);
}
```
