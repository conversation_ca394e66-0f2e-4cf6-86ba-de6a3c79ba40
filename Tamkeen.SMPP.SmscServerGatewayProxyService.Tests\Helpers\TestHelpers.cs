using AutoFixture;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Tests.Helpers
{
    /// <summary>
    /// مساعدات للاختبارات - تحتوي على دوال مشتركة لإنشاء البيانات والكائنات الوهمية
    /// </summary>
    public static class TestHelpers
    {
        private static readonly Fixture _fixture = new Fixture();

        /// <summary>
        /// إنشاء طلب إرسال رسالة صحيح للاختبار
        /// </summary>
        public static SendSmsSmscRequest CreateValidSendSmsRequest(
            string phoneNumber = "96777123456",
            string message = "Test message",
            string transceiverSystemId = "YemenMobile")
        {
            return new SendSmsSmscRequest
            {
                transceiverSystemId = transceiverSystemId,
                dstAdr = phoneNumber,
                smsText = message,
                srcTon = Tamkeen.Inetlab.SMPP.Common.AddressTON.Alphanumeric,
                srcNpi = Tamkeen.Inetlab.SMPP.Common.AddressNPI.Unknown,
                deliveryReceipt = Tamkeen.Inetlab.SMPP.Common.SMSCDeliveryReceipt.SuccessOrFailure
            };
        }

        /// <summary>
        /// إنشاء أمر إرسال رسالة للاختبار
        /// </summary>
        public static SendSmsSmscCommand CreateValidSendSmsCommand(
            string phoneNumber = "96777123456",
            string message = "Test message")
        {
            return new SendSmsSmscCommand
            {
                dstAdr = phoneNumber,
                smsText = message,
                srcTon = Tamkeen.Inetlab.SMPP.Common.AddressTON.Alphanumeric,
                srcNpi = Tamkeen.Inetlab.SMPP.Common.AddressNPI.Unknown,
                deliveryReceipt = Tamkeen.Inetlab.SMPP.Common.SMSCDeliveryReceipt.SuccessOrFailure
            };
        }

        /// <summary>
        /// إنشاء استجابة ناجحة للاختبار
        /// </summary>
        public static BaseSmscResponse CreateSuccessResponse(string message = "SMS sent successfully")
        {
            return new BaseSmscResponse
            {
                resultCode = 1,
                resultMessage = message
            };
        }

        /// <summary>
        /// إنشاء استجابة فاشلة للاختبار
        /// </summary>
        public static BaseSmscResponse CreateErrorResponse(string message = "SMS sending failed")
        {
            return new BaseSmscResponse
            {
                resultCode = -1,
                resultMessage = message
            };
        }

        /// <summary>
        /// إنشاء تكوين وهمي للاختبارات
        /// </summary>
        public static IConfiguration CreateMockConfiguration()
        {
            var configData = new Dictionary<string, string>
            {
                // Yemen Mobile Configuration
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96777|0096777|77|\\+96777|96778|0096778|78|\\+96778)",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YemenMobile",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:ton"] = "5",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:npi"] = "1",

                // SabaFon Configuration
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96771|0096771|71|\\+96771)",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "SabaFonTrans",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:ton"] = "1",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:npi"] = "1",

                // YOU Configuration
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96773|0096773|73|\\+96773)",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YouTrans",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:ton"] = "1",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:npi"] = "1",

                // Y GSM Configuration
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96770|0096770|70|\\+96770)",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YGsm",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:ton"] = "5",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:npi"] = "0",

                // WhatsApp Configuration
                ["FcmWhatsappSettings:DalUrl"] = "http://localhost:8083",
                ["FcmWhatsappSettings:authToken"] = "test-token",
                ["FcmWhatsappSettings:PhoneNumberID"] = "test-phone-id",

                // Logging Configuration
                ["Logging:LogLevel:Default"] = "Warning",
                ["Logging:LogLevel:Microsoft"] = "Warning"
            };

            var configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.AddInMemoryCollection(configData);
            return configurationBuilder.Build();
        }

        /// <summary>
        /// إنشاء logger وهمي للاختبارات
        /// </summary>
        public static Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        /// <summary>
        /// إنشاء ServiceCollection للاختبارات
        /// </summary>
        public static IServiceCollection CreateTestServiceCollection()
        {
            var services = new ServiceCollection();
            
            // إضافة الخدمات الأساسية
            services.AddLogging();
            services.AddSingleton(CreateMockConfiguration());
            
            return services;
        }

        /// <summary>
        /// بيانات اختبار لأرقام هواتف مختلفة
        /// </summary>
        public static class PhoneNumbers
        {
            // Yemen Mobile Numbers
            public static readonly string[] YemenMobile = {
                "96777123456", "0096777123456", "77123456", "+96777123456",
                "96778123456", "0096778123456", "78123456", "+96778123456"
            };

            // SabaFon Numbers
            public static readonly string[] SabaFon = {
                "96771123456", "0096771123456", "71123456", "+96771123456"
            };

            // YOU Numbers
            public static readonly string[] You = {
                "96773123456", "0096773123456", "73123456", "+96773123456"
            };

            // Y GSM Numbers
            public static readonly string[] YGsm = {
                "96770123456", "0096770123456", "70123456", "+96770123456"
            };

            // Invalid Numbers
            public static readonly string[] Invalid = {
                "96772123456", "96779123456", "1234567890", "+1234567890",
                "96774123456", "", " ", "abc", "96775123456"
            };
        }

        /// <summary>
        /// رسائل اختبار مختلفة
        /// </summary>
        public static class TestMessages
        {
            public const string Short = "Hi";
            public const string Normal = "This is a test message";
            public const string Long = "This is a very long test message that exceeds the normal SMS length to test how the system handles long messages and whether it splits them correctly or handles them as a single message depending on the configuration.";
            public const string Arabic = "هذه رسالة تجريبية باللغة العربية";
            public const string Mixed = "Mixed message مختلط 123";
            public const string WithEmojis = "Test message with emojis 😀🎉📱";
            public const string Empty = "";
            public const string WhitespaceOnly = "   ";
        }

        /// <summary>
        /// أسماء مقدمي الخدمة
        /// </summary>
        public static class Providers
        {
            public const string YemenMobile = "YemenMobile";
            public const string SabaFon = "SabaFonTrans";
            public const string You = "YouTrans";
            public const string YGsm = "YGsm";
            public const string Invalid = "InvalidProvider";
        }

        /// <summary>
        /// إنشاء بيانات عشوائية باستخدام AutoFixture
        /// </summary>
        public static T CreateRandom<T>()
        {
            return _fixture.Create<T>();
        }

        /// <summary>
        /// إنشاء قائمة من البيانات العشوائية
        /// </summary>
        public static List<T> CreateRandomList<T>(int count = 5)
        {
            return _fixture.CreateMany<T>(count).ToList();
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف اليمني
        /// </summary>
        public static bool IsValidYemeniPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // إزالة المسافات والرموز الإضافية
            var cleanNumber = phoneNumber.Trim().Replace(" ", "").Replace("-", "");

            // التحقق من الأنماط المختلفة
            var patterns = new[]
            {
                @"^96770\d{6}$", @"^96771\d{6}$", @"^96773\d{6}$", @"^96777\d{6}$", @"^96778\d{6}$",
                @"^0096770\d{6}$", @"^0096771\d{6}$", @"^0096773\d{6}$", @"^0096777\d{6}$", @"^0096778\d{6}$",
                @"^70\d{6}$", @"^71\d{6}$", @"^73\d{6}$", @"^77\d{6}$", @"^78\d{6}$",
                @"^\+96770\d{6}$", @"^\+96771\d{6}$", @"^\+96773\d{6}$", @"^\+96777\d{6}$", @"^\+96778\d{6}$"
            };

            return patterns.Any(pattern => System.Text.RegularExpressions.Regex.IsMatch(cleanNumber, pattern));
        }

        /// <summary>
        /// الحصول على مقدم الخدمة من رقم الهاتف
        /// </summary>
        public static string GetProviderFromPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return null;

            var cleanNumber = phoneNumber.Trim().Replace(" ", "").Replace("-", "");

            if (System.Text.RegularExpressions.Regex.IsMatch(cleanNumber, @"^(96777|0096777|77|\+96777|96778|0096778|78|\+96778)"))
                return Providers.YemenMobile;

            if (System.Text.RegularExpressions.Regex.IsMatch(cleanNumber, @"^(96771|0096771|71|\+96771)"))
                return Providers.SabaFon;

            if (System.Text.RegularExpressions.Regex.IsMatch(cleanNumber, @"^(96773|0096773|73|\+96773)"))
                return Providers.You;

            if (System.Text.RegularExpressions.Regex.IsMatch(cleanNumber, @"^(96770|0096770|70|\+96770)"))
                return Providers.YGsm;

            return null;
        }
    }
}
