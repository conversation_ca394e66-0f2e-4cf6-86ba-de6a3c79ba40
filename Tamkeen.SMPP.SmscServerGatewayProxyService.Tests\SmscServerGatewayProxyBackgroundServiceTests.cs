using Serilog;
using System;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Tamkeen.SMPP.SmscServerGatewayProxyService;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;

public class SmscServerGatewayProxyBackgroundServiceTests
{
    private class TestService : SmscServerGatewayProxyBackgroundService
    {
        public bool StartCalled { get; private set; }
        public bool StopCalled { get; private set; }

        public TestService(IServiceProvider provider, SmscServerGatewayProxySettings settings, SmscServerProxy proxy)
            : base(provider, settings, proxy) { }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            StartCalled = true;
            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            StopCalled = true;
            return Task.CompletedTask;
        }

        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            return Task.CompletedTask;
        }
    }

    [Fact]
    public async Task RestartServiceAsync_InvokesStopAndStart()
    {
        var services = new ServiceCollection();
        services.AddSingleton<ILogger>(new LoggerConfiguration().CreateLogger());
        var provider = services.BuildServiceProvider();

        var settings = new SmscServerGatewayProxySettings
        {
            smscClientSettings = new SmscClientSettings(),
            smscServerSetting = new SmscServerSetting()
        };

        var proxy = (SmscServerProxy)FormatterServices.GetUninitializedObject(typeof(SmscServerProxy));

        var service = new TestService(provider, settings, proxy);

        await service.RestartServiceAsync(CancellationToken.None);

        Assert.True(service.StartCalled);
    }
}
