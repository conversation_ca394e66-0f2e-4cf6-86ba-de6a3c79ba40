using AutoFixture;
using AutoFixture.Xunit2;
using FluentAssertions;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Xunit;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Tests.Services
{
    public class SmscProxyServiceManagerTests
    {
        private readonly Fixture _fixture;
        private readonly List<Mock<ISmscServerGatewayProxyBackgroundService>> _mockServices;
        private readonly SmscProxyServiceManager _serviceManager;

        public SmscProxyServiceManagerTests()
        {
            _fixture = new Fixture();
            _mockServices = new List<Mock<ISmscServerGatewayProxyBackgroundService>>();

            // Create mock services for different providers
            var providers = new[] { "YemenMobile", "SabaFonTrans", "YouTrans", "YGsm" };
            
            foreach (var provider in providers)
            {
                var mockService = new Mock<ISmscServerGatewayProxyBackgroundService>();
                mockService.Setup(x => x.GetTransceiverSystemId()).Returns(provider);
                mockService.Setup(x => x.IsRunning()).Returns(true);
                mockService.Setup(x => x.IsSmscProxyClientWorking()).ReturnsAsync(true);
                _mockServices.Add(mockService);
            }

            _serviceManager = new SmscProxyServiceManager(_mockServices.Select(m => m.Object));
        }

        [Theory]
        [InlineData("YemenMobile")]
        [InlineData("SabaFonTrans")]
        [InlineData("YouTrans")]
        [InlineData("YGsm")]
        public void IsSmscServerProxyServiceRunning_ExistingService_ReturnsTrue(string transceiverSystemId)
        {
            // Act
            var result = _serviceManager.IsSmscServerProxyServiceRunning(transceiverSystemId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void IsSmscServerProxyServiceRunning_NonExistingService_ThrowsKeyNotFoundException()
        {
            // Arrange
            var nonExistingId = "NonExistingService";

            // Act & Assert
            _serviceManager.Invoking(x => x.IsSmscServerProxyServiceRunning(nonExistingId))
                .Should().Throw<KeyNotFoundException>()
                .WithMessage($"Service with TransceiverSystemId {nonExistingId} not found.");
        }

        [Theory]
        [AutoData]
        public async Task SendSmsAsync_ExistingService_CallsServiceSendSms(SendSmsSmscCommand command)
        {
            // Arrange
            var transceiverSystemId = "YemenMobile";
            var expectedResponse = new BaseSmscResponse
            {
                resultCode = 1,
                resultMessage = "SMS sent successfully"
            };

            var mockService = _mockServices.First(m => m.Object.GetTransceiverSystemId() == transceiverSystemId);
            mockService.Setup(x => x.SendSmsAsync(command, It.IsAny<CancellationToken>()))
                      .ReturnsAsync(expectedResponse);

            // Act
            var result = await _serviceManager.SendSmsAsync(transceiverSystemId, command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.resultCode.Should().Be(1);
            result.resultMessage.Should().Be("SMS sent successfully");
            mockService.Verify(x => x.SendSmsAsync(command, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Theory]
        [AutoData]
        public async Task SendSmsAsync_NonExistingService_ThrowsKeyNotFoundException(SendSmsSmscCommand command)
        {
            // Arrange
            var nonExistingId = "NonExistingService";

            // Act & Assert
            await _serviceManager.Invoking(x => x.SendSmsAsync(nonExistingId, command, CancellationToken.None))
                .Should().ThrowAsync<KeyNotFoundException>()
                .WithMessage($"Service with TransceiverSystemId {nonExistingId} not found.");
        }

        [Fact]
        public async Task StartServiceAsync_ExistingService_CallsServiceStart()
        {
            // Arrange
            var transceiverSystemId = "YemenMobile";
            var mockService = _mockServices.First(m => m.Object.GetTransceiverSystemId() == transceiverSystemId);

            // Act
            await _serviceManager.StartServiceAsync(transceiverSystemId, CancellationToken.None);

            // Assert
            mockService.Verify(x => x.StartAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task StopServiceAsync_ExistingService_CallsServiceStop()
        {
            // Arrange
            var transceiverSystemId = "YemenMobile";
            var mockService = _mockServices.First(m => m.Object.GetTransceiverSystemId() == transceiverSystemId);

            // Act
            await _serviceManager.StopServiceAsync(transceiverSystemId, CancellationToken.None);

            // Assert
            mockService.Verify(x => x.StopAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task RestartServiceAsync_ExistingService_CallsServiceRestart()
        {
            // Arrange
            var transceiverSystemId = "YemenMobile";
            var mockService = _mockServices.First(m => m.Object.GetTransceiverSystemId() == transceiverSystemId);

            // Act
            await _serviceManager.RestartServiceAsync(transceiverSystemId, CancellationToken.None);

            // Assert
            mockService.Verify(x => x.RestartServiceAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAllSmscProxysStatusesAsync_ReturnsAllServiceStatuses()
        {
            // Act
            var result = await _serviceManager.GetAllSmscProxysStatusesAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(4); // We have 4 mock services
            
            var statusList = result.ToList();
            statusList.Should().Contain(s => ((dynamic)s).TransceiverSystemId == "YemenMobile");
            statusList.Should().Contain(s => ((dynamic)s).TransceiverSystemId == "SabaFonTrans");
            statusList.Should().Contain(s => ((dynamic)s).TransceiverSystemId == "YouTrans");
            statusList.Should().Contain(s => ((dynamic)s).TransceiverSystemId == "YGsm");
        }

        [Theory]
        [InlineData("YemenMobile")]
        [InlineData("SabaFonTrans")]
        public async Task IsSmscProxyClientWorking_ExistingService_ReturnsServiceStatus(string transceiverSystemId)
        {
            // Arrange
            var mockService = _mockServices.First(m => m.Object.GetTransceiverSystemId() == transceiverSystemId);
            mockService.Setup(x => x.IsSmscProxyClientWorking()).ReturnsAsync(true);

            // Act
            var result = await _serviceManager.IsSmscProxyClientWorking(transceiverSystemId);

            // Assert
            result.Should().BeTrue();
            mockService.Verify(x => x.IsSmscProxyClientWorking(), Times.Once);
        }

        [Fact]
        public async Task ReInitializeSmscServerProxyClientAsync_ExistingService_CallsServiceReInitialize()
        {
            // Arrange
            var transceiverSystemId = "YemenMobile";
            var mockService = _mockServices.First(m => m.Object.GetTransceiverSystemId() == transceiverSystemId);
            mockService.Setup(x => x.ReInitializeSmscServerProxyClientAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(true);

            // Act
            var result = await _serviceManager.ReInitializeSmscServerProxyClientAsync(transceiverSystemId, CancellationToken.None);

            // Assert
            result.Should().BeTrue();
            mockService.Verify(x => x.ReInitializeSmscServerProxyClientAsync(It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
