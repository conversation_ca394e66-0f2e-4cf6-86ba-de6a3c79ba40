using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.Annotations;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Tools;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Diagnostics;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Configuration;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers
{
    /// <summary>
    /// Controller لاختبار وتشخيص مشاكل HashiCorp Vault
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class VaultTestController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<VaultTestController> _logger;

        public VaultTestController(IConfiguration configuration, ILogger<VaultTestController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// اختبار شامل للاتصال مع Vault
        /// </summary>
        [HttpGet("comprehensive")]
        [SwaggerOperation("Run comprehensive Vault connectivity test")]
        [AllowAnonymous]
        public async Task<IActionResult> RunComprehensiveTest()
        {
            try
            {
                _logger.LogInformation("Starting comprehensive Vault test via API");

                var tester = new VaultTester(_configuration, 
                    LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<VaultTester>());
                
                var result = await tester.RunComprehensiveTestAsync();

                return Ok(new
                {
                    Success = result.OverallSuccess,
                    Timestamp = DateTime.UtcNow,
                    VaultAddress = _configuration["Vault:Address"],
                    MountPoint = _configuration["Vault:MountPoint"],
                    SecretPath = _configuration["Vault:SecretPath"],
                    Results = new
                    {
                        Connection = result.ConnectionTest,
                        Health = result.HealthTest,
                        SecretRead = result.SecretReadTest,
                        Token = result.TokenTest,
                        PathTests = result.PathTests
                    },
                    Message = result.OverallSuccess ? 
                        "✅ All Vault tests passed successfully" : 
                        "❌ Some Vault tests failed. Check logs for details.",
                    Summary = result.ToString()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Comprehensive Vault test failed with exception");
                
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow,
                    Message = "❌ Vault test failed with exception"
                });
            }
        }

        /// <summary>
        /// اختبار سريع للاتصال مع Vault
        /// </summary>
        [HttpGet("quick")]
        [SwaggerOperation("Run quick Vault connectivity test")]
        [AllowAnonymous]
        public async Task<IActionResult> RunQuickTest([FromQuery] string? testPath = null)
        {
            try
            {
                _logger.LogInformation("Starting quick Vault test via API");

                var tester = new VaultTester(_configuration, 
                    LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<VaultTester>());
                
                var result = await tester.QuickTestAsync(testPath);

                return Ok(new
                {
                    Success = result,
                    Timestamp = DateTime.UtcNow,
                    TestedPath = testPath ?? _configuration["Vault:SecretPath"],
                    VaultAddress = _configuration["Vault:Address"],
                    Message = result ? 
                        "✅ Quick Vault test passed" : 
                        "❌ Quick Vault test failed"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Quick Vault test failed with exception");
                
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow,
                    Message = "❌ Quick Vault test failed with exception"
                });
            }
        }

        /// <summary>
        /// عرض تكوين Vault الحالي (بدون كشف التوكن)
        /// </summary>
        [HttpGet("config")]
        [SwaggerOperation("Get current Vault configuration")]
        [AllowAnonymous]
        public IActionResult GetVaultConfig()
        {
            try
            {
                var vaultConfig = new
                {
                    Address = _configuration["Vault:Address"],
                    MountPoint = _configuration["Vault:MountPoint"] ?? "secret",
                    SecretPath = _configuration["Vault:SecretPath"] ?? "data/tamkeen-smpp",
                    IgnoreSslErrors = bool.Parse(_configuration["Vault:IgnoreSslErrors"] ?? "false"),
                    ConnectionTimeout = _configuration["Vault:ConnectionTimeout"] ?? "30",
                    RetryAttempts = _configuration["Vault:RetryAttempts"] ?? "3",
                    HasToken = !string.IsNullOrEmpty(_configuration["Vault:Token"]),
                    TokenLength = _configuration["Vault:Token"]?.Length ?? 0,
                    Timestamp = DateTime.UtcNow
                };

                return Ok(vaultConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Vault configuration");
                
                return BadRequest(new
                {
                    Error = ex.Message,
                    Message = "❌ Failed to retrieve Vault configuration"
                });
            }
        }

        /// <summary>
        /// اختبار مسار محدد في Vault
        /// </summary>
        [HttpGet("test-path")]
        [SwaggerOperation("Test specific path in Vault")]
        [AllowAnonymous]
        public async Task<IActionResult> TestSpecificPath(
            [FromQuery] string path, 
            [FromQuery] string? mountPoint = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                {
                    return BadRequest(new
                    {
                        Error = "Path parameter is required",
                        Message = "❌ Please provide a path to test"
                    });
                }

                _logger.LogInformation("Testing specific Vault path: {Path}", path);

                var tester = new VaultTester(_configuration, 
                    LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<VaultTester>());
                
                var result = await tester.QuickTestAsync(path);

                return Ok(new
                {
                    Success = result,
                    TestedPath = path,
                    MountPoint = mountPoint ?? _configuration["Vault:MountPoint"] ?? "secret",
                    VaultAddress = _configuration["Vault:Address"],
                    Timestamp = DateTime.UtcNow,
                    Message = result ? 
                        $"✅ Path '{path}' is accessible and contains secrets" : 
                        $"❌ Path '{path}' is not accessible or empty"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to test specific Vault path: {Path}", path);
                
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    TestedPath = path,
                    Timestamp = DateTime.UtcNow,
                    Message = $"❌ Failed to test path '{path}'"
                });
            }
        }

        /// <summary>
        /// تشخيص شامل لمشاكل بدء التشغيل
        /// </summary>
        [HttpGet("startup-diagnostics")]
        [SwaggerOperation("Run comprehensive startup diagnostics")]
        [AllowAnonymous]
        public async Task<IActionResult> RunStartupDiagnostics()
        {
            try
            {
                _logger.LogInformation("Starting comprehensive startup diagnostics");

                var diagnostics = new StartupDiagnostics(_configuration,
                    LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<StartupDiagnostics>());

                var result = await diagnostics.RunFullDiagnosticsAsync();

                return Ok(new
                {
                    Success = result.OverallHealth == HealthStatus.Healthy,
                    OverallHealth = result.OverallHealth.ToString(),
                    Timestamp = result.Timestamp,
                    Results = new
                    {
                        Configuration = result.ConfigurationCheck.ToString(),
                        Network = result.NetworkCheck.ToString(),
                        ExternalServices = result.ExternalServicesCheck.ToString(),
                        Environment = result.EnvironmentCheck.ToString(),
                        FileSystem = result.FileSystemCheck.ToString()
                    },
                    Errors = result.Errors,
                    Recommendations = GetRecommendations(result),
                    Message = result.OverallHealth switch
                    {
                        HealthStatus.Healthy => "✅ All startup diagnostics passed",
                        HealthStatus.Degraded => "⚠️ Some issues found but application can start",
                        HealthStatus.Critical => "❌ Critical issues found that may prevent startup",
                        _ => "❓ Unknown health status"
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Startup diagnostics failed with exception");

                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow,
                    Message = "❌ Startup diagnostics failed with exception"
                });
            }
        }

        /// <summary>
        /// التحقق من صحة التكوين
        /// </summary>
        [HttpGet("validate-configuration")]
        [SwaggerOperation("Validate application configuration")]
        [AllowAnonymous]
        public IActionResult ValidateConfiguration()
        {
            try
            {
                _logger.LogInformation("Starting configuration validation");

                var result = SafeConfigurationBinder.ValidateConfiguration(_configuration, _logger);

                return Ok(new
                {
                    Success = result.IsValid,
                    Timestamp = result.Timestamp,
                    Errors = result.Errors,
                    Warnings = result.Warnings,
                    Summary = result.ToString(),
                    Message = result.IsValid ?
                        "✅ Configuration validation passed" :
                        "❌ Configuration validation failed"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Configuration validation failed with exception");

                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow,
                    Message = "❌ Configuration validation failed with exception"
                });
            }
        }

        /// <summary>
        /// محاكاة curl command للمقارنة
        /// </summary>
        [HttpGet("simulate-curl")]
        [SwaggerOperation("Simulate curl command for comparison")]
        [AllowAnonymous]
        public IActionResult SimulateCurl()
        {
            try
            {
                var vaultAddress = _configuration["Vault:Address"];
                var mountPoint = _configuration["Vault:MountPoint"] ?? "secret";
                var secretPath = _configuration["Vault:SecretPath"] ?? "data/tamkeen-smpp";
                var token = _configuration["Vault:Token"];

                // تنظيف المسار لمحاكاة curl
                var cleanPath = secretPath?.TrimStart('/');

                var curlCommand = $"curl -H \"X-Vault-Token:{token}\" {vaultAddress}/v1/{mountPoint}/data/{cleanPath}";
                var curlCommandSafe = $"curl -H \"X-Vault-Token:***\" {vaultAddress}/v1/{mountPoint}/data/{cleanPath}";

                return Ok(new
                {
                    CurlCommand = curlCommandSafe,
                    FullUrl = $"{vaultAddress}/v1/{mountPoint}/data/{cleanPath}",
                    VaultAddress = vaultAddress,
                    MountPoint = mountPoint,
                    SecretPath = secretPath,
                    CleanedPath = cleanPath,
                    HasToken = !string.IsNullOrEmpty(token),
                    Instructions = new[]
                    {
                        "1. Copy the curl command above",
                        "2. Replace *** with your actual Vault token",
                        "3. Run the command in terminal to test manually",
                        "4. Compare results with API test results"
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate curl simulation");

                return BadRequest(new
                {
                    Error = ex.Message,
                    Message = "❌ Failed to generate curl command simulation"
                });
            }
        }

        /// <summary>
        /// الحصول على توصيات بناءً على نتائج التشخيص
        /// </summary>
        private static string[] GetRecommendations(DiagnosticResult result)
        {
            var recommendations = new List<string>();

            if (result.ConfigurationCheck != HealthStatus.Healthy)
            {
                recommendations.Add("Check configuration files for missing or invalid values");
                recommendations.Add("Verify Vault connection settings");
                recommendations.Add("Ensure all required sections exist in appsettings.json");
            }

            if (result.NetworkCheck != HealthStatus.Healthy)
            {
                recommendations.Add("Check internet connectivity");
                recommendations.Add("Verify DNS resolution for external services");
                recommendations.Add("Check firewall settings for outbound connections");
            }

            if (result.ExternalServicesCheck != HealthStatus.Healthy)
            {
                recommendations.Add("Verify external service endpoints are accessible");
                recommendations.Add("Check service credentials and authentication");
                recommendations.Add("Ensure services are running and healthy");
            }

            if (result.EnvironmentCheck != HealthStatus.Healthy)
            {
                recommendations.Add("Set required environment variables");
                recommendations.Add("Verify ASPNETCORE_ENVIRONMENT is set correctly");
            }

            if (result.FileSystemCheck != HealthStatus.Healthy)
            {
                recommendations.Add("Check file system permissions");
                recommendations.Add("Ensure log directories are writable");
                recommendations.Add("Verify disk space availability");
            }

            if (recommendations.Count == 0)
            {
                recommendations.Add("All checks passed - no recommendations needed");
            }

            return recommendations.ToArray();
        }
    }
}
