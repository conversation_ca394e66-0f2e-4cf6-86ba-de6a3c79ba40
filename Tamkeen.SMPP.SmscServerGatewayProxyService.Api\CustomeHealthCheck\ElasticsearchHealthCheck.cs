﻿using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using Microsoft.IdentityModel.Tokens;
using static Org.BouncyCastle.Math.EC.ECCurve;
using System.Text;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck
{
    public class ElasticsearchHealthCheck : IHealthCheck
    {
        private readonly TamkeenLoggingConfig _config;
        private readonly HttpClient _httpClient;

        public ElasticsearchHealthCheck(TamkeenLoggingConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));

            var handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => true
            };

            _httpClient = new HttpClient(handler);
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            if (!_config.UseElasticsearch || string.IsNullOrEmpty(_config.ElasticsearchUrl))
            {
                return HealthCheckResult.Unhealthy("Elasticsearch is not enabled or URL is missing in configuration.");
            }

            try
            {
                if (!_config.ElasticSearchApiKey.IsNullOrEmpty())
                {
                    _httpClient.DefaultRequestHeaders.Add("Authorization", $"ApiKey {_config.ElasticSearchApiKey}");
                }
                else if (!_config.ElasticsearchUsername.IsNullOrEmpty())
                {
                    string credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_config.ElasticsearchUsername}:{_config.ElasticsearchPassword}"));
                    _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");
                }
                var response = await _httpClient.GetAsync($"{_config.ElasticsearchUrl}/_cluster/health", cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    return HealthCheckResult.Healthy("Elasticsearch is running.");
                }

                return HealthCheckResult.Unhealthy($"Elasticsearch returned a non-success status: {response.StatusCode}");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy($"Error connecting to Elasticsearch: {ex.Message}");
            }
        }
    }
}
