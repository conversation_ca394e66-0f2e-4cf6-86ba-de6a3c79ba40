﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.OtherSmscServer;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public class SmscServerBackgroundService : BackgroundService, ISmscServerBackgroundService
    {
        private bool _isRunning;
        private readonly IServiceProvider _serviceProvider;
        public OtherSmppSmscServer _otherSmscServer;
        private readonly ILogger _logger;
        //private CancellationTokenSource _cancellationTokenSource; // Instance-specific CancellationTokenSource
        private readonly OtherSmscServerSettings _smscServerSetting;

        public SmscServerBackgroundService(IServiceProvider serviceProvider,
            OtherSmscServerSettings smscServerSettings)
        {
            _smscServerSetting = smscServerSettings;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
            _otherSmscServer = new OtherSmppSmscServer(_serviceProvider, _smscServerSetting);
            //_cancellationTokenSource = new CancellationTokenSource(); // Initialize a new CancellationTokenSource
        }
        //public SmscServerGatewayProxyBackgroundService(IServiceProvider serviceProvider,
        //    IOptions<List<SmscServerGatewayProxySettings>> smscServerGatewayProxySettings)
        //{
        //    _transceiverSystemId = smscServerGatewayProxySettings.Value.FirstOrDefault(s => s.smscClientSettings.transceiverSystemId == "YemenMobile").smscClientSettings.transceiverSystemId;
        //    _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        //    _smscServerProxy = new SmscServerProxy(serviceProvider, smscServerGatewayProxySettings.Value.FirstOrDefault(s => s.smscClientSettings.transceiverSystemId == "YemenMobile"));
        //    _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
        //}
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _isRunning = true;
                _logger.Information("Background service started");
                //await _smscServerProxy.Run(); // Run SmscServerProxy continuously
                //await Task.Delay(TimeSpan.FromSeconds(60), stoppingToken);
                //_cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken); // Link the tokens

                // Ensure that the token used here is the linked token source's token
                _ = Task.Run(() => StartSmscServer(stoppingToken), stoppingToken);
                //if (!stoppingToken.IsCancellationRequested)
                //{
                //    _cancellationTokenSource = new CancellationTokenSource();
                //}
                //else
                //{
                //    await _cancellationTokenSource.CancelAsync();
                //}
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Information($"Error in ExecuteAsync: {ex.Message}");
            }
            //finally
            //{
            //    //_isRunning = false;
            //    //firstRun = false;
            //}
        }

        //// Stop the background service
        //public void StopService()
        //{
        //    _logger.Information("Stopping Background Smsc Server Proxy Service.", "StopService", _smscServerProxy._smscServerProxyClient._clientConnectionSettings.systemId);
        //    _cancellationTokenSource.Cancel();
        //}

        // Restart the background service
        //public async void RestartService()
        //{
        //    _logger.Information("Restarting Background Smsc Server Proxy Service.", "RestartService", _smscServerProxy._smscServerProxyClient._clientConnectionSettings.systemId);
        //    await ClientDisconnect();
        //    await _smscServerProxy.StopAsync();
        //    // Cancel the current task
        //    _cancellationTokenSource.Cancel();

        //    // Reset the cancellation token source
        //    _cancellationTokenSource = new CancellationTokenSource();

        //    // Start the service again
        //    _ = ExecuteAsync(_cancellationTokenSource.Token);
        //}
        public async Task RestartServiceAsync(CancellationToken cancellationToken)
        {
            if (_otherSmscServer!=null)
            {
                _logger.Information("Restarting Background Smsc Server Proxy Service.", "RestartService", _smscServerSetting.SmscServerName);

                await StopAsync(cancellationToken); // Use StopAsync to ensure proper shutdown
                
                await StartAsync(cancellationToken); // Start the service again 
            }
            else
            {
                await StartAsync(cancellationToken);
            }
        }
        private async Task StartSmscServer(CancellationToken stoppingToken)
        {
            bool isStartedToRun = false;
            int retryCount = 0; // Track the number of retries
            const int maxRetries = 10; // Maximum number of retries before reinitializing
            try
            {
                _isRunning = true;
                
                _logger.Information($"Attempting to run client: {_smscServerSetting.SmscServerName}");

                while (!stoppingToken.IsCancellationRequested)
                {
                    // Add timeout for the Run task
                    if (_otherSmscServer!=null && !isStartedToRun)
                    {
                        isStartedToRun = true;
                        if (!_otherSmscServer._otherSmscServer.IsRun)
                        {
                            _logger.Information("Starting _smscServerProxy.Run()");
                            var runTask = _otherSmscServer.Run();

                            // Wait for the task to complete or timeout, while respecting the cancellation token
                            if (await Task.WhenAny(runTask, Task.Delay(TimeSpan.FromSeconds(30), stoppingToken)) == runTask)
                            {
                                _logger.Information("Run completed.");
                            }
                            else
                            {
                                _logger.Information("Run timed out.");
                            }
                        }

                        isStartedToRun = false;
                    }
                    else
                    {
                        isStartedToRun = false;
                        return;
                    }

                    // Wait for 5 seconds or exit early if the token is canceled
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.Information($"StartSmscServer was {_smscServerSetting.SmscServerName} canceled by cancellation token.");
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Error($"Error in StartSmscServer {_smscServerSetting.SmscServerName}: {ex.Message}");
            }
            finally
            {
                isStartedToRun = false;
                _isRunning = false;
                _logger.Information($"StartSmscServer {_smscServerSetting.SmscServerName} has exited.");
                //_cancellationTokenSource = new CancellationTokenSource();
            }
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            cancellationToken = new CancellationTokenSource().Token;
            //_cancellationTokenSource = new CancellationTokenSource(); // Reset the CancellationTokenSource on Start
            if (_otherSmscServer == null)
            {
                _otherSmscServer = new OtherSmppSmscServer(_serviceProvider, _smscServerSetting);
            }
            _logger.Information("Starting Background Smsc Server Proxy Service.", "StartAsync", _smscServerSetting.SmscServerName);
            // Start ExecuteAsync in a new task and do not await it here
            if (!_otherSmscServer._otherSmscServer.IsRun)
            {
                _ = Task.Run(() => ExecuteAsync(cancellationToken)); 
            }
            // Optionally, you can add a small delay to ensure that ExecuteAsync has started
            await Task.Delay(1000, cancellationToken);
        }

        //// Get the last run time
        //public DateTime GetLastRunTime()
        //{
        //    return _lastRunTime.ToLocalTime();
        //}

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            try
            {
                if (_otherSmscServer!=null)
                {
                    _logger.Information("Background Smsc Server Proxy Service is stopping.", "StopAsync", _smscServerSetting.SmscServerName);

                    // Signal cancellation to the running loop in StartSmscServerClient
                    //_cancellationTokenSource.Cancel();

                    // Wait for the base class to stop and perform any additional cleanup
                    await base.StopAsync(cancellationToken);

                    if (_otherSmscServer._otherSmscServer.IsRun)
                    {
                        await _otherSmscServer._otherSmscServer.StopAsync();
                    }
                    _otherSmscServer.Dispose();
                    _otherSmscServer = null!;
                    _logger.Information($"Background Smsc Server Proxy Service for {_smscServerSetting.SmscServerName} stopped."); 
                }
                else
                {
                    _logger.Information($"Background Smsc Server Proxy Service for {_smscServerSetting.SmscServerName} is already stopped.");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in StopAsync: {ex.Message}");
            }
        }

        public bool IsRunning()
        {
            return _isRunning;
        }

        
        //public async Task<bool> IsSmscProxyWorking()
        //{
        //    if (_otherSmscServer!=null)
        //    {
        //        if (_otherSmscServer._otherSmscServer.IsRun)
        //        {
        //            if (!_isRunning)
        //            {
        //                await StartAsync(new CancellationToken());
        //            }
        //            return true;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}

        //public async Task<IBaseSmscResponse> SendSmsAsync(SendSmsSmscCommand sendSmsSmscCommand, CancellationToken cancellationToken)
        //{
        //    if (_otherSmscServer != null)
        //    {
        //        if (_otherSmscServer._smppProxyServer.IsRun && _otherSmscServer._smscServerProxyClient._proxyClient.Status == ConnectionStatus.Bound)
        //        {
        //            if (!_isRunning)
        //            {
        //                await StartAsync(new CancellationToken());
        //            }
        //            return await _otherSmscServer._otherSmscServer.SendMessageAsync(sendSmsSmscCommand);
        //        }
        //        else
        //        {
        //            return await SmscGeneralUtilities.MakeResponse(-1, "The SMSC server or the client is down");               
        //        }
        //    }
        //    else
        //    {
        //        return await SmscGeneralUtilities.MakeResponse(-1, "The SMSC server is not working");
        //    }
        //}
    }
}