﻿using System;
using System.Collections.Concurrent;
using System.Text;
using System.Threading.Tasks;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

public delegate Task RabbitMQMessageReceivedDelegate(string message);

public class RabbitMQMessageBridge
{
    private readonly IConnection _connection;
    private readonly IModel _channel;
    private readonly ConcurrentDictionary<string, string> _messageStore = new ConcurrentDictionary<string, string>();

    public event RabbitMQMessageReceivedDelegate MessageReceived;

    public RabbitMQMessageBridge(string hostname, string queueName)
    {
        var factory = new ConnectionFactory() { HostName = hostname };
        _connection = factory.CreateConnection();
        _channel = _connection.CreateModel();
        _channel.QueueDeclare(queueName, false, false, false, null);

        var consumer = new EventingBasicConsumer(_channel);
        consumer.Received += (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            _messageStore.TryAdd(ea.BasicProperties.MessageId, message);
            OnMessageReceived(message);
        };

        _channel.BasicConsume(queue: queueName, autoAck: true, consumer: consumer);
    }

    public void SendMessage(string queueName, string message, string messageId)
    {
        var body = Encoding.UTF8.GetBytes(message);
        var properties = _channel.CreateBasicProperties();
        properties.MessageId = messageId;

        _channel.BasicPublish(exchange: "", routingKey: queueName, basicProperties: properties, body: body);
        _messageStore.TryAdd(messageId, message);
    }

    private void OnMessageReceived(string message)
    {
        var handler = MessageReceived;
        if (handler != null)
        {
            handler(message);
        }
    }

    public void Dispose()
    {
        _channel?.Close();
        _connection?.Close();
    }
}
