﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class SmscErrorMessages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SmscErrorMessages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager.SmscErrorMessages", typeof(SmscErrorMessages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exception Received: {0} {1}.
        /// </summary>
        internal static string ExceptionReceived {
            get {
                return ResourceManager.GetString("ExceptionReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to forward DeliverSm :.
        /// </summary>
        internal static string FailedForwardDeliverSm {
            get {
                return ResourceManager.GetString("FailedForwardDeliverSm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No matching client found for SystemID: {0}.
        /// </summary>
        internal static string NoMatchingClient {
            get {
                return ResourceManager.GetString("NoMatchingClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receipt received failed.
        /// </summary>
        internal static string ReceiptReceivedFailed {
            get {
                return ResourceManager.GetString("ReceiptReceivedFailed", resourceCulture);
            }
        }
    }
}
