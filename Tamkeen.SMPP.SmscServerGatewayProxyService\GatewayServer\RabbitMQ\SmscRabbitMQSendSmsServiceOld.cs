﻿using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using RabbitMQ.Client.Exceptions;
using Serilog;
using System.Data;
using System.Globalization;
using System.Net.Mail;
using System.Text;
using System.Threading.Channels;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Builders;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ
{
    /// <summary>
    /// Service for handling SMS sending via RabbitMQ in version 2.
    /// This service is responsible for managing the queue and processing unacknowledged messages.
    /// </summary>
    public class SmscRabbitMQSendSmsServiceOld : IDisposable
    {
        private AsyncEventingBasicConsumer _eventingBasicConsumer { get; set; }
        public IChannel _channel;
        private readonly ILogger _logger;
        public readonly string _queueName;
        //private bool _isConsuming;
        private readonly IServiceProvider _serviceProvider;
        public SmscServerProxyClient _smppClient;
        //private readonly Lazy<SendSmsSmscCommandHandler> _sendSmsSmscCommandHandler;
        /// <summary>
        /// Initializes a new instance of the <see cref="SmscRabbitMQSendSmsServiceOld"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve dependencies.</param>
        /// <param name="queueName">The name of the RabbitMQ queue to consume from.</param>
        public SmscRabbitMQSendSmsServiceOld(IServiceProvider serviceProvider, string queueName)
        {
            _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(ILogger));
            // Lazy resolution of SendSmsSmscCommandHandler
            //_sendSmsSmscCommandHandler = new Lazy<SendSmsSmscCommandHandler>(() =>
            //    serviceProvider.GetRequiredService<SendSmsSmscCommandHandler>()
            //);
            _queueName = queueName;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            InitializeChannel();
        }

        /// <summary>
        /// Initializes the RabbitMQ channel and configures the queue.
        /// </summary>
        public async Task<bool> InitializeChannel()
        {
            try
            {
                if (_serviceProvider == null) throw new ArgumentNullException(nameof(_serviceProvider));

                var smscRabbitMQGeneral = _serviceProvider.GetService<SmscRabbitMQGeneral>();
                if (smscRabbitMQGeneral == null) throw new InvalidOperationException("SmscRabbitMQGeneral is not registered.");

                if (await smscRabbitMQGeneral.EnsureChannelIsOpen())
                {
                    _channel = smscRabbitMQGeneral._channel ?? throw new InvalidOperationException("Channel is not initialized.");
                    await SetupChannel();
                    return true;
                }
                throw new InvalidOperationException("Failed to open RabbitMQ channel.");
            }
            catch (Exception ex)
            {
                _logger.Error($"InitializeChannel failed: {ex.Message}");
                return false;
            }
        }

        private async Task SetupChannel()
        {
            if (_channel == null)
                throw new InvalidOperationException("Channel is not initialized.");

            await _channel.QueueDeclareAsync(queue: _queueName, durable: true, exclusive: false, autoDelete: false, arguments: null);
            await _channel.BasicQosAsync(prefetchSize: 0, prefetchCount: 1, global: false);

            _eventingBasicConsumer = new AsyncEventingBasicConsumer(_channel);
            await _channel.BasicConsumeAsync(queue: _queueName, autoAck: false, consumer: _eventingBasicConsumer);

            //_eventingBasicConsumer.ReceivedAsync += async (model, ea) =>
            //{
            //    try
            //    {
            //        var body = ea.Body.ToArray();
            //        var message = Encoding.UTF8.GetString(body);
            //        _logger.Information("Received message: {0}", message);

            //        await ProcessMessageAsync(message);

            //        _channel?.BasicAckAsync(deliveryTag: ea.DeliveryTag, multiple: false);
            //    }
            //    catch (Exception ex)
            //    {
            //        _logger.Error("Error processing message: {0}", ex.Message);
            //        _channel?.BasicNackAsync(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
            //    }
            //};
        }
        //private async Task ProcessMessageAsync(string message)
        //{
        //    try
        //    {
        //        var smsCommand = JsonConvert.DeserializeObject<SmscRabbitMQSaveSMSCommand>(message);
        //        if (smsCommand == null)
        //            throw new InvalidOperationException("Failed to deserialize the SMS command.");

        //        // Process the SMS message here. Add business logic as needed.
        //        _logger.Information("Processing SMS message: {0}", smsCommand.smsText);

        //        await Task.CompletedTask; // Replace with actual processing logic.
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error("Error in ProcessMessageAsync: {0}", ex.Message);
        //        throw;
        //    }
        //}


        /// <summary>
        /// Reinitializes the RabbitMQ channel if it is not open.
        /// This ensures the channel is ready for message processing.
        /// </summary>
        public async Task<bool> ReinitializeChannelIfNeeded()
        {
            try
            {
                if (_channel == null || !_channel.IsOpen)
                {
                    // Dispose of the old channel if it exists
                    DisposeChannel();

                    // Recreate the channel and consumer
                    return await InitializeChannel();

                    //_logger.Information("RabbitMQ channel reinitialized successfully.");
                }
                else
                {
                    return true;
                }
            }
            catch (BrokerUnreachableException ex)
            {
                _logger.Error("RabbitMQ broker is unreachable. Exception: {0}", ex.Message);
                return false;
                // Consider implementing a retry mechanism or backoff strategy here
            }
            catch (IOException ex)
            {
                _logger.Error("I/O error occurred while reinitializing the RabbitMQ channel. Exception: {0}", ex.Message);
                return false;
                // Handle specific I/O exceptions if necessary
            }
            catch (AlreadyClosedException ex)
            {
                _logger.Warning("RabbitMQ channel was already closed. Exception: {0}", ex.Message);
                // The channel was already closed, consider reinitializing after a delay
                Thread.Sleep(5000);  // Adding a delay before retrying could help in some cases
                return await ReinitializeChannelIfNeeded();  // Retry the operation
            }
            catch (Exception ex)
            {
                _logger.Error("An unexpected error occurred while reinitializing the RabbitMQ channel. Exception: {0}", ex.Message);
                return false;
                // Log and handle unexpected exceptions
            }
        }

        private void DisposeChannel()
        {
            try
            {
                // Ensure the _channel is not null and is open before attempting to close or dispose
                if (_channel != null)
                {
                    if (_channel.IsOpen)
                    {
                        _channel.CloseAsync();
                    }
                    _channel.Dispose();
                }
                _channel = null;
            }
            catch (NullReferenceException ex)
            {
                // Handle the case where the _channel or its internals were null
                _logger.Warning("Attempted to dispose a null or partially disposed RabbitMQ channel. Exception: {0}", ex.Message);
            }
            catch (AlreadyClosedException ex)
            {
                // If the channel was already closed, it's usually safe to ignore this
                _logger.Warning("RabbitMQ channel was already closed. Exception: {0}", ex.Message);
            }
            catch (Exception ex)
            {
                // Catch-all for any other exceptions
                _logger.Warning("An unexpected error occurred while disposing of the RabbitMQ channel. Exception: {0}", ex.Message);
            }
            finally
            {
                // Ensure that _channel is set to null, even if an exception occurred
                _channel = null!;
            }
        }



        /// <summary>
        /// Sends unacknowledged SMS messages that are in the queue.
        /// This method checks the queue for messages that were not previously acknowledged.
        /// </summary>
        public async Task SendUnackSMS()
        {
            if (await ReinitializeChannelIfNeeded())
            {
                try
                {
                    var messageCount = await _channel.MessageCountAsync(_queueName);
                    for (ulong i = 0; i < messageCount; i++)
                    {
                        // Check if the channel is open
                        if (await ReinitializeChannelIfNeeded())
                        {
                            var ea = await _channel.BasicGetAsync(_queueName, autoAck: false);
                            if (ea != null)
                            {
                                var body = ea.Body.ToArray();
                                var message = Encoding.UTF8.GetString(body);
                                _logger.Information("Received message from RabbitMQ: {0}", message);
                                SmscRabbitMQSaveSMSCommand smscSMSMessage = JsonConvert.DeserializeObject<SmscRabbitMQSaveSMSCommand>(message);
                                if (_smppClient._proxyClient.Status == ConnectionStatus.Bound)
                                {
                                    #region SendSMS
                                    // Create source and destination addresses for the SMS
                                    var sourceAddress = new SmeAddress(smscSMSMessage.srcAdr, smscSMSMessage.srcTon, smscSMSMessage.srcNpi);
                                    var destinationAddress = new SmeAddress(smscSMSMessage.dstAdr, smscSMSMessage.dstTon, smscSMSMessage.dstNpi);

                                    // Construct the SubmitSmBuilder for the SMS message
                                    var builder = Inetlab.SMPP.SMS.ForSubmit()
                                        .From(sourceAddress)
                                        .To(destinationAddress)
                                        .Coding(smscSMSMessage.dataCodings)
                                        .Text(smscSMSMessage.smsText)
                                        .ExpireIn(TimeSpan.FromDays(2));

                                    if (smscSMSMessage.deliveryReceipt != SMSCDeliveryReceipt.NotRequested)
                                    {
                                        builder.DeliveryReceipt(smscSMSMessage.deliveryReceipt);
                                    }

                                    switch (smscSMSMessage.submitMode)
                                    {
                                        case SubmitMode.Payload:
                                            builder.MessageInPayload();
                                            break;
                                        case SubmitMode.ShortMessageWithSAR:
                                            builder.Concatenation(ConcatenationType.SAR);
                                            break;
                                    }
                                    if (_smppClient._clientConnectionSettings.optionalParameterList != null)
                                    {
                                        byte[] bytesarr = Encoding.UTF8.GetBytes(_smppClient._clientConnectionSettings.optionalParameterList.value);
                                        // Assuming _clientConnectionSettings.optionalParameterList.tag is a string
                                        if (ushort.TryParse(_smppClient._clientConnectionSettings.optionalParameterList.tag, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort))
                                        {
                                            builder.AddParameter(tagAsUshort, bytesarr);
                                        }
                                    }
                                    //SubmitSm submitSm = new SubmitSm
                                    //{
                                    //    ServiceType = builder.ServiceType(message),
                                    //    SourceAddress = sourceAddress,
                                    //    DestinationAddress = builder.DestinationAddress,
                                    //    DataCoding = builder.DataCoding,
                                    //    ScheduledDeliveryTime = builder.ScheduledDeliveryTime,
                                    //    ValidityPeriod = builder.ValidityPeriod,
                                    //    RegisteredDelivery = builder.RegisteredDelivery,
                                    //    ProtocolId = builder.ProtocolId,
                                    //    // Add other necessary properties if needed
                                    //};
                                    // Create an instance of SubmitSm using the builder
                                    //SubmitSm submitSm = new SubmitSm();
                                    //submitSm.
                                    //var submitSmList = new List<SubmitSm>();
                                    //submitSmList.Add(builder);
                                    //// Submit the SMS via the appropriate smppClient and get the response
                                    //var response = await _smppClient._proxyClient.SubmitWithRepeatAsync(submitSmList, TimeSpan.FromSeconds(5));
                                    // Use the builder to create an array of SubmitSm instances
                                    SubmitSm[] submitSmArray = builder.Create(_smppClient._proxyClient);
                                    // Iterate over the array to forward each SubmitSm
                                    //SubmitSmResp submitSmResp;
                                    //foreach (var submitSm in submitSmArray)
                                    //{
                                    //    // Reset the sequence number before forwarding
                                    //    submitSm.Header.Sequence = 0;
                                    //    submitSm.Response = new SubmitSmResp()
                                    //    {
                                    //        MessageId = smscSMSMessage.MessageId,
                                    //    };
                                    //    // Call the ForwardSubmitSm method
                                    //    //submitSmResp = await _smppClient.ForwardSubmitSmFromRabbitMQ(_smppClient._proxyServerClient, submitSm);
                                    //}
                                    //submitSmResp = await _smppClient.ForwardSubmitSmFromRabbitMQ(_smppClient._proxyServerClient, submitSmArray.FirstOrDefault());
                                    //if (submitSmResp.Header.Status == CommandStatus.SMPPCLIENT_NOCONN)
                                    //{
                                    //    await ProcessInteraptedSubmitSmAsync(smscSMSMessage);
                                    //}
                                    //else if (submitSmResp.Header.Status == CommandStatus.ESME_ROK)
                                    //{
                                    //    // Acknowledge message after successful processing and acknowledge it.
                                    //    _channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                                    //}
                                    //else
                                    //{
                                    //    // If processing failed, requeue the message for later processing
                                    //    _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
                                    //}
                                    #endregion
                                    // Now pass this array directly to SubmitWithRepeatAsync
                                    var response = await _smppClient._proxyClient.SubmitWithRepeatAsync(submitSmArray, TimeSpan.FromSeconds(1));

                                    //var response = await _smppClient._proxyClient.SubmitAsync(builder);
                                    if (response.All(x => x.Header.Status == CommandStatus.SMPPCLIENT_NOCONN || x.Header.Status == CommandStatus.SMPPCLIENT_UNBOUND))
                                    {
                                        await ProcessInteraptedSubmitSmAsync(smscSMSMessage);
                                    }
                                    //await handler.Handle(sendSmsSmscCommand, new CancellationToken());
                                    else if (response.All(x => x.Header.Status == CommandStatus.ESME_ROK))
                                    {
                                        // Acknowledge message after successful processing and acknowledge it.
                                        await _channel.BasicAckAsync(deliveryTag: ea.DeliveryTag, multiple: false);
                                    }
                                    else
                                    {
                                        // If processing failed, requeue the message for later processing
                                        await _channel.BasicNackAsync(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
                                    }
                                }
                                else
                                {
                                    // If the client is not connected, requeue the message for later processing
                                    await _channel.BasicNackAsync(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
                                    await Task.Delay(_smppClient._proxyClient.ConnectionRecoveryDelay).ConfigureAwait(false);
                                }
                            }
                        }
                    }

                    _logger.Information("Unacknowledged messages re-sent successfully.");
                }
                catch (Exception ex)
                {
                    _logger.Error("Error on re-sending unacknowledged messages: {0}", ex.Message);
                }
            }
        }

        /// <summary>
        /// Disposes the resources used by the RabbitMQ service.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes managed resources.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources; otherwise, false.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // Ensure that the channel is still open before attempting to close it
                    if (_channel != null && _channel.IsOpen)
                    {
                        _eventingBasicConsumer?.Channel?.CloseAsync(); // Close the channel associated with the consumer
                        _eventingBasicConsumer?.Channel?.Dispose();
                        _eventingBasicConsumer = null;

                        //_channel.Close();
                        //_channel.Dispose();
                        _channel = null;
                    }
                }
                catch (ObjectDisposedException ex)
                {
                    // Log the exception or handle it as necessary
                    _logger?.Error(ex, "Attempted to dispose a channel that was already disposed.");
                }
                catch (Exception ex)
                {
                    // Log any other unexpected exceptions
                    _logger?.Error(ex, "An unexpected error occurred while disposing the RabbitMQ channel.");
                }
            }
        }




        // Destructor (finalizer)
        ~SmscRabbitMQSendSmsServiceOld()
        {
            Dispose(false);
        }
        //public SmscRabbitMQSendSmsServiceVersion2(IServiceProvider serviceProvider/*SmscRabbitMQGeneral smscRabbitMQGeneral*/, string queueName)
        //{
        //    //_serviceProvider = serviceProvider;
        //    //_queueName = queueName;            
        //    if (_channel != null)
        //    {
        //        _channel.Dispose();
        //    }
        //    //var smscRabbitMQGeneral = serviceProvider.GetService<SmscRabbitMQGeneral>();
        //    //_channel = smscRabbitMQGeneral._channel;
        //    _logger = serviceProvider.GetService<Serilog.ILogger>();
        //    _queueName = queueName;
        //    _mediator = serviceProvider.GetService<IMediator>();
        //    //_channel.QueueDeclare(queue: _queueName, durable: false, exclusive: false, autoDelete: false, arguments: null);
        //    //_channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);
        //    //_eventingBasicConsumer = new EventingBasicConsumer(_channel);
        //    //_channel.BasicConsume(queue: _queueName, autoAck: false, consumer: _eventingBasicConsumer);
        //    _serviceProvider = serviceProvider;
        //    //StartAsync(CancellationToken.None);
        //    //_smscSmppClient = new GetSmscSmppClient(serviceProvider);
        //    //Task.Run(() => SendUnackSMS());
        //    InitializeChannel();
        //}
        //private void InitializeChannel()
        //{
        //    var smscRabbitMQGeneral = _serviceProvider.GetService<SmscRabbitMQGeneral>();
        //    _channel = smscRabbitMQGeneral._channel;

        //    _channel.QueueDeclare(queue: _queueName, durable: false, exclusive: false, autoDelete: false, arguments: null);
        //    _channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);

        //    _eventingBasicConsumer = new EventingBasicConsumer(_channel);
        //    _channel.BasicConsume(queue: _queueName, autoAck: false, consumer: _eventingBasicConsumer);
        //}
        //public void ReinitializeChannelIfNeeded()
        //{
        //    if (_channel == null || !_channel.IsOpen)
        //    {
        //        InitializeChannel();
        //    }
        //}
        //public void StartSendSms(SmppClient smppClient)
        //{
        //    _smppClient = smppClient;
        //    _isConsuming = true;
        //}

        //private async Task<bool> StartConsuming()
        //{
        //    try
        //    {
        //        _logger.Debug("Starting sending SMS's from queue");

        //        _eventingBasicConsumer.Received += async (model, ea) =>
        //        {
        //            try
        //            {
        //                GetSmscSmppClient getSmscSmppClient = new GetSmscSmppClient(_serviceProvider);
        //                SmppClient smppClient = getSmscSmppClient.GetClient(_queueName);
        //                if (smppClient.Status == ConnectionStatus.Bound)
        //                {
        //                    var body = ea.Body.ToArray();
        //                    var message = Encoding.UTF8.GetString(body);
        //                    _logger.Information("Received message from RabbitMQ: {0}", message);
        //                    var sendSmsSmscCommand = JsonConvert.DeserializeObject<SendSmsSmscCommand>(message);

        //                    var response = await _mediator.Send(sendSmsSmscCommand);
        //                    if (response.resultCode == 1)
        //                    {
        //                        // Acknowledge message after successful processing
        //                        _channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
        //                    }
        //                    else
        //                    {
        //                        // If processing failed, requeue the message for later processing
        //                        _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
        //                        await Task.Delay(smppClient.ConnectionRecoveryDelay).ConfigureAwait(false);
        //                    }
        //                }
        //                else
        //                {
        //                    // If processing failed, requeue the message for later processing
        //                    _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
        //                    await Task.Delay(smppClient.ConnectionRecoveryDelay).ConfigureAwait(false);
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                // Handle exceptions gracefully
        //                _logger.Error("Error processing message from RabbitMQ: {0}", ex.Message);

        //                // Reject (Nack) message to be requeued or dead-lettered
        //                _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
        //            }
        //        };
        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error("Error on sending SMS's from queue from RabbitMQ: {0}", ex.Message);
        //        return false;
        //    }
        //}
        // Function to send unacknowledged messages
        //public async void SendUnackSMS()
        //{
        //    ReinitializeChannelIfNeeded();
        //    try
        //    {
        //        var messageCount = _channel.MessageCount(_queueName);
        //        for (ulong i = 0; i < messageCount; i++)
        //        {
        //            // Check if the channel is open
        //            ReinitializeChannelIfNeeded();
        //            var ea = _channel.BasicGet(_queueName, autoAck: false);
        //            if (ea != null)
        //            {
        //                var body = ea.Body.ToArray();
        //                var message = Encoding.UTF8.GetString(body);
        //                _logger.Information("Received message from RabbitMQ: {0}", message);
        //                var sendSmsSmscCommand = JsonConvert.DeserializeObject<SendSmsSmscCommand>(message);
        //                GetSmscSmppClient getSmscSmppClient = new GetSmscSmppClient(_serviceProvider);
        //                SmppClient smppClient = getSmscSmppClient.GetClient(sendSmsSmscCommand.transceiverSystemId);
        //                if (smppClient.Status == ConnectionStatus.Bound)
        //                {
        //                    var response = await _mediator.Send(sendSmsSmscCommand);
        //                    if (response.resultCode == 1)
        //                    {
        //                        // Acknowledge message after successful processing
        //                        _channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
        //                    }
        //                    else
        //                    {
        //                        // If processing failed, requeue the message for later processing
        //                        _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
        //                    }
        //                }
        //                else
        //                {
        //                    // If the client is not connected, requeue the message for later processing
        //                    _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
        //                    await Task.Delay(smppClient.ConnectionRecoveryDelay).ConfigureAwait(false);
        //                }

        //            }
        //        }

        //        _logger.Information("Unacknowledged messages re-sent successfully.");
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error("Error on re-sending unacknowledged messages: {0}", ex.Message);
        //    }
        //}
        //protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        //{
        //    while (!stoppingToken.IsCancellationRequested && !_isConsuming)
        //    {

        //        //if (_smppClient != null && _smppClient.IsClientConnected)
        //        //{
        //        _isConsuming = await StartConsuming();
        //        //}
        //        //else
        //        //{
        //        //    await Task.Delay(5000, stoppingToken); // Adjust the delay as needed
        //        //}
        //    }
        //}
        //public override async Task StopAsync(CancellationToken cancellationToken)
        //{
        //    _isConsuming= false;
        //    //// Call base implementation if you have any additional cleanup
        //    await base.StopAsync(cancellationToken);
        //}
        /// <summary>
        /// Asynchronously processes an interrupted SubmitSm operation.
        /// </summary>
        /// <param name="submitSm">The SubmitSm PDU that was interrupted.</param>
        /// <param name="serverClient">The SMPP server client associated with the operation.</param>
        public async Task ProcessInteraptedSubmitSmAsync(SmscRabbitMQSaveSMSCommand smscRabbitMQSaveSMSCommand)
        {
            var message = JsonConvert.SerializeObject(smscRabbitMQSaveSMSCommand);
            var body = Encoding.UTF8.GetBytes(message);

            int maxRetryAttempts = 5;
            int retryDelay = 2000; // Delay between retries in milliseconds

            for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    // Ensure the channel is open before publishing the message
                    // Publish message to RabbitMQ
                    if (await this.ReinitializeChannelIfNeeded())
                    {
                        await this._channel.BasicPublishAsync(
                             exchange: "",
                             routingKey: this._queueName,
                             mandatory: false,
                             basicProperties: new BasicProperties(), // Use an actual instance instead of null
                             body: body);
                    }

                    // Exit the loop if the publish was successful
                    break;
                }
                catch (AlreadyClosedException ex)
                {
                    _logger.Warning("RabbitMQ channel was closed unexpectedly. Attempt {0} of {1}. Exception: {2}", attempt, maxRetryAttempts, ex.Message);

                    if (attempt == maxRetryAttempts)
                    {
                        _logger.Error("Max retry attempts reached. Failed to publish message to RabbitMQ. Exception: {0}", ex.Message);
                        throw;
                    }

                    // Wait before retrying
                    await Task.Delay(retryDelay);
                }
                catch (Exception ex)
                {
                    _logger.Error("An unexpected error occurred while publishing to RabbitMQ. Attempt {0} of {1}. Exception: {2}", attempt, maxRetryAttempts, ex.Message);
                    throw; // Re-throw the exception to be handled by the calling code
                }
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="submitSm"></param>
        /// <param name="serverClient"></param>
        /// <returns></returns>
        public async Task ProcessInteraptedSubmitSmAsync(SubmitSm submitSm, SmppServerClient serverClient)
        {
            var smscRabbitMQSaveSMSCommand = CreateSmscRabbitMQSaveSMSCommand(submitSm, serverClient);
            var message = JsonConvert.SerializeObject(smscRabbitMQSaveSMSCommand);
            var body = Encoding.UTF8.GetBytes(message);

            int maxRetryAttempts = 5;
            int retryDelay = 2000; // Delay between retries in milliseconds

            for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    // Ensure the channel is open before publishing the message
                    // Publish message to RabbitMQ
                    if (await this.ReinitializeChannelIfNeeded())
                    {
                        await this._channel.BasicPublishAsync(
                            exchange: "",
                            routingKey: this._queueName,
                            mandatory: false,
                            basicProperties: new BasicProperties(),
                            body: body.AsMemory(),
                            cancellationToken: CancellationToken.None);


                    }
                    else
                    {
                        _logger.Error($"The message {message} didn't send or saved on RabbitMQ because it's down");
                    }
                    // Exit the loop if the publish was successful
                    break;
                }
                catch (AlreadyClosedException ex)
                {
                    _logger.Warning("RabbitMQ channel was closed unexpectedly. Attempt {0} of {1}. Exception: {2}", attempt, maxRetryAttempts, ex.Message);

                    if (attempt == maxRetryAttempts)
                    {
                        _logger.Error("Max retry attempts reached. Failed to publish message to RabbitMQ. Exception: {0}", ex.Message);
                        throw;
                    }

                    // Wait before retrying
                    await Task.Delay(retryDelay);
                }
                catch (Exception ex)
                {
                    _logger.Error("An unexpected error occurred while publishing to RabbitMQ. Attempt {0} of {1}. Exception: {2}", attempt, maxRetryAttempts, ex.Message);
                    throw; // Re-throw the exception to be handled by the calling code
                }
            }
        }

        /// <summary>
        /// Creates a command to save the SMS message to RabbitMQ.
        /// </summary>
        /// <param name="submitSm">The SubmitSm PDU containing the SMS message.</param>
        /// <param name="serverClient">The SMPP server client associated with the operation.</param>
        /// <returns>A command object to save the SMS message to RabbitMQ.</returns>
        private SmscRabbitMQSaveSMSCommand CreateSmscRabbitMQSaveSMSCommand(SubmitSm submitSm, SmppServerClient serverClient)
        {
            var smscRabbitMQSaveSMSCommand = new SmscRabbitMQSaveSMSCommand()
            {
                dataCodings = submitSm.DataCoding,
                dstAdr = submitSm.DestinationAddress.Address,
                dstNpi = submitSm.DestinationAddress.NPI,
                dstTon = submitSm.DestinationAddress.TON,
                smsDstType = SmsDstType.local,
                smsText = submitSm.GetMessageText(serverClient.EncodingMapper),
                srcAdr = submitSm.SourceAddress.Address,
                srcNpi = submitSm.SourceAddress.NPI,
                srcTon = submitSm.SourceAddress.TON,
                transceiverSystemId = serverClient.SystemID
                //MessageId = submitSm.Response.MessageId
            };

            if (submitSm.Parameters.Count > 0)
            {
                if (submitSm.Parameters[0].ToString().Contains("MessagePayloadParameter"))
                {
                    smscRabbitMQSaveSMSCommand.submitMode = SubmitMode.Payload;
                }
                else
                {
                    smscRabbitMQSaveSMSCommand.submitMode = SubmitMode.ShortMessageWithSAR;
                }
            }

            return smscRabbitMQSaveSMSCommand;
        }
    }
}
