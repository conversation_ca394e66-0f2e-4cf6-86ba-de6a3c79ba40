using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;
using System.Net;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Configuration
{
    public class VaultConfigurationSource : IConfigurationSource
    {
        public string VaultAddress { get; set; }
        public string VaultToken { get; set; }
        public string SecretPath { get; set; }
        public string MountPoint { get; set; } = "secret"; // Default mount point
        public bool IgnoreSslErrors { get; set; } = false;

        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            return new VaultConfigurationProvider(this);
        }
    }

    public class VaultConfigurationProvider : ConfigurationProvider
    {
        private readonly VaultConfigurationSource _source;
        private readonly IVaultClient _vaultClient;
        private readonly ILogger<VaultConfigurationProvider> _logger;

        public VaultConfigurationProvider(VaultConfigurationSource source)
        {
            _source = source;

            // إعداد SSL إذا كان مطلوباً تجاهل أخطاء الشهادات
            if (_source.IgnoreSslErrors)
            {
                ServicePointManager.ServerCertificateValidationCallback =
                    (sender, cert, chain, sslPolicyErrors) => true;
            }

            var authMethod = new TokenAuthMethodInfo(_source.VaultToken);
            var vaultClientSettings = new VaultClientSettings(_source.VaultAddress, authMethod);
            _vaultClient = new VaultClient(vaultClientSettings);

            // إنشاء logger بسيط للتشخيص
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<VaultConfigurationProvider>();
        }

        public override void Load()
        {
            LoadAsync().GetAwaiter().GetResult();
        }

        private async Task LoadAsync()
        {
            try
            {
                _logger?.LogInformation("Loading secrets from Vault. Address: {VaultAddress}, MountPoint: {MountPoint}, SecretPath: {SecretPath}",
                    _source.VaultAddress, _source.MountPoint, _source.SecretPath);

                // تحديد المسار الصحيح - إزالة "data/" إذا كان موجوداً في SecretPath
                var cleanSecretPath = _source.SecretPath?.TrimStart('/');
                if (cleanSecretPath?.StartsWith("data/") == true)
                {
                    cleanSecretPath = cleanSecretPath.Substring(5); // إزالة "data/" من البداية
                }

                _logger?.LogInformation("Using clean secret path: {CleanSecretPath}", cleanSecretPath);

                // قراءة الأسرار من Vault باستخدام KV v2 API
                var secrets = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(
                    path: cleanSecretPath,
                    mountPoint: _source.MountPoint);

                if (secrets?.Data?.Data != null)
                {
                    _logger?.LogInformation("Successfully loaded {SecretCount} secrets from Vault", secrets.Data.Data.Count);

                    foreach (var secret in secrets.Data.Data)
                    {
                        var key = secret.Key;
                        var value = secret.Value?.ToString();

                        Data[key] = value;
                        _logger?.LogDebug("Loaded secret: {Key}", key);
                    }
                }
                else
                {
                    _logger?.LogWarning("No secrets found at path: {SecretPath}", cleanSecretPath);
                }

                // قراءة أسرار إضافية إذا كانت متاحة
                await LoadAdditionalSecrets();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to load secrets from Vault: {ErrorMessage}", ex.Message);

                // عدم إيقاف التطبيق في حالة فشل Vault
                Console.WriteLine($"Vault Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        private async Task LoadAdditionalSecrets()
        {
            // قائمة المسارات الإضافية للأسرار
            var additionalPaths = new Dictionary<string, string>
            {
                { "smsc-credentials", "SMSC" },
                { "database-credentials", "Database" },
                { "external-services", "External" },
                { "fcm-whatsapp", "FcmWhatsapp" }
            };

            foreach (var pathConfig in additionalPaths)
            {
                await LoadSecretsFromPath(pathConfig.Key, pathConfig.Value);
            }
        }

        private async Task LoadSecretsFromPath(string secretPath, string prefix)
        {
            try
            {
                _logger?.LogDebug("Attempting to load secrets from path: {SecretPath} with prefix: {Prefix}", secretPath, prefix);

                var secrets = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(
                    path: secretPath,
                    mountPoint: _source.MountPoint);

                if (secrets?.Data?.Data != null)
                {
                    _logger?.LogInformation("Successfully loaded {SecretCount} secrets from {SecretPath}",
                        secrets.Data.Data.Count, secretPath);

                    foreach (var secret in secrets.Data.Data)
                    {
                        var key = string.IsNullOrEmpty(prefix) ? secret.Key : $"{prefix}:{secret.Key}";
                        var value = secret.Value?.ToString();

                        Data[key] = value;
                        _logger?.LogDebug("Loaded secret: {Key} from path: {SecretPath}", key, secretPath);
                    }
                }
                else
                {
                    _logger?.LogDebug("No secrets found at path: {SecretPath}", secretPath);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to load secrets from path {SecretPath}: {ErrorMessage}",
                    secretPath, ex.Message);
            }
        }

        /// <summary>
        /// تحديث الأسرار من Vault (للاستخدام في وقت التشغيل)
        /// </summary>
        public async Task RefreshSecretsAsync()
        {
            try
            {
                _logger?.LogInformation("Refreshing secrets from Vault...");
                await LoadAsync();
                OnReload(); // إشعار النظام بتحديث التكوين
                _logger?.LogInformation("Secrets refreshed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to refresh secrets from Vault: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// التحقق من صحة الاتصال مع Vault
        /// </summary>
        public async Task<bool> ValidateConnectionAsync()
        {
            try
            {
                // محاولة قراءة معلومات أساسية من Vault للتحقق من الاتصال
                var health = await _vaultClient.V1.System.GetHealthStatusAsync();
                return health != null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Vault connection validation failed: {ErrorMessage}", ex.Message);
                return false;
            }
        }
    }
}