using Microsoft.Extensions.Hosting;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public class SmscRabbitMQGeneralHostedService : IHostedService
    {
        private readonly SmscRabbitMQGeneral _smscRabbitMQGeneral;

        public SmscRabbitMQGeneralHostedService(SmscRabbitMQGeneral smscRabbitMQGeneral)
        {
            _smscRabbitMQGeneral = smscRabbitMQGeneral;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            await _smscRabbitMQGeneral.StartAsync();
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }
    }
}
