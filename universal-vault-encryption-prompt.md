# UNIVERSAL VAULT ENCRYPTION IMPLEMENTATION PROMPT

## 🎯 TASK: Implement Hashi<PERSON>orp Vault password encryption for this project

### 📋 ANALYSIS REQUIRED:
1. **Project Type Detection:**
   - Determine if .NET Framework or .NET Core/5+
   - Check existing configuration system (app.config, appsettings.json, etc.)
   - Identify if Dock<PERSON> is used or standalone deployment
   - Find all password/secret locations in configuration

2. **Current Architecture Assessment:**
   - Dependency injection container (if any)
   - Configuration loading mechanism
   - Existing encryption/security implementations
   - Database connection patterns
   - External service integrations

### 🔧 IMPLEMENTATION REQUIREMENTS:

#### A. VAULT SETUP (Universal)
Create these files regardless of project type:
1. `vault-scripts/init-vault.sh` - Vault initialization script
2. `docker-compose.vault.yml` - Vault container setup
3. `vault-config/vault.hcl` - Vault server configuration

#### B. .NET FRAMEWORK SPECIFIC:
If project uses .NET Framework:
1. Add VaultSharp NuGet package
2. Create `VaultConfigurationManager.cs` - Custom config manager
3. Update `app.config` with Vault placeholders
4. Modify `Global.asax.cs` or startup code
5. Create `VaultEncryptionService.cs` with sync methods

#### C. .NET CORE/5+ SPECIFIC:
If project uses .NET Core/5+:
1. Add VaultSharp NuGet package
2. Create `VaultConfigurationProvider.cs` - Configuration provider
3. Update `appsettings.json` with Vault references
4. Modify `Program.cs` and `Startup.cs`
5. Create `VaultEncryptionService.cs` with async methods

#### D. DOCKER INTEGRATION:
If Docker is detected:
1. Update existing `Dockerfile` with Vault client
2. Modify `docker-compose.yml` to include Vault service
3. Add environment variables for Vault connection
4. Create health checks for Vault connectivity

#### E. NON-DOCKER DEPLOYMENT:
If no Docker:
1. Create standalone Vault installation guide
2. Windows service configuration for Vault
3. Local development setup instructions
4. Production deployment checklist

### 🔐 ENCRYPTION PATTERNS TO IMPLEMENT:

#### 1. Database Passwords:
```csharp
// Before: "Server=...;Password=plaintext;"
// After: "Server=...;Password=${VAULT:DB_PASSWORD};"
```

#### 2. API Keys:
```csharp
// Before: "ApiKey": "plain-api-key"
// After: "ApiKey": "${VAULT:EXTERNAL_API_KEY}"
```

#### 3. Service Credentials:
```csharp
// Before: Username/Password in config
// After: Vault-encrypted credentials with automatic decryption
```

### 📁 FILES TO CREATE/MODIFY:

#### Universal Files (Create Always):
1. `Services/IEncryptionService.cs` - Interface
2. `Services/VaultEncryptionService.cs` - Implementation
3. `Configuration/VaultSettings.cs` - Settings model
4. `Helpers/SecretHelper.cs` - Utility methods
5. `vault-scripts/init-vault.sh` - Initialization
6. `README-Vault-Setup.md` - Documentation

#### Framework-Specific Files:
**For .NET Framework:**
- `Configuration/VaultConfigurationManager.cs`
- `App_Start/VaultConfig.cs`
- Update `packages.config`

**For .NET Core/5+:**
- `Configuration/VaultConfigurationProvider.cs`
- `Extensions/VaultServiceExtensions.cs`
- Update `.csproj` file

### 🔄 MIGRATION STRATEGY:

#### Phase 1: Setup
1. Install and configure Vault server
2. Create encryption keys and policies
3. Add Vault client to project

#### Phase 2: Integration
1. Implement Vault configuration provider
2. Create encryption service
3. Update dependency injection

#### Phase 3: Migration
1. Identify all passwords/secrets
2. Encrypt existing passwords
3. Update configuration files
4. Test connectivity and decryption

#### Phase 4: Deployment
1. Update deployment scripts
2. Configure production Vault
3. Migrate secrets to production
4. Verify all services working

### 🛡️ SECURITY REQUIREMENTS:

#### Development Environment:
- Use Vault dev mode with known token
- Local encryption fallback
- Clear documentation for setup

#### Production Environment:
- Proper Vault authentication (AppRole/Kubernetes)
- Encrypted transit
- Audit logging enabled
- Key rotation policies

### 📊 TESTING CHECKLIST:

#### Unit Tests:
- [ ] Encryption service tests
- [ ] Configuration loading tests
- [ ] Fallback mechanism tests

#### Integration Tests:
- [ ] Vault connectivity tests
- [ ] Secret retrieval tests
- [ ] Application startup tests

#### End-to-End Tests:
- [ ] Full application with Vault
- [ ] Database connection with encrypted password
- [ ] External API calls with encrypted keys

### 🚀 DEPLOYMENT CONFIGURATIONS:

#### Docker Deployment:
```yaml
services:
  vault:
    image: vault:latest
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=dev-token
  
  app:
    depends_on:
      - vault
    environment:
      - VAULT_ADDR=http://vault:8200
      - VAULT_TOKEN=app-token
```

#### Standalone Deployment:
```bash
# Environment variables
export VAULT_ADDR=https://vault.company.com:8200
export VAULT_TOKEN=production-token
```

### 📋 DELIVERABLES:

1. **Complete Vault setup** with all required engines
2. **Encryption service** compatible with project framework
3. **Configuration integration** using existing patterns
4. **Migration scripts** for existing passwords
5. **Documentation** for setup and maintenance
6. **Testing suite** for validation
7. **Deployment guides** for all environments

### 🔍 VALIDATION CRITERIA:

- [ ] All passwords encrypted and stored in Vault
- [ ] Application starts successfully with Vault
- [ ] Database connections work with encrypted passwords
- [ ] External API calls use encrypted credentials
- [ ] Fallback works when Vault unavailable (dev only)
- [ ] Production deployment ready
- [ ] Documentation complete
- [ ] Tests passing

### 📝 NOTES:
- Maintain backward compatibility during migration
- Provide clear rollback procedures
- Include monitoring and alerting setup
- Consider key rotation strategies
- Plan for disaster recovery scenarios

## EXECUTE THIS IMPLEMENTATION:
Analyze the current project structure, determine the appropriate approach based on framework and deployment method, then implement the complete Vault encryption solution following the patterns above.