﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.BackgroundServiceWrapper;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.OtherSmscServer;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ;
using Tamkeen.SMPP.SmscServerGatewayProxyService;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RateLimiting;


namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddSmscServerGatewayProxyServices(this IServiceCollection services, IConfiguration configuration, IConfiguration smscSettings/*, SmscServerSetting smscServerSetting, List<SmscClientSettings> smscClientSettings, RabbitMQSettings rabbitMQSettings*/)
        {

            #region RabbitMQ
            services.AddSingleton(provider =>
            {
                //var logger = provider.GetRequiredService<Serilog.ILogger>();
                //var mediator = provider.GetRequiredService<IMediator>();
                var rabbitMQSettings = new RabbitMQSettings();
                configuration.GetSection(nameof(RabbitMQSettings)).Bind(rabbitMQSettings);
                var logger = provider.GetRequiredService<ILogger>();
                var smscRabbitMQGeneral = new SmscRabbitMQGeneral(rabbitMQSettings, logger);

                //var smscRabbitMQGeneral = new SmscRabbitMQGeneral(rabbitMQSettings);
                return smscRabbitMQGeneral;
            });
            services.AddHostedService<SmscRabbitMQGeneralHostedService>();
            //var smscSettings = new SmscSettings();
            //configuration.GetSection(nameof(SmscSettings)).Bind(smscSettings);
            //services.AddSmscRabbitQMs(smscSettings.smscClientSettings);
            #endregion
            services.AddSingleton<IRateLimiter, LeakyBucketRateLimiter>();
            #region otherSmppSmscServer
            services.AddSingleton(provider =>
            {
                //var logger = provider.GetRequiredService<Serilog.ILogger>();
                //var mediator = provider.GetRequiredService<IMediator>();
                var otherSmscServerSettings = new OtherSmscServerSettings();
                configuration.GetSection("OtherSmscServerSettings").Bind(otherSmscServerSettings);
                var logger = provider.GetRequiredService<ILogger>();
                var smscServerBackgroundService = new SmscServerBackgroundService(provider, otherSmscServerSettings);

                //var smscRabbitMQGeneral = new SmscRabbitMQGeneral(rabbitMQSettings);
                return smscServerBackgroundService;
            });
            services.AddHostedService(provider => provider.GetRequiredService<SmscServerBackgroundService>());
            #endregion
            #region BackgroundService
            //Register SmscServerProxyBackgroundService
            //services.AddSingleton<SmscServerProxyBackgroundService>();

            //Register the background service to run in the background
            //services.AddHostedService<SmscServerProxyBackgroundService>();
            #endregion
            //services.AddSingleton<SmscServerProxy>();
            //services.AddSingleton<SmscServerProxyClient>();
            //services.AddSingleton<SmscRabbitMQSaveSMSCommandHandler>();
            //services.AddSingleton<SendSmsSmscCommandHandler>();
            //services.AddSingleton<SmscServerProxy>();
            //#region registerBackgroundService
            ////foreach (var section in smscSettings.GetChildren())
            ////{
            ////    var smscClientSettings = section.GetSection("SmscClientSettings");
            ////    var rabbitMQSettings = section.GetSection("RabbitMQSettings");
            ////    var smscServerSetting = section.GetSection("SmscServerSetting");

            ////    if (smscClientSettings.GetValue<bool>("enable"))
            ////    {
            ////        services.AddHostedService(provider => new SmscServerGatewayProxyBackgroundService(provider,
            ////            smscClientSettings, rabbitMQSettings, smscServerSetting));
            ////    }
            ////} 
            //var backgroundServices = new Dictionary<string, SmscServerGatewayProxyBackgroundService>();

            //foreach (var section in smscSettings.GetChildren())
            //{
            //    SmscServerGatewayProxySettings smscServerGatewayProxySettings = new SmscServerGatewayProxySettings();
            //    smscServerGatewayProxySettings.rabbitMQSettings = section.GetSection("RabbitMQSettings").Get<RabbitMQSettings>();
            //    smscServerGatewayProxySettings.smscClientSettings = section.GetSection("SmscClientSettings").Get<SmscClientSettings>();
            //    smscServerGatewayProxySettings.smscServerSetting = section.GetSection("SmscServerSetting").Get<SmscServerSetting>();
            //    //var smscClientSettings = section.GetSection("SmscClientSettings");
            //    //var rabbitMQSettings = section.GetSection("RabbitMQSettings");
            //    //var smscServerSetting = section.GetSection("SmscServerSetting");

            //    //if (smscClientSettings.GetValue<bool>("enable"))
            //    //{
            //    var serviceName = section.Key;
            //    services.AddSingleton(provider =>
            //        {
            //            var backgroundService = new SmscServerGatewayProxyBackgroundService(
            //                serviceProvider: provider,
            //                smscServerGatewayProxySettings
            //            );

            //            backgroundServices.Add(backgroundService.TransceiverSystemId, backgroundService);
            //            Log.Logger.Information($"Registered {serviceName} with TransceiverSystemId: {backgroundService.TransceiverSystemId}");

            //            return backgroundService;
            //        });

            //        services.AddHostedService(provider => provider.GetRequiredService<SmscServerGatewayProxyBackgroundService>());
            //    //}
            //}

            //services.AddSingleton(backgroundServices);
            //#endregion
            //// Add more services here
            ///
            //#region Register Background Services

            //var backgroundServices = new Dictionary<string, SmscServerGatewayProxyBackgroundService>();

            //foreach (var section in smscSettings.GetChildren())
            //{
            //    var smscServerGatewayProxySettings = new SmscServerGatewayProxySettings
            //    {
            //        smscClientSettings = section.GetSection("SmscClientSettings").Get<SmscClientSettings>(),
            //        smscServerSetting = section.GetSection("SmscServerSetting").Get<SmscServerSetting>()
            //    };

            //    // Unique service name based on the section key
            //    var serviceName = section.Key;

            //    services.AddSingleton(provider =>
            //    {
            //        // Create an instance of the background service
            //        var backgroundService = new SmscServerGatewayProxyBackgroundService(
            //            serviceProvider: provider,
            //            smscServerGatewayProxySettings
            //        );

            //        // Add to the dictionary with a unique key
            //        backgroundServices.Add(backgroundService.TransceiverSystemId, backgroundService);
            //        Log.Logger.Information($"Registered {serviceName} with TransceiverSystemId: {backgroundService.TransceiverSystemId}");

            //        // Return the background service instance
            //        return backgroundService;
            //    });

            //    // Register each service as a hosted service
            //    services.AddHostedService(provider =>
            //    {
            //        // Retrieve the background service from the dictionary by its TransceiverSystemId
            //        var service = backgroundServices[serviceName];
            //        return service;
            //    });
            //}

            //services.AddSingleton(backgroundServices);

            //#endregion
            //var smscSettings = configuration.GetSection("SmscSettings");
            //services.AddHostedService<SmscServerGatewayProxyBackgroundService>();
            //services.AddSingleton<SmscServerGatewayProxyBackgroundService>();

            foreach (var section in smscSettings.GetChildren())
            {
                var settings = section.Get<SmscServerGatewayProxySettings>();
                if (settings == null)
                {
                    throw new ArgumentNullException($"Settings for {section.Key} cannot be null.");
                }
                if (settings.smscClientSettings?.enable == true)
                {
                    services.AddSingleton<ISmscServerGatewayProxyBackgroundService>(provider =>
                    {
                        // Dynamically select the correct wrapper based on the configuration key
                        return section.Key switch
                        {
                            "SmscYGsmServerGatewayProxySettings" => new YGsmSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscYGsmTransmitterServerGatewayProxySettings" => new YGsmSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscYouTransmitterServerGatewayProxySettings" => new YouSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscYouReceiverServerGatewayProxySettings" => new YouSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscSabaFonTransmitterServerGatewayProxySettings" => new SabaFonSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscSabaFonReceiverServerGatewayProxySettings" => new SabaFonSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscYemenMobileServerGatewayProxySettings" => new YemenMobileSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            "SmscYemenMobileTransmitterServerGatewayProxySettings" => new YemenMobileSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings),
                            _ => throw new NotImplementedException($"No wrapper registered for the key {section.Key}")
                        };
                    });

                    // Register the hosted service
                    services.AddHostedService(provider => provider.GetRequiredService<ISmscServerGatewayProxyBackgroundService>());
                }
                else
                {
                    Log.Logger.Information($"Skipped registration for {section.Key} because it is disabled.");
                }
            }


            //foreach (var section in smscSettings.GetChildren())
            //{
            //    // Determine which wrapper to use based on the section key
            //    switch (section.Key)
            //    {
            //        case "SmscYGsmServerGatewayProxySettings":
            //            services.AddSingleton<YGsmSmscServerGatewayProxyBackgroundServiceWrapper>(provider =>
            //            {
            //                var settings = section.Get<SmscServerGatewayProxySettings>();
            //                if (settings == null)
            //                {
            //                    throw new ArgumentNullException(nameof(settings), $"Settings for {section.Key} cannot be null.");
            //                }
            //                return new YGsmSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings);
            //            });
            //            services.AddHostedService(provider => provider.GetRequiredService<YGsmSmscServerGatewayProxyBackgroundServiceWrapper>());
            //            break;

            //        case "SmscYouTransmitterServerGatewayProxySettings":
            //            services.AddSingleton<YouSmscServerGatewayProxyBackgroundServiceWrapper>(provider =>
            //            {
            //                var settings = section.Get<SmscServerGatewayProxySettings>();
            //                if (settings == null)
            //                {
            //                    throw new ArgumentNullException(nameof(settings), $"Settings for {section.Key} cannot be null.");
            //                }
            //                return new YouSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings);
            //            });
            //            services.AddHostedService(provider => provider.GetRequiredService<YouSmscServerGatewayProxyBackgroundServiceWrapper>());
            //            break;
            //        case "SmscYouReceiverServerGatewayProxySettings":
            //            services.AddSingleton<YouSmscServerGatewayProxyBackgroundServiceWrapper>(provider =>
            //            {
            //                var settings = section.Get<SmscServerGatewayProxySettings>();
            //                if (settings == null)
            //                {
            //                    throw new ArgumentNullException(nameof(settings), $"Settings for {section.Key} cannot be null.");
            //                }
            //                return new YouSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings);
            //            });
            //            services.AddHostedService(provider => provider.GetRequiredService<YouSmscServerGatewayProxyBackgroundServiceWrapper>());
            //            break;
            //        case "SmscSabaFonTransmitterServerGatewayProxySettings":
            //            services.AddSingleton<SabaFonSmscServerGatewayProxyBackgroundServiceWrapper>(provider =>
            //            {
            //                var settings = section.Get<SmscServerGatewayProxySettings>();
            //                if (settings == null)
            //                {
            //                    throw new ArgumentNullException(nameof(settings), $"Settings for {section.Key} cannot be null.");
            //                }
            //                return new SabaFonSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings);
            //            });
            //            services.AddHostedService(provider => provider.GetRequiredService<SabaFonSmscServerGatewayProxyBackgroundServiceWrapper>());
            //            break;
            //        case "SmscSabaFonReceiverServerGatewayProxySettings":
            //            services.AddSingleton<SabaFonSmscServerGatewayProxyBackgroundServiceWrapper>(provider =>
            //            {
            //                var settings = section.Get<SmscServerGatewayProxySettings>();
            //                if (settings == null)
            //                {
            //                    throw new ArgumentNullException(nameof(settings), $"Settings for {section.Key} cannot be null.");
            //                }
            //                return new SabaFonSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings);
            //            });
            //            services.AddHostedService(provider => provider.GetRequiredService<SabaFonSmscServerGatewayProxyBackgroundServiceWrapper>());
            //            break;
            //        case "SmscYemenMobileServerGatewayProxySettings":
            //            services.AddSingleton<YemenMobileSmscServerGatewayProxyBackgroundServiceWrapper>(provider =>
            //            {
            //                var settings = section.Get<SmscServerGatewayProxySettings>();
            //                if (settings == null)
            //                {
            //                    throw new ArgumentNullException(nameof(settings), $"Settings for {section.Key} cannot be null.");
            //                }
            //                return new YemenMobileSmscServerGatewayProxyBackgroundServiceWrapper(provider, settings);
            //            });
            //            services.AddHostedService(provider => provider.GetRequiredService<YemenMobileSmscServerGatewayProxyBackgroundServiceWrapper>());
            //            break;

            //        default:
            //            throw new NotImplementedException($"No wrapper registered for the key {section.Key}");
            //    }
            //}


            return services;


        }
        //public static void AddSmscRabbitQMs(this IServiceCollection services, List<SmscClientSettings> smscClientSettings)
        //{
        //    foreach (var settings in smscClientSettings)
        //    {
        //        if (settings.enable == true)
        //        {
        //            services.AddSingleton<SmscRabbitMQSendSmsServiceVersion2>(provider =>
        //            {
        //                //var logger = provider.GetRequiredService<Serilog.ILogger>();
        //                //var smscRabbitMQGeneral = provider.GetRequiredService<SmscRabbitMQGeneral>();
        //                var client = new SmscRabbitMQSendSmsServiceVersion2(provider, settings.transceiverSystemId);
        //                // Additional configuration or setup for Smsc can be done here if needed


        //                return client;
        //            });
        //        }
        //    }

        //}
    }
}