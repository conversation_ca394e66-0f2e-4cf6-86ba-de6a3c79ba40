﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="GatewayServer\BackgroundServiceWrapper\SabaFonSmscServerGatewayProxyBackgroundServiceWrapper2.cs" />
    <Compile Remove="GatewayServer\Models\Smsc\GetSmscSmppClient.cs" />
    <Compile Remove="GatewayServer\Models\Smsc\SendSmsSmscCommandHandler.cs" />
    <Compile Remove="GatewayServer\OtherSmscServer\SmscClient.cs" />
    <Compile Remove="GatewayServer\RabbitMQ\ProcessInteraptedSubmitSm.cs" />
    <Compile Remove="GatewayServer\RabbitMQ\RabbitMQMessageBridge.cs" />
    <Compile Remove="GatewayServer\RabbitMQ\RabbitMQMessageStore.cs" />
    <Compile Remove="GatewayServer\RabbitMQ\SmscRabbitMQSendSmsService.cs" />
    <Compile Remove="GatewayServer\Shared\Helper.cs" />
    <Compile Remove="GatewayServer\SmscServerProxyNew.cs" />
    <Compile Remove="Program.cs" />
    <Compile Remove="SmscServerProxyBackgroundService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="Tamkeen.CacheService.Core" Version="1.0.4" />
    <PackageReference Include="Tamkeen.Encryption.Core" Version="1.0.3" />
    <PackageReference Include="Tamkeen.Inetlab.SMPP" Version="3.0.2" />
    <PackageReference Include="Tamkeen.Monitor.Core" Version="*******" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.6.0-rc.1" />
    <PackageReference Include="System.Threading.RateLimiting" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="ResourceManager\RabbitMQErrorNotiMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>RabbitMQErrorNotiMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="ResourceManager\SmscErrorMessages.Designer.cs">
      <DependentUpon>SmscErrorMessages.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Update="ResourceManager\SmscNotificationMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SmscNotificationMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="ResourceManager\SmscSenderConfiguration.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SmscSenderConfiguration.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="ResourceManager\RabbitMQErrorNotiMessages.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>RabbitMQErrorNotiMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="ResourceManager\SmscErrorMessages.resx">
      <LastGenOutput>SmscErrorMessages.Designer.cs</LastGenOutput>
      <Generator>ResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="ResourceManager\SmscNotificationMessages.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>SmscNotificationMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="ResourceManager\SmscSenderConfiguration.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SmscSenderConfiguration.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
