﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.Annotations;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Shared;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.DropBox
{
    [Route("api/[controller]")]
    [ApiController]
    public class DropBoxController : ControllerBase
    {
        private FcmWhatsappSettings _mySettings;
        private ITamkeenLogRegister _LogRegister;
        public DropBoxController(IOptions<FcmWhatsappSettings> appSettings, ITamkeenLogRegister logRegister)
        {
            _mySettings = appSettings.Value;
            _LogRegister = logRegister;
        }

        [HttpGet("token")]
        [SwaggerOperation("GetToken")]
        [SwaggerResponse(200, "DropBoxTokenResponse", typeof(string))]
        public async Task<IActionResult> GetToken()
        {
            try
            {
                RedisCalls redisCalls = new RedisCalls(_mySettings.RedisHost, _mySettings.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("drpStorage");
                return Ok(token);
            }
            catch (Exception ex)
            {

                return BadRequest(ex.Message);
            }
        }

    }
}
