﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Org.BouncyCastle.Asn1.X509;
using Swashbuckle.AspNetCore.Annotations;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Shared;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.DropBox
{
    [Route("api/[controller]")]
    [ApiController]
    public class DropBoxController : ControllerBase
    {
        private FcmWhatsappSettings _mySettings;
        //private string logRef;
        //private ClaimsModel _claimsModel;
       // private FCMBackgroundServices FcmService;
        private ITamkeenLogRegister _LogRegister;
        public DropBoxController(IOptions<FcmWhatsappSettings> appSettings, ITamkeenLogRegister logRegister)
        {
            _mySettings = appSettings.Value;
            _LogRegister = logRegister;
           // FcmService = new FCMBackgroundServices(_LogRegister, _mySettings);
        }

        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("GetDropBoxToken")]
        [SwaggerOperation("GetDropBoxToken")]
        [SwaggerResponse(200, "DropBoxTokenResponse", typeof(string))]
        public async Task<IActionResult> GetDropBoxToken()
        {
            try
            {
                RedisCalls redisCalls = new RedisCalls(_mySettings.RedisHost, _mySettings.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("drpStorage");
                return Ok(token);
            }
            catch (Exception ex)
            {

                return BadRequest(ex.Message);
            }
        }

    }
}
