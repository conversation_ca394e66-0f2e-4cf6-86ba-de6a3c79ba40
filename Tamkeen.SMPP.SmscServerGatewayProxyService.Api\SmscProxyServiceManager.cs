﻿using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.BackgroundServiceWrapper;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class SmscProxyServiceManager
    {
        private readonly Dictionary<string, ISmscServerGatewayProxyBackgroundService> _services;

        public SmscProxyServiceManager(IEnumerable<ISmscServerGatewayProxyBackgroundService> services)
        {
            _services = services.ToDictionary(service => service.GetTransceiverSystemId(), service => service);
        }

        public bool IsSmscServerProxyServiceRunning(string transceiverSystemId)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                return service.IsRunning();
            }
            throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
        }

        public async Task StartSmscServerProxyServiceAsync(string transceiverSystemId, CancellationToken cancellationToken)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                await service.StartAsync(cancellationToken);
            }
            else
            {
                throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
            }
        }

        public async Task StopSmscServerProxyServiceAsync(string transceiverSystemId, CancellationToken cancellationToken)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                await service.StopAsync(cancellationToken);
            }
            else
            {
                throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
            }
        }

        public async Task RestartSmscServerProxyServiceAsync(string transceiverSystemId, CancellationToken cancellationToken)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                await service.RestartServiceAsync(cancellationToken);
            }
            else
            {
                throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
            }
        }

        public async Task ReInitializeSmscServerProxyServiceClientAsync(string transceiverSystemId, CancellationToken cancellationToken)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                await service.ReInitializeSmscServerProxyClientAsync(cancellationToken);
            }
            else
            {
                throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
            }
        }

        public IEnumerable<object> GetAllSmscServerProxyStatuses()
        {
            return _services.Select(service => new
            {
                TransceiverSystemId = service.Key,
                IsRunning = service.Value.IsRunning()
            }).ToList();
        }

        public async Task StartAllServicesAsync(CancellationToken cancellationToken)
        {
            foreach (var service in _services.Values)
            {
                await service.StartAsync(cancellationToken);
            }
        }

        public async Task StopAllServicesAsync(CancellationToken cancellationToken)
        {
            foreach (var service in _services.Values)
            {
                await service.StopAsync(cancellationToken);
            }
        }

        public async Task RestartAllServicesAsync(CancellationToken cancellationToken)
        {
            foreach (var service in _services.Values)
            {
                await service.RestartServiceAsync(cancellationToken);
            }
        }

        public async Task<IEnumerable<object>> GetAllSmscProxysStatusesAsync()
        {
            var tasks = _services.Select(async service => new
            {
                TransceiverSystemId = service.Key,
                IsSmscProxyClientWorking = await service.Value.IsSmscProxyClientWorking(),
                IsSmscServiceRunning = service.Value.IsRunning()
            }).ToList();

            return await Task.WhenAll(tasks);
        }

        public async Task<IBaseSmscResponse> SendSmsAsync(string transceiverSystemId, SendSmsSmscCommand sendSmsSmscCommand, CancellationToken cancellationToken)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                return await service.SendSmsAsync(sendSmsSmscCommand, cancellationToken);
            }
            else
            {
                throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
            }
        }
        public IReadOnlyList<SmppConnectedClients> GetSmppServerConnectedClients(string transceiverSystemId)
        {
            if (_services.TryGetValue(transceiverSystemId, out var service))
            {
                return service.SmppServerConnectedClients();
            }
            else
            {
                throw new KeyNotFoundException($"Service with TransceiverSystemId {transceiverSystemId} not found.");
            }
        }
        public IReadOnlyList<SmppConnectedClients> GetAllConnectedClients()
        {
            var allConnectedClients = new List<SmppConnectedClients>();

            foreach (var service in _services.Values)
            {
                var connectedClients = service.SmppServerConnectedClients();
                if (connectedClients != null && connectedClients.Count > 0)
                {
                    allConnectedClients.AddRange(connectedClients);
                }
            }

            return allConnectedClients.AsReadOnly();
        }
        public Dictionary<string, SmscServerGatewayProxySettings> GetAllRunningSettings()
        {
            return _services.ToDictionary(
                s => s.Key,
                s => s.Value.GetSettings()
            );
        }
    }

}
