﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class SmscNotificationMessages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SmscNotificationMessages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager.SmscNotificationMessag" +
                            "es", typeof(SmscNotificationMessages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bind failed: Status: {0}.
        /// </summary>
        internal static string BindFailed {
            get {
                return ResourceManager.GetString("BindFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The bind is not connected as the proxy client is {0} and the connected client is {1}.
        /// </summary>
        internal static string BindMissMatch {
            get {
                return ResourceManager.GetString("BindMissMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bind succeeded: Status: {0}, SystemId: {1}.
        /// </summary>
        internal static string BindSucceeded {
            get {
                return ResourceManager.GetString("BindSucceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client {0} bind as {1}:{2}.
        /// </summary>
        internal static string ClientBind {
            get {
                return ResourceManager.GetString("ClientBind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client {0} tries to bind with invalid SystemId: {1}.
        /// </summary>
        internal static string ClientBindInvalidSystemId {
            get {
                return ResourceManager.GetString("ClientBindInvalidSystemId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client {0} tries to bind with invalid Password..
        /// </summary>
        internal static string ClientBindWithInvalidPassword {
            get {
                return ResourceManager.GetString("ClientBindWithInvalidPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RunAsync is already in progress for client {0}. Skipping....
        /// </summary>
        internal static string RunAsyncAlreadyInProgress {
            get {
                return ResourceManager.GetString("RunAsyncAlreadyInProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMS Received: {0}.
        /// </summary>
        internal static string SmsReceived {
            get {
                return ResourceManager.GetString("SmsReceived", resourceCulture);
            }
        }
    }
}
