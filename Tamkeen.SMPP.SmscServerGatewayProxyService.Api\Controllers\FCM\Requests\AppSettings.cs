﻿namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.FCM.Requests
{
    public class AppSettings
    {
       
        public string ApplicationName { get; set; }
       
        public string RedisHost { get; set; }
        public int RedisPort { get; set; }
        public string RedisEndPoint { get; set; }
        public string LogFilePath { get; set; }
        public double TokenSessionTimeExpiration { get; set; }
        public string CommonTimeExpiration { get; set; }
        public string SeqUrl { get; set; }
        public string DalUrl { get; set; }
        public string Sec { get; }
        public string PhoneNumberID { get; set; }
        public string PhoneNumberIDExp { get; set; }
        public string Version { get; set; }
        public string SecretKey { get; set; }

        public string ExpServerKey { get; set; }
        public int ServerKey { get; set; }

        public int TimeTransExpired { get; set; }
        public string Mode { get; set; }
    }


}