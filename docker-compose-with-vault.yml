version: '3.8'

services:
  # HashiCorp Vault
  vault:
    image: vault:latest
    container_name: tamkeen-vault
    ports:
      - "8200:8200"
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=tamkeen-root-token
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
      - VAULT_ADDR=http://0.0.0.0:8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault-data:/vault/data
      - ./vault-config:/vault/config
    command: vault server -dev -dev-root-token-id=tamkeen-root-token

  # Vault Init Container
  vault-init:
    image: vault:latest
    depends_on:
      - vault
    environment:
      - VAULT_ADDR=http://vault:8200
      - VAULT_TOKEN=tamkeen-root-token
    volumes:
      - ./vault-scripts:/scripts
    command: /scripts/init-vault.sh

  # Tamkeen SMPP Service
  tamkeen-smpp:
    image: tamkeen-smpp:latest
    container_name: tamkeen-smpp-service
    ports:
      - "5015:5015"
      - "7020:7020"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - VAULT_TOKEN=tamkeen-app-token
      - VAULT_ADDR=http://vault:8200
    depends_on:
      - vault
      - vault-init
    volumes:
      - ./certificates:/app/https
      - ./logs:/app/logs

volumes:
  vault-data:
