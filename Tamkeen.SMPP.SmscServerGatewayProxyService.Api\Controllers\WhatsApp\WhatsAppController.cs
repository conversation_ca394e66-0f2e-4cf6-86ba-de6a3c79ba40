﻿using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using RestSharp;
using Swashbuckle.AspNetCore.Annotations;
using System.Net;
using System.Reflection.Metadata;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.WhatsApp;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp
{
    [Route("")]
    [ApiController]
    public class WhatsAppController : ControllerBase
    {
        private string error;
        private FcmWhatsappSettings _mySettings;
        //private string logRef;
        //private ClaimsModel _claimsModel;
        private WhatsAppBackgroundServices WhatsappService;
        private ITamkeenLogRegister _LogRegister;



        public WhatsAppController(IOptions<FcmWhatsappSettings> appSettings, ITamkeenLogRegister logRegister)
        {
            _mySettings = appSettings.Value;
            _LogRegister = logRegister;
            WhatsappService = new WhatsAppBackgroundServices(_LogRegister, _mySettings);
        }




        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendToWhatsApp")]

        public async Task<IActionResult> SendToWhatsApp(SendToWhatsAppRequest req)
        {


            try
            {
                var command = req.Adapt<SendToWhatsAppCommand>();
                var response = await WhatsappService.SendToWhatsApp(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }


        }
        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendToWhatsAppExp")]

        public async Task<IActionResult> SendToWhatsAppExp(SendToWhatsAppRequest req)
        {
            try
            {
                var command = req.Adapt<SendToWhatsAppCommand>();
                var response = await WhatsappService.SendToWhatsAppExp(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }


        }

        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendToWhatsAppCash")]

        public async Task<IActionResult> SendToWhatsAppCash(SendToWhatsAppRequest req)
        {
            try
            {
                var command = req.Adapt<SendToWhatsAppCommand>();
                var response = await WhatsappService.SendToWhatsAppCash(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }


        }

        /// <summary>
        /// send PDf file 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendPDFToWhatsApp")]
        public async Task<IActionResult> SendPDFToWhatsApp(SendPDFToWhatsAppRequest req)
        {

            try
            {
                var command = req.Adapt<SendPDFToWhatsAppCommand>();
                var response = await WhatsappService.SendPDFToWhatsApp(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }


        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendToWhatsAppAlert")]

        public async Task<IActionResult> SendToWhatsAppAlert(SendToWhatsAppRequest req)
        {
            try
            {
                var command = req.Adapt<SendToWhatsAppCommand>();
                var response = await WhatsappService.SendToWhatsAppAlert(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }



        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendToWhatsAppOTP")]

        public async Task<IActionResult> SendToWhatsAppOTP(SendToWhatsAppRequest req)
        {

            try
            {
                var command = req.Adapt<SendToWhatsAppCommand>();
                var response = await WhatsappService.SendToWhatsAppOTP(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }


        }

        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendToWhatsAppCKR")]

        public async Task<IActionResult> SendToWhatsAppCKR(SendToWhatsAppRequest req)
        {


            try
            {
                var command = req.Adapt<SendToWhatsAppCommand>();
                var response = await WhatsappService.SendToWhatsAppCKR(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }


            

        
    }
}
