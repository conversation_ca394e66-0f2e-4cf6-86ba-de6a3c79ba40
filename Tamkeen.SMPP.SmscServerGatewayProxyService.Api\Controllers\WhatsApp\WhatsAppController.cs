using System;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp.Requests;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp
{
    [Route("api/[controller]")]
    [ApiController]
    public class WhatsAppController : ControllerBase
    {
        private readonly IWhatsAppService _whatsAppService;

        public WhatsAppController(IWhatsAppService whatsAppService)
        {
            _whatsAppService = whatsAppService;
        }

        [HttpPost("send")]
        public async Task<IActionResult> Send(SendToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendToWhatsApp(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost("send/exp")]
        public async Task<IActionResult> SendExp(SendToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendToWhatsAppExp(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost("send/cash")]
        public async Task<IActionResult> SendCash(SendToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendToWhatsAppCash(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        /// <summary>
        /// send PDf file
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost("send/pdf")]
        public async Task<IActionResult> SendPdf(SendPDFToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendPDFToWhatsApp(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost("send/alert")]
        public async Task<IActionResult> SendAlert(SendToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendToWhatsAppAlert(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost("send/otp")]
        public async Task<IActionResult> SendOtp(SendToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendToWhatsAppOTP(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost("send/ckr")]
        public async Task<IActionResult> SendCkr(SendToWhatsAppRequest req)
        {
            try
            {
                var response = await _whatsAppService.SendToWhatsAppCKR(req);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }
    }
}
