﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc
{
    public class SmscClientSettings
    {
        /// <summary>
        /// Indicates whether the client is enabled.
        /// </summary>
        public bool enable { get; set; } = false;

        /// <summary>
        /// Gets or sets the URI of the client.
        /// This is the endpoint where the client will connect.
        /// </summary>
        public string clientUri { get; set; } = "tcp://localhost:6005";

        /// <summary>
        /// Gets or sets the system ID for the SMSC client.
        /// </summary>
        public string systemId { get; set; } = "Test";

        /// <summary>
        /// Gets or sets the password for the SMSC client.
        /// </summary>
        public string password { get; set; } = "test";

        /// <summary>
        /// Gets or sets the mode of connection for the SMSC client.
        /// The mode can be Transceiver, Receiver, or Transmitter.
        /// </summary>
        public string mode { get; set; } = "Transceiver";

        /// <summary>
        /// Gets or sets the mobile pattern used for validation.
        /// </summary>
        public string mobilePattern { get; set; } = @"^(96770|0096770|70|\+96770)";

        /// <summary>
        /// Gets or sets the transceiver system ID.
        /// </summary>
        public string transceiverSystemId { get; set; } = "test";

        /// <summary>
        /// Gets or sets the system type for the SMSC client.
        /// </summary>
        public string systemType { get; set; } = "SMPP";

        /// <summary>
        /// Gets or sets the type of number (TON) for the SMSC client.
        /// </summary>
        public AddressTON ton { get; set; } = 0;

        /// <summary>
        /// Gets or sets the numbering plan indicator (NPI) for the SMSC client.
        /// </summary>
        public AddressNPI npi { get; set; } = 0;

        /// <summary>
        /// Gets or sets the address range used by the SMSC client.
        /// </summary>
        public string addressRange { get; set; } = "";

        /// <summary>
        /// Indicates whether connection recovery is enabled.
        /// If enabled, the client will attempt to reconnect after losing connection.
        /// </summary>
        public bool connectionRecovery { get; set; } = false;

        /// <summary>
        /// Gets or sets the connection mode for the SMSC client.
        /// This determines how the client connects and interacts with the SMSC.
        /// </summary>
        public ConnectionMode connectionMode { get; set; } = ConnectionMode.Transceiver;
        public OptionalParameterList optionalParameterList { get; set; }

    }
    public class OptionalParameterList
    {
        public string requestName { get; set; }
        public string tag { get; set; }
        public string value { get; set; }
    }
}
