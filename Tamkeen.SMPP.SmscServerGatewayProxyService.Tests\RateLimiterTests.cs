using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RateLimiting;
using Xunit;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Tests
{
    public class RateLimiterTests
    {
        [Fact]
        public async Task PermitAsync_RespectsLimit()
        {
            var limiter = new LeakyBucketRateLimiter();
            Assert.True(await limiter.PermitAsync("a"));
            Assert.True(await limiter.PermitAsync("a"));
        }
    }
}
