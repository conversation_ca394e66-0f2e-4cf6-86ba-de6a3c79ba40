﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Builders;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.Logging;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Interfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ;
using Serilog;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscClient
{
    public class SmscClient
    {
        public SmscClientSettings _clientConnectionSettings { get; private set; }
        public readonly SmppClient _client;
        //private readonly SmppServer _proxyServer;
        //public SmppServerClient _proxyServerClient;
        //private readonly SmscMessagesBridge _bridge;
        private readonly ILogger _logger;
        public readonly MessageComposer _messageComposer;
        private readonly SmscRabbitMQSendSmsServiceOld _smscRabbitMQSendSmsServiceVersion2;
        //private readonly string _whatsAppUrl;
        //private readonly string _resource;
        public SmscClient(IServiceProvider serviceProvider,/*, string transceiverSystemId, string addressRange, int ton, int npi,*/ SmscClientSettings smscClientSettings)
        {
            var _smscRabbitMQSendSmsServiceVersion2s = serviceProvider.GetServices<SmscRabbitMQSendSmsServiceOld>();
            _smscRabbitMQSendSmsServiceVersion2 = _smscRabbitMQSendSmsServiceVersion2s.FirstOrDefault(s => s._queueName == smscClientSettings.transceiverSystemId);
            _logger = serviceProvider.GetService<ILogger>();
            //Enable debug logging for Inetlab.SMPP library
            //Inetlab.SMPP.Logging.LogManager.SetLoggerFactory(new LoggerFactoryAdapter(loggerFactory, LogLevel.Debug));
            LogManager.SetLoggerFactory(new SmscSeriLoggerFactoryAdapter(_logger, LogLevel.Debug));

            _clientConnectionSettings = smscClientSettings;
            #region MessageComposer
            _messageComposer = new MessageComposer();
            _messageComposer.evFullMessageReceived += OnFullMessageReceived;
            _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;
            #endregion
            #region Bridg
            ISmscSMSMessageStore store = new SmscInMemoryMessageStore();
            //_bridge = new SmscMessagesBridge(store);

            #endregion
            //_bridge.ReceiptReadyForForward += WhenReceiptReadyForForward;

            _client = new SmppClient();
            _client.ConnectionRecovery = true;
            _client.ConnectionRecoveryDelay = TimeSpan.FromSeconds(5);
            _client.ReceiveTaskScheduler = TaskScheduler.Default;
            _client.Name = _clientConnectionSettings.transceiverSystemId;
            _client.EsmeAddress = new SmeAddress(_clientConnectionSettings.addressRange, (AddressTON)Convert.ToByte(_clientConnectionSettings.ton), (AddressNPI)Convert.ToByte(_clientConnectionSettings.npi));
            _client.SystemType = _clientConnectionSettings.systemType;
            _client.evRecoverySucceeded += ClientOnRecoverySucceeded;

            //_client.Name = "Proxy" + _client.Name;
            _client.evDeliverSm += new DeliverSmEventHandler(client_evDeliverSm);
            //_client.evDeliverSm += (sender, data) =>
            //{
            //    if (data.MessageType == MessageTypes.SMSCDeliveryReceipt)
            //    {
            //        Task.Run(() => _bridge.ReceiptReceived(data))
            //            .ContinueWith(t =>
            //            {
            //                _client.Logger.Error(t.Exception, "ReceiptReceived failed");
            //            }
            //                , TaskContinuationOptions.OnlyOnFaulted);
            //    }
            //    else
            //    {
            //        Task.Run(() => ForwardDeliverSm(data));
            //    }

            //};
            _client.evUnBind += new UnBindEventHandler(client_evUnBind);
            _client.evDisconnected += new DisconnectedEventHandler(client_evDisconnected);

            // _proxyServer=null;


            //LogManager.SetLoggerFactory(new SmscSeriLoggerFactoryAdapter(Log.Logger)); //log to file using serilog  



            //this.RunAsync(_clientConnectionSettings.clientUri,_clientConnectionSettings.connectionMode).AsyncState(false);
        }
        //public static async Task RunAsync(string[] args)
        //{
        //    var host = CreateHostBuilder(args).Build();

        //    var loggerFactory = host.Services.GetRequiredService<ILoggerFactory>();

        //    // Enable debug logging for Inetlab.SMPP library
        //    Inetlab.SMPP.Logging.LogManager.SetLoggerFactory(new LoggerFactoryAdapter(loggerFactory, LogLevel.Debug));

        //    await host.RunAsync();
        //}

        //public static IHostBuilder CreateHostBuilder(string[] args) =>
        //    Host.CreateDefaultBuilder(args)
        //        .ConfigureLogging(logging =>
        //        {
        //            logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);
        //        })
        //        .ConfigureServices((hostContext, services) =>
        //        {
        //            services.AddHostedService<HelloWorldService>(); // Replace HelloWorldService with your service
        //        });
        private void ClientOnRecoverySucceeded(object sender, BindResp data)
        {
            _client.IsClientConnected = true;
            Task.Run(() => _smscRabbitMQSendSmsServiceVersion2.SendUnackSMS());
            //_smscRabbitMQSendSmsServiceVersion2._smppClient=_proxyClient;
            //_smscRabbitMQSendSmsServiceVersion2.SatartSendSms(_client);
            //if (!_smscRabbitMQSendSmsServiceVersion2._isConsuming)
            //{
            //    _smscRabbitMQSendSmsServiceVersion2.StartAsync(CancellationToken.None);
            //}
            //else
            //{
            //    Task.Run(() => _smscRabbitMQSendSmsServiceVersion2.SendUnackSMS());
            //}
        }
        public async Task RunAsync(Uri uri, ConnectionMode mode, string systemId, string password, bool connectionRecovery)
        {
            _client.ConnectionRecovery = connectionRecovery;
            // Create a CancellationTokenSource with a timeout of 1 second
            using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(1)))
            {
                try
                {
                    // Start smscClient.RunAsync() and pass the cancellation token
                    await _client.RetryUntilConnectedAsync(uri.Host, uri.Port, TimeSpan.FromSeconds(5), 20, cancellationTokenSource.Token);

                    // If RunAsync completes within 1 second, continue with the rest of the code
                    _logger.Information("smscClient.RunAsync completed within 1 second");
                    await Task.Run(() => _smscRabbitMQSendSmsServiceVersion2.SendUnackSMS());
                }
                catch (OperationCanceledException)
                {
                    // If RunAsync is cancelled due to timeout, handle the timeout
                    _logger.Information("Timeout occurred");
                }
            }

            BindResp bindResp = await _client.BindAsync(systemId, password, mode);
        }

        private void client_evUnBind(object sender, UnBind data)
        {
            _logger.Information("UnBind request received from " + _client.Name);
        }
        private void client_evDisconnected(object sender)
        {
            _client.IsClientConnected = false;
            _logger.Information(_client.Name + " disconnected");

        }
        //private async Task ForwardDeliverSm(DeliverSm data)
        //{
        //    try
        //    {
        //        var client = FindDestination(data.DestinationAddress);
        //        if (client != null)
        //        {
        //            DeliverSmResp resp = await client.DeliverAsync(data);
        //        }
        //        else
        //        {
        //            // save DeliverSm for delivery when client connects.
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _client.Logger.Error(ex, "Failed to forward DeliverSm.");
        //    }

        //}
        //private SmppServerClient FindDestination(SmeAddress address)
        //{
        //    foreach (SmppServerClient client in _proxyServer.ConnectedClients)
        //    {
        //        if (client.BindingMode == ConnectionMode.Transmitter) continue;

        //        if (!string.IsNullOrEmpty(client.EsmeAddress?.Address))
        //        {
        //            Regex regex = new Regex(client.EsmeAddress?.Address);
        //            if (regex.IsMatch(address.Address))
        //            {
        //                return client;
        //            }
        //        }

        //    }

        //    return null;
        //}
        public async Task<bool> CheckHealthAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return _client.IsClientConnected;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        private void client_evDeliverSm(object sender, DeliverSm data)
        {
            try
            {
                //Check if we received Delivery Receipt
                if (data.MessageType == MessageTypes.SMSCDeliveryReceipt)
                {
                    //Get MessageId of delivered message
                    string messageId = data.Receipt.MessageId;
                    MessageState deliveryStatus = data.Receipt.State;

                    _logger.Information("Delivery Receipt received: {0}", data.Receipt.ToString());
                }
                else
                {

                    // Receive incoming message and try to concatenate all parts
                    if (data.Concatenation != null)
                    {
                        _messageComposer.AddMessage(data);

                        _logger.Information("DeliverSm part received: Sequence: {0}, SourceAddress: {1}, Concatenation ( {2} )" +
                                " Coding: {3}, Text: {4}",
                                data.Header.Sequence, data.SourceAddress, data.Concatenation, data.DataCoding, _client.EncodingMapper.GetMessageText(data));
                    }
                    else
                    {

                        _logger.Information("DeliverSm received : Sequence: {0}, SourceAddress: {1}, Coding: {2}, Text: {3}",
                            data.Header.Sequence, data.SourceAddress, data.DataCoding, _client.EncodingMapper.GetMessageText(data));
                    }
                    //if (_proxyServerClient!=null)
                    //{
                    //    //_proxyServerClient= sender as SmppServerClient;
                    //    DeliverSmResp resp = await _proxyServerClient.DeliverAsync(data);
                    //    if (resp.Header.Status == CommandStatus.ESME_ROK)
                    //    {
                    //        //_bridge.ReceiptDelivered(data.Receipt.MessageId);
                    //        _proxyServer.Logger.Info($"Deliver the Receipt: {resp}");
                    //    }
                    //    else
                    //    {
                    //        _proxyServer.Logger.Warn($"Failed to deliver the Receipt: {resp}");
                    //        _bridge.FailedToDeliverReceipt(data.Receipt.MessageId);
                    //    }
                    //}

                    // Check if an ESME acknowledgement is required
                    if (data.Acknowledgement != SMEAcknowledgement.NotRequested)
                    {
                        // You have to clarify with SMSC support what kind of information they request in ESME acknowledgement.

                        string messageText = data.GetMessageText(_client.EncodingMapper);

                        var smBuilder = SMS.ForSubmit()
                            .From(data.DestinationAddress)
                            .To(data.SourceAddress)
                            .Coding(data.DataCoding)
                            .Concatenation(ConcatenationType.UDH8bit, _client.SequenceGenerator.NextReferenceNumber())
                            .Set(m => m.MessageType = MessageTypes.SMEDeliveryAcknowledgement)
                            .Text(new Receipt
                            {
                                DoneDate = DateTime.Now,
                                State = MessageState.Delivered,
                                //  MessageId = data.Response.MessageId,
                                ErrorCode = "0",
                                SubmitDate = DateTime.Now,
                                Text = messageText.Substring(0, Math.Min(20, messageText.Length))
                            }.ToString()
                            );



                        _client.SubmitAsync(smBuilder).ConfigureAwait(false);
                    }
                }
            }
            catch (Exception ex)
            {
                data.Response.Header.Status = CommandStatus.ESME_RX_T_APPN;
                _logger.Error(ex, "Failed to process DeliverSm");
            }
        }
        #region BridgFunction
        //private async Task WhenReceiptReadyForForward(string systemId, DeliverSm data)
        //{
        //    SmppServerClient client = _proxyServer.ConnectedClients.FirstOrDefault(x => x.SystemID == systemId
        //        && (x.BindingMode == ConnectionMode.Receiver || x.BindingMode == ConnectionMode.Transceiver));
        //    if (client != null)
        //    {

        //        DeliverSmResp resp = await client.DeliverAsync(data);
        //        if (resp.Header.Status == CommandStatus.ESME_ROK)
        //        {
        //            _bridge.ReceiptDelivered(data.Receipt.MessageId);
        //        }
        //        else
        //        {
        //            _proxyServer.Logger.Warn($"Failed to deliver the Receipt: {resp}");
        //            _bridge.FailedToDeliverReceipt(data.Receipt.MessageId);
        //        }

        //    }
        //    else
        //    {
        //        _proxyServer.Logger.Warn($"Unable to find the client '{systemId}' for the Receipt: {data}");
        //    }
        //}

        //public async Task ForwardSubmitSm(SmppServerClient serverClient, SubmitSm data)
        //{
        //    try
        //    {

        //        string messageId = data.Response.MessageId;

        //        _bridge.SubmitReceived(messageId, data);

        //        SubmitSm submitSm = data.ClonePDU();

        //        submitSm.Header.Sequence = 0;

        //        SubmitSmResp resp = await _client.SubmitWithRepeatAsync(submitSm, TimeSpan.FromSeconds(5));

        //        if (resp.Header.Status == CommandStatus.ESME_ROK)
        //        {
        //            _bridge.SubmitForwarded(messageId, resp);
        //        }

        //        if (data.SMSCReceipt == SMSCDeliveryReceipt.NotRequested)
        //        {
        //            _bridge.DeliveryReceiptNotRequested(messageId);
        //        }
        //        else if (resp.Header.Status != CommandStatus.ESME_ROK)
        //        {
        //            _ = SendUndeliverableReceiptAsync(serverClient, data);
        //        }



        //    }
        //    catch (Exception ex)
        //    {
        //        serverClient.Logger.Error(ex, "Failed to process SubmitSm.");
        //    }
        //}

        //private async Task SendUndeliverableReceiptAsync(SmppServerClient serverClient, SubmitSm submitSm)
        //{
        //    await serverClient.DeliverAsync(SMS.ForDeliver().From(submitSm.DestinationAddress).To(submitSm.SourceAddress).Receipt(
        //            new Receipt
        //            {
        //                MessageId = submitSm.Response.MessageId,
        //                DoneDate = DateTime.Now,
        //                State = MessageState.Undeliverable
        //            }
        //            ));

        //}
        #endregion

        #region messages
        private /*async*/ void OnFullMessageReceived(object sender, MessageEventHandlerArgs args)
        {
            SubmitSm pdu = args.GetFirst<SubmitSm>();
            _logger.Information("Full message received From: {0}, To: {1}, Text: {2}", args.GetFirst<DeliverSm>().SourceAddress, args.GetFirst<DeliverSm>().DestinationAddress, args.Text);

            //            try
            //            {



            //                SubmitSm pdu = args.GetFirst<SubmitSm>();
            //                var Mob = pdu.DestinationAddress;
            //#if DEBUG
            //                //Mob= Mob == null ? null :
            //                Mob.Address = "737333203";
            //#endif
            //                var Msg = args.Text;
            //                var ShortCode = pdu.SourceAddress;
            //                //using (var connection = new System.Data.SqlClient.SqlConnection(cs))
            //                //{
            //                //    var affectedRows = connection.Execute(sql, new { MS = Msg.ToString(), MB = Mob.ToString(), SC = ShortCode.ToString() });
            //                //}

            //                _proxyServer.Logger.Info(string.Format("message Received: {0} {1} ", Msg, DateTime.Now.ToString() + Environment.NewLine));

            //                try
            //                {
            //                    RestFulClient client = new RestFulClient(_logger);
            //                    string cleaned = Msg.Replace("\n", "").Replace("\r", "");
            //                    Msg = cleaned;
            //                    //if (Msg.ToLower().Contains("pin") || Msg.ToLower().Contains("الرمز"))
            //                    {
            //                        //var lo = /*await*/ vRestFulClient.CallApiAsync<Object, SendWhatAppDto>(_whatsAppUrl, _resource,
            //                        ////var lo = await vRestFulClient.CallApiAsync<Object, SendWhatAppDto>("https://www.tamkeen.com.ye:5059", "WhatsApp/SendToWhatsApp",
            //                        //new SendWhatAppDto { To = Mob.ToString(), Message = string.Format("{0}", Msg) }, false, 30000, new List<KeyValueDto> {
            //                        //    //new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CRXhwdHJpYXRlIiwibmJmIjoxNjYyODEzMTM3LCJleHAiOjE4MjA1Nzk1MzcsImlhdCI6MTY2MjgxMzEzNywiaXNzIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIiwiYXVkIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIn0.1Iqu__xTyKPrhl_tQ-tIfkF4kAiUcLpVJ7Op7_GVClw" } });//780077268
            //                        //    new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CQnVzaW5lc3MiLCJuYmYiOjE2NjMxNjM5NjMsImV4cCI6MTk3ODc4MzE2MywiaWF0IjoxNjYzMTYzOTYzLCJpc3MiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2giLCJhdWQiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2gifQ.Du4t1tXuxnxBtf7GcvYkQWfgcDzMvbVcAW7EDZniqNU" } });
            //                        var lo = /*await*/ client.CallApi<Object, SendWhatAppDto>(_whatsAppUrl, _resource,
            //                        //var lo = await vRestFulClient.CallApiAsync<Object, SendWhatAppDto>("https://www.tamkeen.com.ye:5059", "WhatsApp/SendToWhatsApp",
            //                        new SendWhatAppDto { To = Mob.ToString(), Message = string.Format("{0}", Msg) }, false, 30000, new List<KeyValueDto> {
            //                            //new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CRXhwdHJpYXRlIiwibmJmIjoxNjYyODEzMTM3LCJleHAiOjE4MjA1Nzk1MzcsImlhdCI6MTY2MjgxMzEzNywiaXNzIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIiwiYXVkIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIn0.1Iqu__xTyKPrhl_tQ-tIfkF4kAiUcLpVJ7Op7_GVClw" } });//780077268
            //                            new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CQnVzaW5lc3MiLCJuYmYiOjE2NjMxNjM5NjMsImV4cCI6MTk3ODc4MzE2MywiaWF0IjoxNjYzMTYzOTYzLCJpc3MiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2giLCJhdWQiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2gifQ.Du4t1tXuxnxBtf7GcvYkQWfgcDzMvbVcAW7EDZniqNU" } });
            //                        ////if (lo != null)
            //                        ////{
            //                        ////    _smppServer.Logger.Info(string.Format("WhatsApp Response: {0}", @lo.ToString().Replace("{", "{{").Replace("}", "}}")));
            //                        ////}
            //                    }

            //                }
            //                catch (Exception ex)
            //                {
            //                    _proxyServer.Logger.Error(string.Format("Exception Received: {0} {1} ", ex.Message, DateTime.Now.ToString() + Environment.NewLine));
            //                };
            //            }
            //            catch (Exception)
            //            {
            //                _proxyServer.Logger.Error(string.Format("Exception Received: {0} ", DateTime.Now.ToString() + Environment.NewLine));
            //            }
            //            _proxyServer.Logger.Info(string.Format("SMS Received: {0}", args.Text));
        }
        private void OnFullMessageTimeout(object sender, MessageEventHandlerArgs args)
        {
            throw new NotImplementedException();
        }
        #endregion
    }
}
