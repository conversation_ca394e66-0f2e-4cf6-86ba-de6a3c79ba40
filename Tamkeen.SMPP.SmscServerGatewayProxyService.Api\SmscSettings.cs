﻿using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class SmscSettings
    {
        public SmscGatewayProxySettings SmscYGsmServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscYGsmTransmitterServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscYouTransmitterServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscYouReceiverServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscSabaFonTransmitterServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscSabaFonReceiverServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscYemenMobileServerGatewayProxySettings { get; set; }
        public SmscGatewayProxySettings SmscYemenMobileTransmitterServerGatewayProxySettings { get; set; }
    }
    public class SmscGatewayProxySettings
    {
        public SmscClientSettings SmscClientSettings { get; set; }
        public SmscServerSetting SmscServerSetting { get; set; }
    }

}
