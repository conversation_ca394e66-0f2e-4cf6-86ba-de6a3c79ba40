﻿using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    /// <summary>
    /// جميع إعدادات بوابات الـ SMSC في النظام.
    /// </summary>
    public class SmscSettings
    {
        public SmscGatewayProxySettings SmscYGsmServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscYGsmTransmitterServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscYouTransmitterServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscYouReceiverServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscSabaFonTransmitterServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscSabaFonReceiverServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscYemenMobileServerGatewayProxySettings { get; set; } = new();
        public SmscGatewayProxySettings SmscYemenMobileTransmitterServerGatewayProxySettings { get; set; } = new();

        /* خصائص Facade للوصول المباشر إلى إعدادات العميل */
        public SmscClientSettings YgsmClient => SmscYGsmServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings YgsmTransmitterClient => SmscYGsmTransmitterServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings YouTransmitterClient => SmscYouTransmitterServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings YouReceiverClient => SmscYouReceiverServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings SabaFonTransmitterClient => SmscSabaFonTransmitterServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings SabaFonReceiverClient => SmscSabaFonReceiverServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings YemenMobileClient => SmscYemenMobileServerGatewayProxySettings.SmscClientSettings;
        public SmscClientSettings YemenMobileTransmitterClient => SmscYemenMobileTransmitterServerGatewayProxySettings.SmscClientSettings;
    }

    /// <summary>
    /// إعدادات بوابة واحدة تتكوّن من إعدادات العميل والخادم.
    /// </summary>
    public class SmscGatewayProxySettings
    {
        public SmscClientSettings SmscClientSettings { get; set; } = new();
        public SmscServerSetting SmscServerSetting { get; set; } = new();
    }
}
