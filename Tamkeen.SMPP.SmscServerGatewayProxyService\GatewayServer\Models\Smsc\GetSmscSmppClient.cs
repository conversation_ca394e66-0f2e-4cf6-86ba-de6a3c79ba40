﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc
{
    public class GetSmscSmppClient
    {
        private readonly SmscServerProxy _smscServerProxy;
        private readonly IEnumerable<SmscClient> _smscClients;
        public GetSmscSmppClient(IServiceProvider serviceProvider)
        {
            _smscServerProxy = serviceProvider.GetService<SmscServerProxy>();
            if (_smscServerProxy == null)
            {
                _smscClients = serviceProvider.GetServices<SmscClient>();
            }
        }
        public SmppClient GetClient(string transceiverSystemId)
        {
            try
            {
                if (_smscServerProxy != null)
                {
                    // Check if SMPP smppClient exists
                    if (!_smscServerProxy._gatewayClients.ContainsKey(transceiverSystemId))
                    {
                        throw new InvalidOperationException($"No SMSC client found for transceiverSystemId {transceiverSystemId}");
                    }
                    // Use the SmscServerProxy if available
                    return _smscServerProxy._gatewayClients[transceiverSystemId]._proxyClient;
                }
                else
                {
                    // Use a specific SmscClient if SmscServerProxy is not available
                    var client = _smscClients.FirstOrDefault(x => x._clientConnectionSettings.transceiverSystemId == transceiverSystemId)._client;
                    if (_smscClients == null)
                    {
                        throw new InvalidOperationException($"No SMSC client found for transceiverSystemId {transceiverSystemId}");
                    }
                    return client;
                }
            }
            catch (Exception ex)
            {
                // Throw a custom exception if an error occurs during SMS submission
                throw new Exception($"Error in GetClient: {ex.Message}", ex);
            }
        }
    }
}
