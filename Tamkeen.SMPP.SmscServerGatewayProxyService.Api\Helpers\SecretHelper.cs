using Serilog;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Helpers
{
    public static class SecretHelper
    {
        public static string ReadSecret(string secretPath)
        {
            try
            {
                if (File.Exists(secretPath))
                {
                    return File.ReadAllText(secretPath).Trim();
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Warning("Failed to read secret from {SecretPath}: {Error}", secretPath, ex.Message);
                return null;
            }
        }

        public static string GetPassword(string envVarName, string secretFilePath = null)
        {
            // أولاً جرب قراءة من Docker Secret
            var secretFile = Environment.GetEnvironmentVariable($"{envVarName}_FILE");
            if (!string.IsNullOrEmpty(secretFile))
            {
                var secret = ReadSecret(secretFile);
                if (!string.IsNullOrEmpty(secret))
                    return secret;
            }

            // إذا لم توجد، استخدم متغير البيئة (للتطوير فقط)
            return Environment.GetEnvironmentVariable(envVarName);
        }
    } 
}