﻿using FluentValidation;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Validators
{
    public class SendSmsSmscRequestValidator : AbstractValidator<SendSmsSmscRequest>
    {
        public SendSmsSmscRequestValidator()
        {
            // Validate smsDstType is a valid enum value
            RuleFor(x => x.smsDstType)
                .IsInEnum()
                .WithMessage( SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<SmsDstType>()));

            // Validate submitMode is a valid enum value
            RuleFor(x => x.submitMode)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<SubmitMode>()));

            // Validate deliveryReceipt is a valid enum value
            RuleFor(x => x.deliveryReceipt)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<SMSCDeliveryReceipt>()));

            // Validate address TON is a valid enum value
            RuleFor(x => x.srcTon)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<AddressTON>()));

            RuleFor(x => x.dstTon)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<AddressTON>()));

            // Validate address NPI is a valid enum value
            RuleFor(x => x.srcNpi)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<AddressNPI>()));

            RuleFor(x => x.dstNpi)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<AddressNPI>()));

            // Validate dataCodings is a valid enum value
            RuleFor(x => x.dataCodings)
                .IsInEnum()
                .WithMessage(SmscSenderConfiguration.ValidatorInvalidEnum +
                             string.Join(", ", Enum.GetValues<DataCodings>()));

            // Validate dstAdr based on smsDstType
            RuleFor(x => x.dstAdr)
                .NotEmpty()
                .WithMessage(SmscSenderConfiguration.ValidatorRequired)
                .Must(BeValidPhoneNumber)
                .WithMessage("{PropertyName} must be a valid phone number of 12 digits.")
                .DependentRules(() =>
                {
                    When(x => x.smsDstType == SmsDstType.local, () =>
                    {
                        RuleFor(x => x.dstAdr)
                            .Must(BeValidLocalNumber)
                            .WithMessage("{PropertyName} must start with '967' followed by '73', '71', '70', '77', or '78'.");
                    });

                    When(x => x.smsDstType == SmsDstType.other, () =>
                    {
                        RuleFor(x => x.dstAdr)
                            .Must(BeValidInternationalNumber)
                            .WithMessage("{PropertyName} must be a valid international number for 'other' destinations.");
                    });
                });
        }

        private bool BeValidPhoneNumber(string dstAdr)
        {
            // Validates the phone number contains exactly 12 digits
            return Regex.IsMatch(dstAdr, @"^\d{12}$");
        }

        private bool BeValidLocalNumber(string dstAdr)
        {
            // Validates local numbers start with '967' followed by '73', '71', '70', '77', or '78'
            if (dstAdr.StartsWith(SmscSenderConfiguration.SenderYemenInternational))
            {
                var validPrefixes = new HashSet<string> { SmscSenderConfiguration.YouStart, SmscSenderConfiguration.SabaFonStart, SmscSenderConfiguration.YGsmStart, SmscSenderConfiguration.YemenMobileStart77, SmscSenderConfiguration.YemenMobileStart78 };
                var prefix = dstAdr.Substring(3, 2); // Get the two digits after '967'
                return validPrefixes.Contains(prefix);
            }
            return false;
        }

        private bool BeValidInternationalNumber(string dstAdr)
        {
            // Customize validation logic for international numbers
            return dstAdr.StartsWith(SmscSenderConfiguration.OtherValidInternationalNumberDZero) || dstAdr.StartsWith(SmscSenderConfiguration.OtherValidInternationalNumberPlus);
        }
    }
}
