﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.WhatsApp
{
    public class CloudApiTemplateRequest
    {
        public string messaging_product { get; set; } = "whatsapp";

        public string to { get; set; }

        public string type { get; set; } = "template";

        public Template template { get; set; }
    }
    public class CloudApiTemplateRequest2
    {
        public string messaging_product { get; set; } = "whatsapp";

        public string to { get; set; }

        public string type { get; set; } = "template";

        public Template2 template { get; set; }
    }
}
