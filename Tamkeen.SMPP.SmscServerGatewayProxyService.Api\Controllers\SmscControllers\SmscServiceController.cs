﻿using Microsoft.AspNetCore.Mvc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SmscControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SmscServiceController : ControllerBase
    {
        SmscServiceManager _smscServiceManager;
        public SmscServiceController(SmscServiceManager smscServiceManager)
        {
            _smscServiceManager = smscServiceManager;
        }
        
        [HttpGet("status")]
        public IActionResult GetStatus()
        {
            if (_smscServiceManager.IsSmscServiceRunning())
            {
                return Ok(new { Status = "Smsc Service is running" });
            }
            else
            {
                return Ok(new { Status = "Smsc Service is not running"});
            }
            
        }

        [HttpPost("start")]
        public async Task<IActionResult> Start(CancellationToken cancellationToken)
        {
            try
            {
                await _smscServiceManager.StartServiceAsync(cancellationToken);
                return Ok(new { Status = "Smsc Service Started" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("stop")]
        public async Task<IActionResult> Stop(CancellationToken cancellationToken)
        {
            try
            {
                await _smscServiceManager.StopServiceAsync(cancellationToken);
                return Ok(new { Status = "Smsc Service Stopped" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("restart")]
        public async Task<IActionResult> Restart(CancellationToken cancellationToken)
        {
            try
            {
                await _smscServiceManager.RestartServiceAsync(cancellationToken);
                return Ok(new {Status = "Smsc Service Restarted" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
    }
}
