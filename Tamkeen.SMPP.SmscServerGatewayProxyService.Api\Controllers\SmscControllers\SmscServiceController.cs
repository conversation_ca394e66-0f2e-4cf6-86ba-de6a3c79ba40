﻿using Microsoft.AspNetCore.Mvc;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SmscControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SmscServiceController : ControllerBase
    {
        SmscServiceManager _smscServiceManager;
        public SmscServiceController(SmscServiceManager smscServiceManager)
        {
            _smscServiceManager = smscServiceManager;
        }
        
        [HttpGet("GetSmscServiceStatus")]
        public IActionResult GetSmscServiceStatus()
        {
            if (_smscServiceManager.IsSmscServiceRunning())
            {
                return Ok(new { Status = "Smsc Service is running" });
            }
            else
            {
                return Ok(new { Status = "Smsc Service is not running"});
            }
            
        }

        [HttpPost("StartSmscService")]
        public async Task<IActionResult> StartSmscService(CancellationToken cancellationToken)
        {
            try
            {
                await _smscServiceManager.StartServiceAsync(cancellationToken);
                return Ok(new { Status = "Smsc Service Started" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("StopSmscService")]
        public async Task<IActionResult> StopSmscService(CancellationToken cancellationToken)
        {
            try
            {
                await _smscServiceManager.StopServiceAsync(cancellationToken);
                return Ok(new { Status = "Smsc Service Stopped" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("RestartSmscService")]
        public async Task<IActionResult> RestartSmscService(CancellationToken cancellationToken)
        {
            try
            {
                await _smscServiceManager.RestartServiceAsync(cancellationToken);
                return Ok(new {Status = "Smsc Service Restarted" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
    }
}
