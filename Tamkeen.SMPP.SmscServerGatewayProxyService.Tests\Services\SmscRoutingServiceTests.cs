using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Moq;
using System.Collections.Generic;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api;
using Xunit;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Tests.Services
{
    public class SmscRoutingServiceTests
    {
        private readonly SmscRoutingService _routingService;
        private readonly Mock<IConfiguration> _mockConfiguration;

        public SmscRoutingServiceTests()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            SetupConfiguration();
            _routingService = new SmscRoutingService(_mockConfiguration.Object);
        }

        private void SetupConfiguration()
        {
            var configData = new Dictionary<string, string>
            {
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96777|0096777|77|\\+96777|96778|0096778|78|\\+96778)",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YemenMobile",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:ton"] = "5",
                ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:npi"] = "1",

                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96771|0096771|71|\\+96771)",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "SabaFonTrans",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:ton"] = "1",
                ["SmscSettings:SmscSabaFonTransmitterServerGatewayProxySettings:SmscClientSettings:npi"] = "1",

                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96773|0096773|73|\\+96773)",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YouTrans",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:ton"] = "1",
                ["SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscClientSettings:npi"] = "1",

                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96770|0096770|70|\\+96770)",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YGsm",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:ton"] = "5",
                ["SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:npi"] = "0"
            };

            var mockConfigurationBuilder = new ConfigurationBuilder();
            mockConfigurationBuilder.AddInMemoryCollection(configData);
            var configuration = mockConfigurationBuilder.Build();

            // Setup the mock to return the actual configuration
            _mockConfiguration.Setup(x => x.GetSection(It.IsAny<string>()))
                .Returns<string>(key => configuration.GetSection(key));
        }

        [Theory]
        [InlineData("***********", "YemenMobile")] // Yemen Mobile 77
        [InlineData("***********", "YemenMobile")] // Yemen Mobile 78
        [InlineData("00***********", "YemenMobile")] // Yemen Mobile with country code
        [InlineData("********", "YemenMobile")] // Yemen Mobile short format
        [InlineData("+***********", "YemenMobile")] // Yemen Mobile with plus
        public void TryResolve_YemenMobileNumbers_ReturnsCorrectRoute(string phoneNumber, string expectedProvider)
        {
            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeTrue();
            routeInfo.Should().NotBeNull();
            routeInfo.TransceiverSystemId.Should().Be(expectedProvider);
            routeInfo.SrcTon.Should().Be("5");
            routeInfo.SrcNpi.Should().Be("1");
        }

        [Theory]
        [InlineData("***********", "SabaFonTrans")] // SabaFon
        [InlineData("00***********", "SabaFonTrans")] // SabaFon with country code
        [InlineData("********", "SabaFonTrans")] // SabaFon short format
        [InlineData("+***********", "SabaFonTrans")] // SabaFon with plus
        public void TryResolve_SabaFonNumbers_ReturnsCorrectRoute(string phoneNumber, string expectedProvider)
        {
            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeTrue();
            routeInfo.Should().NotBeNull();
            routeInfo.TransceiverSystemId.Should().Be(expectedProvider);
            routeInfo.SrcTon.Should().Be("1");
            routeInfo.SrcNpi.Should().Be("1");
        }

        [Theory]
        [InlineData("***********", "YouTrans")] // YOU
        [InlineData("00***********", "YouTrans")] // YOU with country code
        [InlineData("********", "YouTrans")] // YOU short format
        [InlineData("+***********", "YouTrans")] // YOU with plus
        public void TryResolve_YouNumbers_ReturnsCorrectRoute(string phoneNumber, string expectedProvider)
        {
            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeTrue();
            routeInfo.Should().NotBeNull();
            routeInfo.TransceiverSystemId.Should().Be(expectedProvider);
            routeInfo.SrcTon.Should().Be("1");
            routeInfo.SrcNpi.Should().Be("1");
        }

        [Theory]
        [InlineData("967********", "YGsm")] // Y GSM
        [InlineData("*************", "YGsm")] // Y GSM with country code
        [InlineData("********", "YGsm")] // Y GSM short format
        [InlineData("+967********", "YGsm")] // Y GSM with plus
        public void TryResolve_YGsmNumbers_ReturnsCorrectRoute(string phoneNumber, string expectedProvider)
        {
            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeTrue();
            routeInfo.Should().NotBeNull();
            routeInfo.TransceiverSystemId.Should().Be(expectedProvider);
            routeInfo.SrcTon.Should().Be("5");
            routeInfo.SrcNpi.Should().Be("0");
        }

        [Theory]
        [InlineData("***********")] // Invalid prefix
        [InlineData("***********")] // Invalid prefix
        [InlineData("**********")] // International number
        [InlineData("+**********")] // International number with plus
        [InlineData("***********")] // Non-existent Yemen prefix
        public void TryResolve_InvalidNumbers_ReturnsFalse(string phoneNumber)
        {
            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeFalse();
            routeInfo.Should().BeNull();
        }

        [Theory]
        [InlineData("")] // Empty string
        [InlineData(" ")] // Whitespace
        [InlineData("abc")] // Non-numeric
        public void TryResolve_InvalidInput_ReturnsFalse(string phoneNumber)
        {
            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeFalse();
            routeInfo.Should().BeNull();
        }

        [Fact]
        public void TryResolve_CaseInsensitive_WorksCorrectly()
        {
            // Arrange
            var phoneNumber = "+***********"; // Mixed case in pattern

            // Act
            var result = _routingService.TryResolve(phoneNumber, out var routeInfo);

            // Assert
            result.Should().BeTrue();
            routeInfo.Should().NotBeNull();
            routeInfo.TransceiverSystemId.Should().Be("YemenMobile");
        }
    }
}
