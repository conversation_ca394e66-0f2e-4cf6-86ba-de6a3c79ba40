﻿using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.FCM.Requests;
using Microsoft.Extensions.Options;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Mapster;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.FCM
{
    [Route("api/[controller]")]
    [ApiController]
    public class FCMController : ControllerBase
    {
        private FcmWhatsappSettings _mySettings;
        private FCMBackgroundServices FcmService;
        private ITamkeenLogRegister _LogRegister;
        public FCMController(IOptions<FcmWhatsappSettings> appSettings, ITamkeenLogRegister logRegister)
        {
            _mySettings = appSettings.Value;
            _LogRegister = logRegister;
            FcmService = new FCMBackgroundServices(_LogRegister, _mySettings);
        }

        [HttpPost("send")]
        [SwaggerOperation("Send")]
        [SwaggerResponse(200, "SendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendAsync(FCMMessageInfoRequest info)
        {
            try {
                var command = info.Adapt<FCMMessageInfoCommand>();
                var response = await FcmService.SendFCM(command);
               
                return Ok(response);
            }
           
            catch (Exception ex)
            {
               

                return BadRequest(ex);
            }
        }

        [HttpPost("mobile")]
        public async Task<IActionResult> GetMobileAsync(GetMobileFCMRequest info)
        {
           
            try
            {
                var command = info.Adapt<GetMobileFCMCommand>();
                var response = await FcmService.GetMobileFCM(command);

                return Ok(response);

            }
                      
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [HttpPost("send/ios")]
        [SwaggerOperation("SendIOS")]
        [SwaggerResponse(200, "SendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendIos(FCMMessageInfoRequest info)
        {
           
            try
            {
                var command = info.Adapt<FCMMessageInfoCommand>();
                var response = await FcmService.SendFCMIOS(command);
                return Ok(response);

            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }
        [HttpPost("send/ios/v2")]
        [SwaggerOperation("SendIOSV2")]
        [SwaggerResponse(200, "SendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendIosV2(ExpMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.SendFCMIOSV2(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }


        [HttpPost("send/exp")]
        [SwaggerOperation("SendExp")]
        [SwaggerResponse(200, "ExpSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendExp(ExpMessageInfoRequest info)
        {
           
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.ExpSendFCM(command);
                return Ok(response);
            }
           
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }


        [HttpPost("send/new")]
        [SwaggerOperation("SendNew")]
        [SwaggerResponse(200, "ExpSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendNew(ExpMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.NewSendFCM(command);
                return Ok(response);
            }
           
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [HttpPost("send/bus")]
        [SwaggerOperation("SendBus")]
        [SwaggerResponse(200, "BusSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendBus(ExpMessageInfoRequest info)
        {
          
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.BusSendFCM(command);
                return Ok(response);

            }
            
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }
        [HttpPost("send/remittance")]
        [SwaggerOperation("SendRemittance")]
        [SwaggerResponse(200, "RemittanceSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendRemittance(ExpMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.RemittanceSendFCM(command);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }


        [HttpPost("send/remittance-condition")]
        [SwaggerOperation("SendRemittanceCondition")]
        [SwaggerResponse(200, "RemittanceConditionSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendRemittanceCondition(ConditionMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ConditionMessageInfoCommand>();
                var response = await FcmService.RemittanceConditionSendFCM(command);
                return Ok(response);
            }
           
            catch (Exception ex)
            {
                return BadRequest(ex);
            }


        }

    }
}
