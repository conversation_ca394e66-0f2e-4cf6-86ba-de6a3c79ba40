﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Swashbuckle.AspNetCore.Annotations;
using System.Net;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.FCM.Requests;
using Microsoft.Extensions.Options;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;

using Mapster;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.FCM
{
    [Route("[controller]")]
    [ApiController]
    public class FCMController : ControllerBase
    {

        private string error;
        private FcmWhatsappSettings _mySettings;
        //private string logRef;
        //private ClaimsModel _claimsModel;
        private FCMBackgroundServices FcmService;
        private ITamkeenLogRegister _LogRegister;
        // private readonly HelperFunctions helper;
        // private RedisCalls vRedisCalls;
        public FCMController(IOptions<FcmWhatsappSettings> appSettings, ITamkeenLogRegister logRegister)
        {
            _mySettings = appSettings.Value;
            _LogRegister = logRegister;
            FcmService = new FCMBackgroundServices(_LogRegister, _mySettings);
        }


        //FCM/FCM

        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendFCM")]
        [SwaggerOperation("SendFCM")]
        [SwaggerResponse(200, "SendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendFCM(FCMMessageInfoRequest info)
        {
            try {
                var command = info.Adapt<FCMMessageInfoCommand>();
                var response = await FcmService.SendFCM(command);
               
                return Ok(response);
            }
           
            catch (Exception ex)
            {
               

                return BadRequest(ex);
            }
        }

        [HttpPost]
        // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("GetMobileFCM")]
      //  [SwaggerOperation("GetMobileFCM")]
       
        public async Task<IActionResult> GetMobileFCM(GetMobileFCMRequest info)
        {
           
            try
            {
                var command = info.Adapt<GetMobileFCMCommand>();
                var response = await FcmService.GetMobileFCM(command);

                return Ok(response);

            }
                      
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        //FCM​/SendFCMIOS


        [HttpPost]
      
        [Route("SendFCMIOS")]
        [SwaggerOperation("SendFCMIOS")]
        [SwaggerResponse(200, "SendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendFCMIOS(FCMMessageInfoRequest info)
        {
           
            try
            {
                var command = info.Adapt<FCMMessageInfoCommand>();
                var response = await FcmService.SendFCMIOS(command);
                return Ok(response);

            }
            catch (Exception ex)
            {
                return   BadRequest(ex);
            }

        }
        ///FCM/SendFCMV2

        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("SendFCMIOSV2")]
        [SwaggerOperation("SendFCMIOSV2")]
        [SwaggerResponse(200, "SendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> SendFCMIOSV2(ExpMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.SendFCMIOSV2(command);
                return Ok(response);

            }

            catch (Exception ex)
            {
            return   BadRequest(ex);
            }

        }


        [HttpPost]
        [Route("ExpSendFCM")]
        [SwaggerOperation("ExpSendFCM")]
        [SwaggerResponse(200, "ExpSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> ExpSendFCM(ExpMessageInfoRequest info)
        {
           
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.ExpSendFCM(command);
                return Ok(response);
            }
           
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }


        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("NewSendFCM")]
        [SwaggerOperation("NewSendFCM")]
        [SwaggerResponse(200, "ExpSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> NewSendFCM(ExpMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.NewSendFCM(command);
                return Ok(response);
            }
           
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("BusSendFCM")]
        [SwaggerOperation("BusSendFCM")]
        [SwaggerResponse(200, "BusSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> BusSendFCM(ExpMessageInfoRequest info)
        {
          
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.BusSendFCM(command);
                return Ok(response);

            }
            
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }
        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("RemittanceSendFCM")]
        [SwaggerOperation("RemittanceSendFCM")]
        [SwaggerResponse(200, "RemittanceSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> RemittanceSendFCM(ExpMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ExpMessageInfoCommand>();
                var response = await FcmService.RemittanceSendFCM(command);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }


        [HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        [Route("RemittanceConditionSendFCM")]
        [SwaggerOperation("RemittanceConditionSendFCM")]
        [SwaggerResponse(200, "RemittanceConditionSendFCMResponse", typeof(IActionResult))]
        public async Task<IActionResult> RemittanceConditionSendFCM(ConditionMessageInfoRequest info)
        {
            try
            {
                var command = info.Adapt<ConditionMessageInfoCommand>();
                var response = await FcmService.RemittanceConditionSendFCM(command);
                return Ok(response);
            }
           
            catch (Exception ex)
            {
                return BadRequest(ex);
            }


        }

    }
}
