using AutoFixture;
using AutoFixture.Xunit2;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Xunit;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Tests.Controllers
{
    public class SendSmscControllerTests
    {
        private readonly Mock<SmscProxyServiceManager> _mockServiceManager;
        private readonly Mock<WhatsAppController> _mockWhatsAppController;
        private readonly SendSmscController _controller;
        private readonly Fixture _fixture;

        public SendSmscControllerTests()
        {
            _fixture = new Fixture();
            _mockServiceManager = new Mock<SmscProxyServiceManager>(Mock.Of<IEnumerable<ISmscServerGatewayProxyBackgroundService>>());
            _mockWhatsAppController = new Mock<WhatsAppController>();
            
            _controller = new SendSmscController(_mockServiceManager.Object, _mockWhatsAppController.Object);
        }

        [Theory]
        [AutoData]
        public async Task SendSmsNewVersionAsync_ValidRequest_ReturnsOkResult(SendSmsSmscRequest request)
        {
            // Arrange
            var expectedResponse = new BaseSmscResponse
            {
                resultCode = 1,
                resultMessage = "SMS sent successfully"
            };

            _mockServiceManager
                .Setup(x => x.SendSmsAsync(It.IsAny<string>(), It.IsAny<SendSmsSmscCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.SendSmsNewVersionAsync(request, CancellationToken.None);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult?.Value.Should().NotBeNull();
        }

        [Theory]
        [AutoData]
        public async Task SendSmsNewVersionAsync_ServiceThrowsException_ReturnsBadRequest(SendSmsSmscRequest request)
        {
            // Arrange
            _mockServiceManager
                .Setup(x => x.SendSmsAsync(It.IsAny<string>(), It.IsAny<SendSmsSmscCommand>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Service error"));

            // Act
            var result = await _controller.SendSmsNewVersionAsync(request, CancellationToken.None);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
        }

        [Fact]
        public async Task SendSmsSampleVersionAsync_LocalSmsWithValidNumber_ReturnsOkResult()
        {
            // Arrange
            var request = new SendSmsSmscSampleRequest
            {
                dstAdr = "96777123456",
                smsText = "Test message",
                smsDstType = SmsDstType.local
            };

            var expectedResponse = new BaseSmscResponse
            {
                resultCode = 1,
                resultMessage = "SMS sent successfully"
            };

            _mockServiceManager
                .Setup(x => x.SendSmsAsync(It.IsAny<string>(), It.IsAny<SendSmsSmscCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.SendSmsSampleVersionAsync(request, CancellationToken.None);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
        }

        [Fact]
        public async Task SendSmsSampleVersionAsync_InvalidPhoneNumber_ReturnsBadRequest()
        {
            // Arrange
            var request = new SendSmsSmscSampleRequest
            {
                dstAdr = "96799123456", // Invalid prefix
                smsText = "Test message",
                smsDstType = SmsDstType.local
            };

            // Act
            var result = await _controller.SendSmsSampleVersionAsync(request, CancellationToken.None);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
        }

        [Theory]
        [InlineData("96777123456", "YemenMobile")] // Yemen Mobile
        [InlineData("96778123456", "YemenMobile")] // Yemen Mobile
        [InlineData("96771123456", "SabaFonTrans")] // SabaFon
        [InlineData("96773123456", "YouTrans")] // YOU
        [InlineData("96770123456", "YGsm")] // Y GSM
        public async Task SendSmsSampleVersionAsync_DifferentProviders_RoutesToCorrectProvider(string phoneNumber, string expectedProvider)
        {
            // Arrange
            var request = new SendSmsSmscSampleRequest
            {
                dstAdr = phoneNumber,
                smsText = "Test message",
                smsDstType = SmsDstType.local
            };

            var expectedResponse = new BaseSmscResponse
            {
                resultCode = 1,
                resultMessage = "SMS sent successfully"
            };

            _mockServiceManager
                .Setup(x => x.SendSmsAsync(expectedProvider, It.IsAny<SendSmsSmscCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.SendSmsSampleVersionAsync(request, CancellationToken.None);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            _mockServiceManager.Verify(x => x.SendSmsAsync(expectedProvider, It.IsAny<SendSmsSmscCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task SendSmsSampleVersionAsync_NonLocalMessage_CallsWhatsApp()
        {
            // Arrange
            var request = new SendSmsSmscSampleRequest
            {
                dstAdr = "+**********",
                smsText = "Test message",
                smsDstType = SmsDstType.international
            };

            _mockWhatsAppController
                .Setup(x => x.SendToWhatsApp(It.IsAny<WhatsApp.Requests.SendToWhatsAppRequest>()))
                .ReturnsAsync(new OkObjectResult("WhatsApp message sent"));

            // Act
            var result = await _controller.SendSmsSampleVersionAsync(request, CancellationToken.None);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            _mockWhatsAppController.Verify(x => x.SendToWhatsApp(It.IsAny<WhatsApp.Requests.SendToWhatsAppRequest>()), Times.Once);
        }
    }

    // Helper classes for testing
    public enum SmsDstType
    {
        local,
        international
    }

    public class SendSmsSmscSampleRequest
    {
        public string dstAdr { get; set; } = string.Empty;
        public string smsText { get; set; } = string.Empty;
        public SmsDstType smsDstType { get; set; }
    }
}
