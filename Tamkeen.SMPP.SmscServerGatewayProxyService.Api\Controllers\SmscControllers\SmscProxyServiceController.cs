﻿using Microsoft.AspNetCore.Mvc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SmscControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SmscProxyServiceController : ControllerBase
    {
        private readonly SmscProxyServiceManager _serviceManager;

        public SmscProxyServiceController(SmscProxyServiceManager serviceManager)
        {
            _serviceManager = serviceManager;
        }

        [HttpGet("status/{transceiverSystemId}")]
        public IActionResult GetStatus(string transceiverSystemId)
        {
            try
            {
                var isRunning = _serviceManager.IsSmscServerProxyServiceRunning(transceiverSystemId);
                return Ok(new { TransceiverSystemId = transceiverSystemId, IsRunning = isRunning });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("start/{transceiverSystemId}")]
        public async Task<IActionResult> Start(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.StartSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Started" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("stop/{transceiverSystemId}")]
        public async Task<IActionResult> Stop(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.StopSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Stopped" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("restart/{transceiverSystemId}")]
        public async Task<IActionResult> Restart(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.RestartSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Restarted" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        [HttpPost("restart-client/{transceiverSystemId}")]
        public async Task<IActionResult> RestartClient(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.ReInitializeSmscServerProxyServiceClientAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Restarted" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        [HttpGet("status/all")]
        public IActionResult GetAllStatuses()
        {
            var statuses = _serviceManager.GetAllSmscServerProxyStatuses();
            return Ok(statuses);
        }
        [HttpGet("proxy-status/all")]
        public async Task<IActionResult> GetAllProxyStatuses()
        {
            var statuses = await _serviceManager.GetAllSmscProxysStatusesAsync();
            return Ok(statuses);
        }

        [HttpPost("start/all")]
        public async Task<IActionResult> StartAll(CancellationToken cancellationToken)
        {
            await _serviceManager.StartAllServicesAsync(cancellationToken);
            return Ok(new { Status = "All services started" });
        }

        [HttpPost("stop/all")]
        public async Task<IActionResult> StopAll(CancellationToken cancellationToken)
        {
            await _serviceManager.StopAllServicesAsync(cancellationToken);
            return Ok(new { Status = "All services stopped" });
        }

        [HttpPost("restart/all")]
        public async Task<IActionResult> RestartAll(CancellationToken cancellationToken)
        {
            await _serviceManager.RestartAllServicesAsync(cancellationToken);
            return Ok(new { Status = "All services restarted" });
        }
        [HttpGet("connected-clients/{transceiverSystemId}")]
        public IActionResult GetConnectedClients(string transceiverSystemId)
        {
            try
            {
                var connectedClients = _serviceManager.GetSmppServerConnectedClients(transceiverSystemId);
                return Ok(connectedClients);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        [HttpGet("connected-clients/all")]
        public IActionResult GetAllConnectedClients()
        {
            var allConnectedClients = _serviceManager.GetAllConnectedClients();
            return Ok(allConnectedClients);
        }

    }

}
