﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SmscControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SmscProxyServiceController : ControllerBase
    {
        private readonly SmscProxyServiceManager _serviceManager;

        public SmscProxyServiceController(SmscProxyServiceManager serviceManager)
        {
            _serviceManager = serviceManager;
        }

        [HttpGet("status/{transceiverSystemId}")]
        public IActionResult GetServiceStatus(string transceiverSystemId)
        {
            try
            {
                var isRunning = _serviceManager.IsSmscServerProxyServiceRunning(transceiverSystemId);
                return Ok(new { TransceiverSystemId = transceiverSystemId, IsRunning = isRunning });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("start/{transceiverSystemId}")]
        public async Task<IActionResult> StartService(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.StartSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Started" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("stop/{transceiverSystemId}")]
        public async Task<IActionResult> StopService(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.StopSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Stopped" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPost("restart/{transceiverSystemId}")]
        public async Task<IActionResult> RestartService(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.RestartSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Restarted" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        [HttpPost("restartClient/{transceiverSystemId}")]
        public async Task<IActionResult> ReInitializeSmscServerProxyClientAsync(string transceiverSystemId, CancellationToken cancellationToken)
        {
            try
            {
                await _serviceManager.ReInitializeSmscServerProxyServiceClientAsync(transceiverSystemId, cancellationToken);
                return Ok(new { TransceiverSystemId = transceiverSystemId, Status = "Restarted" });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        // New endpoint to get the status of all services
        [HttpGet("status/all")]
        public IActionResult GetAllServiceStatuses()
        {
            var statuses = _serviceManager.GetAllSmscServerProxyStatuses();
            return Ok(statuses);
        }
        [HttpGet("proxystatus/all")]
        public async Task<IActionResult> GetAllSmscProxyStatuses()
        {
            var statuses = await _serviceManager.GetAllSmscProxysStatusesAsync();
            return Ok(statuses);
        }

        // New endpoint to start all services
        [HttpPost("start/all")]
        public async Task<IActionResult> StartAllServices(CancellationToken cancellationToken)
        {
            await _serviceManager.StartAllServicesAsync(cancellationToken);
            return Ok(new { Status = "All services started" });
        }

        // New endpoint to stop all services
        [HttpPost("stop/all")]
        public async Task<IActionResult> StopAllServices(CancellationToken cancellationToken)
        {
            await _serviceManager.StopAllServicesAsync(cancellationToken);
            return Ok(new { Status = "All services stopped" });
        }

        // New endpoint to restart all services
        [HttpPost("restart/all")]
        public async Task<IActionResult> RestartAllServices(CancellationToken cancellationToken)
        {
            await _serviceManager.RestartAllServicesAsync(cancellationToken);
            return Ok(new { Status = "All services restarted" });
        }
        [HttpGet("connectedClients/{transceiverSystemId}")]
        public IActionResult GetConnectedClients(string transceiverSystemId)
        {
            try
            {
                var connectedClients = _serviceManager.GetSmppServerConnectedClients(transceiverSystemId);
                return Ok(connectedClients);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        [HttpGet("connectedClients/all")]
        public IActionResult GetAllConnectedClients()
        {
            var allConnectedClients = _serviceManager.GetAllConnectedClients();
            return Ok(allConnectedClients);
        }

    }

}
