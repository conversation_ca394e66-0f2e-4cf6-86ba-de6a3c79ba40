# دليل إصلاح مشاكل HashiCorp Vault

## المشكلة المحددة

```
VaultSharp.Core.VaultApiException: {"errors":["no handler for route \"data/data/tamkeen-smpp\". route entry not found."]}
```

## السبب الجذري

المشكلة كانت في تكوين مسار Vault. التطبيق كان يحاول الوصول إلى مسار خاطئ بسبب:

1. **MountPoint خاطئ**: كان مضبوط على `"data"` بدلاً من `"secret"`
2. **SecretPath غير صحيح**: لم يكن يتعامل مع بنية KV v2 بشكل صحيح
3. **عدم تنظيف المسار**: لم يكن يزيل `"data/"` الإضافية من المسار

## الحل المطبق

### 1. تصحيح ملف appsettings.vault.json

```json
{
  "Vault": {
    "Address": "https://vault.tamkeenye.com:8200",
    "Token": "hvs.qRYEZjHiWwVeC7Y01PNDReBR",
    "SecretPath": "data/tamkeen-smpp",     // ✅ المسار الصحيح
    "MountPoint": "secret",               // ✅ MountPoint الصحيح
    "IgnoreSslErrors": false,
    "ConnectionTimeout": 30,
    "RetryAttempts": 3,
    "RetryDelay": 5000
  }
}
```

### 2. تحسين VaultConfigurationProvider.cs

- إضافة تنظيف المسار التلقائي
- تحسين معالجة الأخطاء
- إضافة logging مفصل
- دعم SSL configuration

### 3. إضافة أدوات التشخيص

- `VaultTester.cs` - أداة شاملة لاختبار Vault
- `VaultTestController.cs` - API endpoints لاختبار Vault
- اختبارات متعددة المسارات

## كيفية التحقق من الإصلاح

### 1. اختبار عبر API

```bash
# اختبار شامل
curl http://localhost:5015/api/VaultTest/comprehensive

# اختبار سريع
curl http://localhost:5015/api/VaultTest/quick

# عرض التكوين
curl http://localhost:5015/api/VaultTest/config

# اختبار مسار محدد
curl "http://localhost:5015/api/VaultTest/test-path?path=data/tamkeen-smpp"
```

### 2. اختبار عبر curl المباشر

```bash
# الأمر الصحيح
curl -H "X-Vault-Token:hvs.qRYEZjHiWwVeC7Y01PNDReBR" \
     https://vault.tamkeenye.com:8200/v1/secret/data/data/tamkeen-smpp

# أو للحصول على محاكاة الأمر
curl http://localhost:5015/api/VaultTest/simulate-curl
```

### 3. فحص اللوجات

```bash
# تشغيل التطبيق مع logging مفصل
dotnet run --environment=Development

# البحث عن رسائل Vault في اللوجات
grep -i "vault" logs/application.log
```

## بنية Vault المتوقعة

```
vault/
├── secret/                    # Mount Point
│   └── data/                  # KV v2 structure
│       └── data/              # Data path
│           └── tamkeen-smpp/  # Secret path
│               ├── DB_SERVER
│               ├── DB_PASSWORD
│               ├── SMSC_YGSM_SYSTEM_ID
│               └── ...
```

## متغيرات البيئة المطلوبة

```bash
# في production
export VAULT_TOKEN="your-actual-token"
export VAULT_ADDRESS="https://vault.tamkeenye.com:8200"

# في development
export ASPNETCORE_ENVIRONMENT="Development"
```

## أفضل الممارسات

### 1. أمان التوكن

```bash
# لا تضع التوكن في ملفات التكوين في production
# استخدم متغيرات البيئة أو خدمات إدارة الأسرار

# في appsettings.vault.json
"Token": "${VAULT_TOKEN}"

# في البيئة
export VAULT_TOKEN="hvs.actual-token-here"
```

### 2. معالجة الأخطاء

```csharp
// التطبيق الآن يتعامل مع فشل Vault بدون إيقاف التشغيل
try
{
    await LoadVaultSecrets();
}
catch (VaultApiException ex)
{
    _logger.LogWarning("Vault unavailable, using fallback configuration");
    // Continue with default configuration
}
```

### 3. مراقبة الصحة

```csharp
// إضافة health check لـ Vault
services.AddHealthChecks()
    .AddCheck<VaultHealthCheck>("vault");
```

## استكشاف الأخطاء الشائعة

### خطأ: "route entry not found"
**السبب**: مسار خاطئ أو mount point خاطئ
**الحل**: تحقق من `MountPoint` و `SecretPath` في التكوين

### خطأ: "permission denied"
**السبب**: التوكن لا يملك صلاحيات كافية
**الحل**: تحقق من policies المرتبطة بالتوكن

### خطأ: "connection timeout"
**السبب**: مشاكل في الشبكة أو SSL
**الحل**: تحقق من `IgnoreSslErrors` أو إعدادات الشبكة

### خطأ: "token expired"
**السبب**: التوكن منتهي الصلاحية
**الحل**: تجديد التوكن أو استخدام renewable token

## اختبار التكامل

```csharp
[Test]
public async Task VaultConfiguration_ShouldLoadSecrets()
{
    // Arrange
    var configuration = BuildConfigurationWithVault();
    
    // Act
    var dbConnection = configuration.GetConnectionString("DefaultConnection");
    
    // Assert
    Assert.IsNotNull(dbConnection);
    Assert.DoesNotContain("${VAULT:", dbConnection);
}
```

## مراقبة الأداء

```csharp
// إضافة metrics لـ Vault operations
_metrics.CreateCounter("vault_requests_total")
    .WithTag("operation", "read_secret")
    .WithTag("status", "success")
    .Add(1);
```

## الخطوات التالية

1. **تطبيق الحل**: استخدم الملفات المحدثة
2. **اختبار شامل**: شغل جميع اختبارات Vault
3. **مراقبة الإنتاج**: تأكد من عمل Vault في production
4. **توثيق العمليات**: وثق إجراءات إدارة Vault
5. **تدريب الفريق**: تأكد من فهم الفريق لـ Vault

## جهات الاتصال للدعم

- **Vault Admin**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>

---

**ملاحظة**: هذا الدليل يحل المشكلة المحددة ويوفر إطار عمل شامل لإدارة Vault في المستقبل.
