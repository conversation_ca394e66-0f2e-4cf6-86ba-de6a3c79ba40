using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models;

public class SmscRabbitMQSendSmsService : IDisposable
{
    private readonly IConnection _connection;
    private readonly IModel _channel;
    private readonly RabbitMQSettings _settings;

    public SmscRabbitMQSendSmsService(RabbitMQSettings settings)
    {
        _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        var factory = new ConnectionFactory() { HostName = settings.hostName };
        _connection = factory.CreateConnection();
        _channel = _connection.CreateModel();
        _channel.QueueDeclare(queue: "smsc_queue", durable: false, exclusive: false, autoDelete: false, arguments: null);
    }

    public async Task StoreMessageAsync(SmscSMSMessage message)
    {
        var body = Encoding.UTF8.GetBytes(message.ToString());
        var props = _channel.CreateBasicProperties();
        props.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
        _channel.BasicPublish(exchange: "", routingKey: "smsc_queue", basicProperties: props, body: body);
        await Task.CompletedTask;
    }

    public async Task<List<SmscSMSMessage>> RetrieveMessagesAsync()
    {
        var messages = new List<SmscSMSMessage>();
        var consumer = new EventingBasicConsumer(_channel);

        consumer.Received += (model, ea) =>
        {
            var timestamp = ea.BasicProperties?.Timestamp.UnixTime ?? 0;
            if (timestamp > 0)
            {
                var messageTime = DateTimeOffset.FromUnixTimeSeconds(timestamp).UtcDateTime;
                var maxAge = TimeSpan.FromMinutes(_settings.MaxMessageAgeMinutes);
                if (DateTime.UtcNow - messageTime > maxAge)
                {
                    return; // skip expired messages
                }
            }

            var body = ea.Body.ToArray();
            var serializedMessage = Encoding.UTF8.GetString(body);

            // Modify this line to match the available data in the serializedMessage.
            var message = SmscSMSMessage.FromString(serializedMessage); // Deserialize the message
            messages.Add(message);
        };

        _channel.BasicConsume(queue: "smsc_queue", autoAck: true, consumer: consumer);
        await Task.Delay(1000); // Wait for messages to be retrieved
        return messages;
    }

    public void Dispose()
    {
        _channel.Close();
        _connection.Close();
    }
}
