﻿using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer;

public class SmscRabbitMQSendSmsService
{
    private readonly IConnection _connection;
    private readonly IModel _channel;

    public SmscRabbitMQSendSmsService()
    {
        var factory = new ConnectionFactory() { HostName = "localhost" };
        _connection = factory.CreateConnection();
        _channel = _connection.CreateModel();
        _channel.QueueDeclare(queue: "smsc_queue", durable: false, exclusive: false, autoDelete: false, arguments: null);
    }

    public async Task StoreMessageAsync(SmscSMSMessage message)
    {
        var body = Encoding.UTF8.GetBytes(message.ToString());
        _channel.BasicPublish(exchange: "", routingKey: "smsc_queue", basicProperties: null, body: body);
        await Task.CompletedTask;
    }

    public async Task<List<SmscSMSMessage>> RetrieveMessagesAsync()
    {
        var messages = new List<SmscSMSMessage>();
        var consumer = new EventingBasicConsumer(_channel);

        consumer.Received += (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var serializedMessage = Encoding.UTF8.GetString(body);

            // Modify this line to match the available data in the serializedMessage.
            var message = SmscSMSMessage.FromString(serializedMessage); // Deserialize the message
            messages.Add(message);
        };

        _channel.BasicConsume(queue: "smsc_queue", autoAck: true, consumer: consumer);
        await Task.Delay(1000); // Wait for messages to be retrieved
        return messages;
    }


    public void Dispose()
    {
        _channel.Close();
        _connection.Close();
    }
}
