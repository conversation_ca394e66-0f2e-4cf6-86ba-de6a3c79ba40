﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using StackExchange.Redis;
using System.Threading;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck
{
    public class RedisHealthCheck : IHealthCheck
    {
        private readonly string _redisConnectionString;

        public RedisHealthCheck(IConfiguration configuration)
        {
            _redisConnectionString = configuration.GetSection("AppOptions")["RedisConnectionString"];
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var connection = await ConnectionMultiplexer.ConnectAsync(_redisConnectionString);
                if (connection.IsConnected)
                {
                    return HealthCheckResult.Healthy("Redis is connected");
                }
                else
                {
                    return HealthCheckResult.Unhealthy("Redis is not connected");
                }
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy($"Redis connection failed: {ex.Message}");
            }
        }
    }
}
