﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.Logging;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.Inetlab.SMPP;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.Inetlab.SMPP.Builders;
using System.Globalization;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Microsoft.Extensions.Configuration;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Tamkeen.Inetlab.SMPP.Parameters;
using Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RateLimiting;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer
{
    public class SmscServerProxy : IDisposable
    {
        public SmppServer SmppServer { get; private set; }
        private MessageComposer _messageComposer;
        private bool _disposed = false;
        public SmscServerProxyClient SmscServerProxyClient { get; private set; }
        private readonly SmscServerGatewayProxySettings _smscServerGatewayProxySettings;
        public IPEndPoint _serverIPEndPoint { get; private set; }
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger _logger;
        private readonly IRateLimiter _rateLimiter;
        // Define a dictionary to map mode strings to ConnectionMode enum values
        private readonly Dictionary<string, ConnectionMode> modeMap
            = new Dictionary<string, ConnectionMode>
            {
                { "transceiver", ConnectionMode.Transceiver },
                { "receiver", ConnectionMode.Receiver },
                { "transmitter", ConnectionMode.Transmitter }
            };
        public SmscServerProxy(IServiceProvider serviceProvider)
            : this(serviceProvider, serviceProvider.GetRequiredService<SmscServerGatewayProxySettings>())
        {
        }
        public SmscServerProxy(IServiceProvider serviceProvider, SmscServerGatewayProxySettings smscServerGatewayProxySettings)
        {
            //_smscServerGatewayProxySettings = new SmscServerGatewayProxySettings();
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(_serviceProvider));
            //smscServerSetting.GetSection($"SmscClientSettings").Bind(_smscServerGatewayProxySettings.SmscClientSettings);
            //smscClientSettings.GetSection($"RabbitMQSettings").Bind(_smscServerGatewayProxySettings.RabbitMQSettings);
            //smscServerSetting.GetSection($"SmscServerSetting").Bind(_smscServerGatewayProxySettings.SmscServerSetting);
            _smscServerGatewayProxySettings = smscServerGatewayProxySettings ?? throw new ArgumentNullException(nameof(_smscServerGatewayProxySettings)); ;
            _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
            _rateLimiter = serviceProvider.GetService<IRateLimiter>() ?? throw new ArgumentNullException(nameof(IRateLimiter));

            LogManager.SetLoggerFactory(new SmscSeriLoggerFactoryAdapter(_logger, LogLevel.Debug));
            _serverIPEndPoint = new IPEndPoint(IPAddress.Any, Convert.ToInt32(_smscServerGatewayProxySettings.smscServerSetting.SmscServerPort));

            //_smppProxyServer = new SmppServer(_serverIPEndPoint);
            //if (_smscServerGatewayProxySettings.smscClientSettings.enable)
            //{
            //    //ConnectionMode mode = ConnectionMode.None;

            //    if (_smscServerGatewayProxySettings.smscClientSettings.mode != null && modeMap.ContainsKey(_smscServerGatewayProxySettings.smscClientSettings.mode))
            //    {
            //        string lowercasedMode = _smscServerGatewayProxySettings.smscClientSettings.mode.ToLowerInvariant(); // Convert mode to lowercase for case-insensitive comparison
            //        _smscServerGatewayProxySettings.smscClientSettings.connectionMode = modeMap[lowercasedMode];
            //    }
            //    //SmscRabbitMQSendSmsServiceVersion2 smscRabbitMQSendSmsServiceVersion2 = _smscRabbitMQSendSmsServiceVersion2s.FirstOrDefault(s => s._queueName == smscClientSetting.transceiverSystemId);
            //    _smscServerProxyClient = new SmscServerProxyClient(_smppProxyServer, _logger, _smscServerGatewayProxySettings, serviceProvider);
            //    //_smscServerProxyClient.RunAsync().ConfigureAwait(false);

            //}
        }
        public async Task<bool> ReInitializeSmscServerProxyClientAsync()
        {
            try
            {
                SmscServerProxyClient.Dispose();
                await Task.Delay(TimeSpan.FromSeconds(5)); // Wait for 5 seconds before trying to reinitialize the client
                SmscServerProxyClient = new SmscServerProxyClient(SmppServer, _logger, _smscServerGatewayProxySettings, _serviceProvider);
                await SmscServerProxyClient.RunAsync();
                SmscServerProxyClient._proxyServerClient = SmppServer.ConnectedClients.FirstOrDefault();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
            
        }
        public async Task StartAsync()
        {
            try
            {
                //Create message composer.It helps to get full text of the concatenated message in the method OnFullMessageReceived
                #region MessageComposer
                _messageComposer = new MessageComposer();
                _messageComposer.evFullMessageReceived += OnFullMessageReceived;
                _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;
                #endregion

                SmppServer = new SmppServer(_serverIPEndPoint);
                if (_smscServerGatewayProxySettings.smscClientSettings.enable)
                {
                    //ConnectionMode mode = ConnectionMode.None;

                    if (_smscServerGatewayProxySettings.smscClientSettings.mode != null && modeMap.ContainsKey(_smscServerGatewayProxySettings.smscClientSettings.mode.ToLower()))
                    {
                        string lowercasedMode = _smscServerGatewayProxySettings.smscClientSettings.mode.ToLowerInvariant(); // Convert mode to lowercase for case-insensitive comparison
                        _smscServerGatewayProxySettings.smscClientSettings.connectionMode = modeMap[lowercasedMode];
                    }
                    //SmscRabbitMQSendSmsServiceVersion2 smscRabbitMQSendSmsServiceVersion2 = _smscRabbitMQSendSmsServiceVersion2s.FirstOrDefault(s => s._queueName == smscClientSetting.transceiverSystemId);
                    SmscServerProxyClient = new SmscServerProxyClient(SmppServer, _logger, _smscServerGatewayProxySettings, _serviceProvider);
                    //_smscServerProxyClient.RunAsync().ConfigureAwait(false);

                }
                SmppServer.evClientBind += (sender, client, data) =>
                {
                    _logger.Information(SmscNotificationMessages.ClientBind, client.RemoteEndPoint, data.SystemId, data.Password);
                    if (data.SystemId == string.Empty || SmscServerProxyClient._clientConnectionSettings.transceiverSystemId != data.SystemId || data.SystemId == "TamOT")
                    {
                        data.Response.Header.Status = CommandStatus.ESME_RINVSYSID;
                        _logger.Information(SmscNotificationMessages.ClientBindInvalidSystemId, client.RemoteEndPoint, data.SystemId);
                        return;
                    }
                    if (data.Password == string.Empty)
                    {
                        _logger.Information(SmscNotificationMessages.ClientBindWithInvalidPassword, client.RemoteEndPoint);

                        data.Response.Header.Status = CommandStatus.ESME_RINVPASWD;
                        return;
                    }
                    //Reject the client if the bind is not same as the proxy client
                    //ToDo: Make sure the telco bind with same as the proxy client, if it does that enable this condation
                    //if (data.Header.Command.ToString().Replace("Bind", "").ToLower() != _smscServerGatewayProxySettings.smscClientSettings.connectionMode.ToString().ToLower())
                    //{
                    //    _logger.Information(SmscNotificationMessages.BindMissMatch, data.Header.Command.ToString().Replace("Bind", ""), _smscServerGatewayProxySettings.smscClientSettings.connectionMode.ToString());
                    //    data.Response.Header.Status = CommandStatus.ESME_RINVBNDSTS;
                    //    return;
                    //}
                    var matchedClient = SmppServer.ConnectedClients.FirstOrDefault(c => c.SystemID == data.SystemId);
                    if (matchedClient != null)
                    {
                        SmscServerProxyClient._proxyServerClient = matchedClient;
                    }
                    else
                    {
                        _logger.Warning(SmscErrorMessages.NoMatchingClient, data.SystemId);
                    }

                    _logger.Information(SmscNotificationMessages.ClientBind, client.RemoteEndPoint, data.SystemId, data.Password);

                };
                SmppServer.evClientDisconnected += (sender, client) =>
                {

                };
                SmppServer.evClientConnected += (sender, client) =>
                {

                    client.ReceiveTaskScheduler = new WorkersTaskScheduler(10);
                    //client.ReceiveBufferSize = 30 * 1024 * 1024;
                    //client.SendBufferSize = 30 * 1024 * 1024;

                };
                SmppServer.evClientSubmitSm += server_evClientSubmitSm;
                SmppServer.evClientSubmitSm += WhenServerReceivesPDU;
                SmppServer.Name = $"Tamkeen {_smscServerGatewayProxySettings.smscClientSettings.transceiverSystemId} Server";
            }
            catch (Exception)
            {

                throw;
            }
            await Task.Delay(100);
        }//public SmscServerProxy(IServiceProvider serviceProvider)
        //{
        //    _smscServerGatewayProxySettings = serviceProvider.GetRequiredService<IOptionsSnapshot<SmscServerGatewayProxySettings>>().Value;
        //    _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
        //    //Create message composer.It helps to get full text of the concatenated message in the method OnFullMessageReceived
        //    #region MessageComposer
        //    _messageComposer = new MessageComposer();
        //    _messageComposer.evFullMessageReceived += OnFullMessageReceived;
        //    _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;
        //    #endregion

        //    //_whatsAppUrl = appsettings.whatsUrl??"https://172.16.50.231:9556";
        //    //_resource = appsettings.resource?? "/SendToWhatsApp";


        //    //#if DEBUG
        //    //LogManager.SetLoggerFactory(new ConsoleLogFactory(Inetlab.SMPP.Logging.LogLevel.Debug)); //log to console
        //    //#endif

        //    //#if !DEBUG
        //    //LogManager.SetLoggerFactory(new ConsoleLogFactory(LogLevel.Info)); //log to console
        //    //LogManager.SetLoggerFactory(new SmscSeriLoggerFactoryAdapter(_logger, LogLevel.Info));
        //    LogManager.SetLoggerFactory(new SmscSeriLoggerFactoryAdapter(_logger, LogLevel.Debug));
        //    //#endif
        //    _serverIPEndPoint = new IPEndPoint(IPAddress.Any, Convert.ToInt32(_smscServerGatewayProxySettings.smscServerSetting.SmscServerPort));

        //    _smppProxyServer = new SmppServer(_serverIPEndPoint);
        //    //ConnectionMode mode = ConnectionMode.Transmitter;
        //    //foreach (var smscClientSetting in siteSettings.smscClientSettings)
        //    //{
        //    if (_smscServerGatewayProxySettings.smscClientSettings.enable)
        //    {
        //        //ConnectionMode mode = ConnectionMode.None;

        //        if (_smscServerGatewayProxySettings.smscClientSettings.mode != null && modeMap.ContainsKey(_smscServerGatewayProxySettings.smscClientSettings.mode))
        //        {
        //            string lowercasedMode = _smscServerGatewayProxySettings.smscClientSettings.mode.ToLowerInvariant(); // Convert mode to lowercase for case-insensitive comparison
        //            _smscServerGatewayProxySettings.smscClientSettings.connectionMode = modeMap[lowercasedMode];
        //        }
        //        //SmscRabbitMQSendSmsServiceVersion2 smscRabbitMQSendSmsServiceVersion2 = _smscRabbitMQSendSmsServiceVersion2s.FirstOrDefault(s => s._queueName == smscClientSetting.transceiverSystemId);
        //        _smscServerProxyClient = new SmscServerProxyClient(_smppProxyServer, _logger, _smscServerGatewayProxySettings, serviceProvider);
        //        //_smscServerProxyClient.RunAsync().ConfigureAwait(false);
        //    //    _smscServerProxyClient.RunAsync(
        //    //    new Uri(_smscServerGatewayProxySettings.smscClientSettings.clientUri),
        //    //    _smscServerGatewayProxySettings.smscClientSettings.connectionMode,
        //    //    _smscServerGatewayProxySettings.smscClientSettings.transceiverSystemId,
        //    //    _smscServerGatewayProxySettings.smscClientSettings.password,
        //    //    _smscServerGatewayProxySettings.smscClientSettings.connectionRecovery
        //    //).ConfigureAwait(false);

        //    }
        //    //this.Run().ConfigureAwait(false);
        //}

        public async Task StopAsync()
        {
            if (SmppServer != null)
            {
                await SmppServer.StopAsync();
            }
        }

        public async Task<IBaseSmscResponse> Run()
        {
            if (SmppServer == null)
            {
                await StartAsync();
            }
            #region mainServer          
            try
            {
                if (SmppServer != null)
                {
                    if (!SmppServer.IsRun)
                    {
                        await SmppServer.StartAsync();
                        return await SmscGeneralUtilities.MakeResponse(1, $"The server {_serverIPEndPoint.Address}:{_serverIPEndPoint.Port} run successfully");
                    }
                }
                else
                {
                    return await SmscGeneralUtilities.MakeResponse(-1, "The server doesn't run");
                }
                //if (!_smscServerProxyClient.IsConnected)
                //{
                //    await _smscServerProxyClient.RunAsync();
                //}
                //// Get the type of SmppServer
                //Type type = _smppServer.GetType();

                //// Get the field info for the _serverIPEndPoint field
                //FieldInfo fieldInfo = type.GetField("_serverIPEndPoint", BindingFlags.NonPublic | BindingFlags.Instance);

                //// Read the value of the _serverIPEndPoint field
                //IPEndPoint endPoint = (IPEndPoint)fieldInfo.GetValue(_smppServer);

                return await SmscGeneralUtilities.MakeResponse(1, $"The server {_serverIPEndPoint.Address}:{_serverIPEndPoint.Port} run successfully");

            }
            catch (Exception)
            {
                return await SmscGeneralUtilities.MakeResponse(-1, "The server doesn't run");
            }
            #endregion
        }

        #region Message

        private void OnFullMessageTimeout(object sender, MessageEventHandlerArgs args)
        {
            throw new NotImplementedException();
        }

        private /*async*/ void OnFullMessageReceived(object sender, MessageEventHandlerArgs args)
        {
            try
            {



                SubmitSm pdu = args.GetFirst<SubmitSm>();
                var Mob = pdu.DestinationAddress;
#if DEBUG
                //Mob= Mob == null ? null :
                Mob.Address = "737333203";
#endif
                var Msg = args.Text;
                var ShortCode = pdu.SourceAddress;
                //using (var connection = new System.Data.SqlClient.SqlConnection(cs))
                //{
                //    var affectedRows = connection.Execute(sql, new { MS = Msg.ToString(), MB = Mob.ToString(), SC = ShortCode.ToString() });
                //}

                SmppServer.Logger.Info(string.Format("message Received: {0} {1} ", Msg, DateTime.Now.ToString() + Environment.NewLine));

                try
                {
                    //RestFulClient client = new RestFulClient(_logger);
                    //string cleaned = Msg.Replace("\n", "").Replace("\r", "");
                    //Msg = cleaned;
                    ////if (Msg.ToLower().Contains("pin") || Msg.ToLower().Contains("الرمز"))
                    //{
                    //    //var lo = /*await*/ vRestFulClient.CallApiAsync<Object, SendWhatAppDto>(_whatsAppUrl, _resource,
                    //    ////var lo = await vRestFulClient.CallApiAsync<Object, SendWhatAppDto>("https://www.tamkeen.com.ye:5059", "WhatsApp/SendToWhatsApp",
                    //    //new SendWhatAppDto { To = Mob.ToString(), Message = string.Format("{0}", Msg) }, false, 30000, new List<KeyValueDto> {
                    //    //    //new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CRXhwdHJpYXRlIiwibmJmIjoxNjYyODEzMTM3LCJleHAiOjE4MjA1Nzk1MzcsImlhdCI6MTY2MjgxMzEzNywiaXNzIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIiwiYXVkIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIn0.1Iqu__xTyKPrhl_tQ-tIfkF4kAiUcLpVJ7Op7_GVClw" } });//780077268
                    //    //    new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CQnVzaW5lc3MiLCJuYmYiOjE2NjMxNjM5NjMsImV4cCI6MTk3ODc4MzE2MywiaWF0IjoxNjYzMTYzOTYzLCJpc3MiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2giLCJhdWQiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2gifQ.Du4t1tXuxnxBtf7GcvYkQWfgcDzMvbVcAW7EDZniqNU" } });
                    //    var lo = /*await*/ client.CallApi<Object, SendWhatAppDto>(_whatsAppUrl, _resource,
                    //    //var lo = await vRestFulClient.CallApiAsync<Object, SendWhatAppDto>("https://www.tamkeen.com.ye:5059", "WhatsApp/SendToWhatsApp",
                    //    new SendWhatAppDto { To = Mob.ToString(), Message = string.Format("{0}", Msg) }, false, 30000, new List<KeyValueDto> {
                    //        //new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CRXhwdHJpYXRlIiwibmJmIjoxNjYyODEzMTM3LCJleHAiOjE4MjA1Nzk1MzcsImlhdCI6MTY2MjgxMzEzNywiaXNzIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIiwiYXVkIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIn0.1Iqu__xTyKPrhl_tQ-tIfkF4kAiUcLpVJ7Op7_GVClw" } });//780077268
                    //        new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CQnVzaW5lc3MiLCJuYmYiOjE2NjMxNjM5NjMsImV4cCI6MTk3ODc4MzE2MywiaWF0IjoxNjYzMTYzOTYzLCJpc3MiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2giLCJhdWQiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2gifQ.Du4t1tXuxnxBtf7GcvYkQWfgcDzMvbVcAW7EDZniqNU" } });
                    //    ////if (lo != null)
                    //    ////{
                    //    ////    _smppServer.Logger.Info(string.Format("WhatsApp Response: {0}", @lo.ToString().Replace("{", "{{").Replace("}", "}}")));
                    //    ////}
                    //}

                }
                catch (Exception ex)
                {
                    SmppServer.Logger.Error(string.Format(SmscErrorMessages.ExceptionReceived, ex.Message, DateTime.Now.ToString() + Environment.NewLine));
                };
            }
            catch (Exception ex)
            {
                SmppServer.Logger.Error(string.Format(SmscErrorMessages.ExceptionReceived, ex.Message, DateTime.Now.ToString() + Environment.NewLine));
            }
            SmppServer.Logger.Info(string.Format(SmscNotificationMessages.SmsReceived, args.Text));
        }

        #endregion
        private async void WhenServerReceivesPDU(object sender, SmppServerClient serverClient, SubmitSm data)
        {
            //var smBuilder = SMS.ForSubmit()
            //                .From(data.DestinationAddress)
            //                .To(data.SourceAddress)
            //                .Coding(data.DataCoding)
            //                .Concatenation(ConcatenationType.UDH8bit, _proxyClient.SequenceGenerator.NextReferenceNumber())
            //                .Set(m => m.MessageType = MessageTypes.SMEDeliveryAcknowledgement)
            //                .Text(new Receipt
            //                {
            //                    DoneDate = DateTime.Now,
            //                    State = MessageState.Delivered,
            //                    ErrorCode = "0",
            //                    SubmitDate = DateTime.Now,
            //                    Text = messageText.Substring(0, Math.Min(20, messageText.Length))
            //                }.ToString());
            //if (_clientConnectionSettings.optionalParameterList != null)
            //{
            //    byte[] bytesarr = Encoding.UTF8.GetBytes(_clientConnectionSettings.optionalParameterList.value);
            //    // Assuming _clientConnectionSettings.optionalParameterList.tag is a string
            // if (ushort.TryParse(_smppClient._clientConnectionSettings.optionalParameterList.tag, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort))

            //    {
            //        smBuilder.AddParameter(tagAsUshort, bytesarr);
            //    }
            //}
            //unwanted code: SendDeliveryReceipt when required
            //if (data.RegisteredDelivery == 1)
            //{

            //    //Send Delivery Receipt when required
            //    await serverClient.DeliverAsync(
            //        SMS.ForDeliver()
            //            .From(data.SourceAddress)
            //            .To(data.DestinationAddress)
            //            .Coding(data.DataCoding)
            //            .Receipt(new Receipt
            //            {
            //                DoneDate = DateTime.Now,
            //                State = MessageState.Delivered,
            //                MessageId = data.Response.MessageId,
            //                ErrorCode = "8",
            //                SubmitDate = DateTime.Now
            //            }
            //            )
            //    )
            //    .ConfigureAwait(false);
            //}
        }

        private async void server_evClientSubmitSm(object sender, SmppServerClient client, SubmitSm data)
        {
            //long messageId = Interlocked.Increment(ref _messageIdCounter);
            // You can set your own MessageId
            //data.Response.MessageId = messageId.ToString();

            //_log.Info("Client {0} sends message From:{1}, To:{2}, Text: {3}",
            //client.RemoteEndPoint, data.SourceAddress, data.DestinationAddress,
            //data.GetMessageText(client.EncodingMapper));
            data.SourceAddress.Address = SmscSenderConfiguration.SenderId;
            switch (data.DestinationAddress.Address.Length)
            {
                //"712345678"
                case 9:
                    data.DestinationAddress.Address = SmscSenderConfiguration.SenderYemenInternational + data.DestinationAddress.Address;
                    break;
                //"+967712345678"
                case 13:
                    data.DestinationAddress.Address = data.DestinationAddress.Address.Replace("+967", SmscSenderConfiguration.SenderYemenInternational);
                    break;
                //"00967712345678"
                case 14:
                    data.DestinationAddress.Address = data.DestinationAddress.Address.Remove(0, 2);
                    break;
                //"967712345678"
                default:
                    break;
            }
            if (Regex.IsMatch(data.DestinationAddress.Address, SmscServerProxyClient._clientConnectionSettings.mobilePattern))
            {
                if (!await _rateLimiter.PermitAsync("submit"))
                {
                    _logger.Warning("Rate limit exceeded for submit_sm");
                    return;
                }
                if (SmscServerProxyClient._proxyClient.IsClientConnected)
                {
                    if (_smscServerGatewayProxySettings.smscClientSettings.optionalParameterList != null)
                    {
                        byte[] bytesarr = Encoding.UTF8.GetBytes(_smscServerGatewayProxySettings.smscClientSettings.optionalParameterList.value);
                        // Assuming _clientConnectionSettings.optionalParameterList.tag is a string
                        if (ushort.TryParse(_smscServerGatewayProxySettings.smscClientSettings.optionalParameterList.tag, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort))
                        {
                            TLV tlv = new TLV(tagAsUshort, bytesarr);
                            data.Parameters.Add(tlv);
                        }
                    }
                    /*Task.Run(() =>*/
                    await SmscServerProxyClient.ForwardSubmitSm(client, data);
                    //_gatewayYGsmClient._messageComposer.AddMessage(data);
                }
                else if (!SmscServerProxyClient._proxyClient.ConnectionRecovery && SmscServerProxyClient._isConnectedOnce)
                {
                    await SmscServerProxyClient.RunAsync();
                    //await _smscServerProxyClient.RunAsync(
                    //new Uri(_smscServerProxyClient._clientConnectionSettings.clientUri),
                    //_smscServerProxyClient._clientConnectionSettings.connectionMode,
                    //_smscServerProxyClient._clientConnectionSettings.systemId,
                    //_smscServerProxyClient._clientConnectionSettings.password,
                    //_smscServerProxyClient._clientConnectionSettings.connectionRecovery
                    // ).ConfigureAwait(false);

                    if (SmscServerProxyClient._proxyClient.IsClientConnected)
                    {
                        await Task.Run(() => SmscServerProxyClient.ForwardSubmitSm(client, data));
                        //_gatewayYGsmClient._messageComposer.AddMessage(data);
                    }
                }
                else
                {
                    await Task.Run(() => SmscServerProxyClient._smscRabbitMQSendSmsService.ProcessInteraptedSubmitSmAsync(data, client));
                }

            }
            //else if (Regex.IsMatch(data.DestinationAddress.Address, _gatewayTransYouClient._clientConnectionSettings.mobilePattern))
            //{
            //    if (_gatewayTransYouClient._client.IsClientConnected)
            //    {
            //        Task.Run(() => _gatewayTransYouClient.ForwardSubmitSm(client, data));
            //        //_gatewayYemenMobileClient._messageComposer.AddMessage(data);
            //    }
            //}
            //else if (Regex.IsMatch(data.DestinationAddress.Address, _SabaFonTransClientConnection.mobilePattern))
            //{
            //    if (_gatewaySabaFonTransClient._client.IsClientConnected)
            //    {
            //        Task.Run(() => _gatewaySabaFonTransClient.ForwardSubmitSm(client, data));
            //        //_gatewayYemenMobileClient._messageComposer.AddMessage(data);
            //    }
            //}
            //else if (Regex.IsMatch(data.DestinationAddress.Address, _YemenMobileClientConnection.mobilePattern))
            //{
            //    if (_gatewayYemenMobileClient._client.IsClientConnected)
            //    {
            //        Task.Run(() => _gatewayYemenMobileClient.ForwardSubmitSm(client, data));
            //        //_gatewayYemenMobileClient._messageComposer.AddMessage(data);
            //    }
            //}
            else
            {
                _messageComposer.AddMessage(data);
            }




            // Set unsuccess response status
            //data.Response.Status = CommandStatus.ESME_RSUBMITFAIL;

            //unwanted code: Handle other message types
            //if (data.SMSCReceipt != SMSCDeliveryReceipt.NotRequested)
            //{
            //    //Send Delivery Receipt when required

            //    string messageText = data.GetMessageText(client.EncodingMapper);

            //    var dlrBuilder = SMS.ForDeliver()
            //        .From(data.DestinationAddress)
            //        .To(data.SourceAddress)
            //        .Receipt(new Receipt
            //        {
            //            DoneDate = DateTime.Now,
            //            State = MessageState.Delivered,
            //            MessageId = data.Response.MessageId,
            //            ErrorCode = "9",
            //            SubmitDate = DateTime.Now,
            //            Text = messageText.Substring(0, Math.Min(20, messageText.Length))
            //        });
            //    if (_smscServerGatewayProxySettings.smscClientSettings.optionalParameterList != null)
            //    {
            //        byte[] bytesarr = Encoding.UTF8.GetBytes(_smscServerGatewayProxySettings.smscClientSettings.optionalParameterList.value);
            //        // Assuming _clientConnectionSettings.optionalParameterList.tag is a string
            //        if (ushort.TryParse(_smscServerGatewayProxySettings.smscClientSettings.optionalParameterList.tag, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort))
            //        {
            //            dlrBuilder.AddParameter(tagAsUshort, bytesarr);
            //        }
            //    }
            //    if (data.DataCoding == DataCodings.UCS2)
            //    {
            //        //short_message field cannot contain user data longer than 255 octets,
            //        //therefore for UCS2 encoding we are sending DLR in message_payload parameter
            //        dlrBuilder.MessageInPayload();
            //    }

            //    client.DeliverAsync(dlrBuilder).ConfigureAwait(false);
            //}



        }

        #region IDisposable
        // Implement IDisposable
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // Dispose managed resources
                SmppServer?.Dispose();
                SmscServerProxyClient?.Dispose();

                _messageComposer.evFullMessageReceived -= OnFullMessageReceived;
                _messageComposer.evFullMessageTimeout -= OnFullMessageTimeout;
            }

            // Dispose unmanaged resources (if any)

            _disposed = true;
            //_initialized = false;  // Allow reinitialization
        }

        // Destructor
        ~SmscServerProxy()
        {
            Dispose(false);
        }
        #endregion
    }

}
