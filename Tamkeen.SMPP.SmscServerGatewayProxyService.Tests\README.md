# Tamkeen SMPP Gateway Proxy Service - Unit Tests

## نظرة عامة

هذا المشروع يحتوي على اختبارات الوحدة الشاملة لخدمة Tamkeen SMPP Gateway Proxy Service. الاختبارات مصممة لضمان جودة الكود وموثوقية النظام.

## هيكل الاختبارات

```
Tests/
├── Controllers/
│   └── SendSmscControllerTests.cs      # اختبارات API Controllers
├── Services/
│   ├── SmscProxyServiceManagerTests.cs # اختبارات إدارة الخدمات
│   └── SmscRoutingServiceTests.cs      # اختبارات توجيه الرسائل
├── Integration/
│   └── ApiIntegrationTests.cs          # اختبارات التكامل
└── Helpers/
    └── TestHelpers.cs                  # أدوات مساعدة للاختبارات
```

## التقنيات المستخدمة

### إطار عمل الاختبارات
- **xUnit** - إطار عمل الاختبارات الرئيسي
- **FluentAssertions** - مكتبة assertions أكثر قابلية للقراءة
- **Moq** - مكتبة mocking للكائنات الوهمية
- **AutoFixture** - إنشاء بيانات اختبار تلقائياً

### اختبارات التكامل
- **Microsoft.AspNetCore.Mvc.Testing** - اختبار Web APIs
- **WebApplicationFactory** - إعداد بيئة اختبار متكاملة

## كيفية تشغيل الاختبارات

### المتطلبات الأساسية
```bash
# التأكد من وجود .NET 8.0 SDK
dotnet --version

# يجب أن يكون الإصدار 8.0.x أو أحدث
```

### تشغيل جميع الاختبارات
```bash
# من مجلد الحل الرئيسي
dotnet test

# أو من مجلد الاختبارات
cd Tamkeen.SMPP.SmscServerGatewayProxyService.Tests
dotnet test
```

### تشغيل اختبارات محددة
```bash
# تشغيل اختبارات controller فقط
dotnet test --filter "FullyQualifiedName~SendSmscControllerTests"

# تشغيل اختبارات service فقط
dotnet test --filter "FullyQualifiedName~SmscProxyServiceManagerTests"

# تشغيل اختبارات التكامل فقط
dotnet test --filter "FullyQualifiedName~ApiIntegrationTests"
```

### تشغيل مع تقارير التغطية
```bash
# تشغيل مع جمع بيانات التغطية
dotnet test --collect:"XPlat Code Coverage"

# تثبيت أداة إنشاء التقارير
dotnet tool install -g dotnet-reportgenerator-globaltool

# إنشاء تقرير HTML
reportgenerator -reports:"TestResults/*/coverage.cobertura.xml" -targetdir:"coveragereport" -reporttypes:Html

# فتح التقرير
start coveragereport/index.html  # Windows
open coveragereport/index.html   # macOS
xdg-open coveragereport/index.html # Linux
```

## أنواع الاختبارات

### 1. اختبارات الوحدة (Unit Tests)
تختبر مكونات فردية بمعزل عن باقي النظام:

```csharp
[Fact]
public async Task SendSmsAsync_ValidRequest_ReturnsSuccess()
{
    // Arrange
    var request = new SendSmsRequest { /* ... */ };
    
    // Act
    var result = await _controller.SendSmsAsync(request);
    
    // Assert
    result.Should().BeOfType<OkObjectResult>();
}
```

### 2. اختبارات التكامل (Integration Tests)
تختبر تفاعل المكونات مع بعضها البعض:

```csharp
[Fact]
public async Task Api_SendSms_EndToEnd_Works()
{
    // اختبار كامل من API إلى قاعدة البيانات
    var response = await _client.PostAsync("/api/sms", content);
    response.StatusCode.Should().Be(HttpStatusCode.OK);
}
```

### 3. اختبارات البيانات (Data-Driven Tests)
تختبر نفس الوظيفة مع بيانات مختلفة:

```csharp
[Theory]
[InlineData("96777123456", "YemenMobile")]
[InlineData("96771123456", "SabaFon")]
public void RouteMessage_DifferentNumbers_RoutesToCorrectProvider(
    string phoneNumber, string expectedProvider)
{
    // اختبار التوجيه لشبكات مختلفة
}
```

## أفضل الممارسات المطبقة

### 1. تسمية الاختبارات
```csharp
// نمط: MethodName_Scenario_ExpectedResult
public void SendSms_ValidRequest_ReturnsSuccess()
public void SendSms_InvalidNumber_ThrowsException()
public void SendSms_ServiceDown_ReturnsError()
```

### 2. ترتيب الاختبارات (AAA Pattern)
```csharp
[Fact]
public void TestMethod()
{
    // Arrange - إعداد البيانات والكائنات
    var request = new SendSmsRequest();
    
    // Act - تنفيذ العملية المراد اختبارها
    var result = _service.ProcessRequest(request);
    
    // Assert - التحقق من النتائج
    result.Should().NotBeNull();
}
```

### 3. استخدام Mocking
```csharp
// إنشاء mock objects للتبعيات الخارجية
var mockService = new Mock<ISmscService>();
mockService.Setup(x => x.SendSms(It.IsAny<string>()))
           .ReturnsAsync(new SuccessResult());
```

## تقارير الاختبارات

### تشغيل مع تفاصيل مفصلة
```bash
dotnet test --verbosity detailed --logger "console;verbosity=detailed"
```

### إنشاء تقرير XML
```bash
dotnet test --logger "trx;LogFileName=TestResults.trx"
```

### إنشاء تقرير JSON
```bash
dotnet test --logger "json;LogFileName=TestResults.json"
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل اختبارات التكامل
```bash
# التأكد من تشغيل الخدمات المطلوبة
docker-compose up -d

# أو استخدام in-memory services للاختبار
```

#### 2. مشاكل في Mock Objects
```csharp
// التأكد من إعداد جميع الـ dependencies
_mockService.Setup(x => x.Method()).Returns(expectedValue);
```

#### 3. مشاكل في البيانات
```csharp
// استخدام AutoFixture لإنشاء بيانات صحيحة
var fixture = new Fixture();
var request = fixture.Create<SendSmsRequest>();
```

## المساهمة في الاختبارات

### إضافة اختبارات جديدة
1. إنشاء ملف اختبار جديد في المجلد المناسب
2. اتباع نمط التسمية المعتمد
3. كتابة اختبارات شاملة للحالات المختلفة
4. إضافة تعليقات باللغة العربية للوضوح

### مراجعة الاختبارات
- التأكد من تغطية جميع الحالات المهمة
- فحص أداء الاختبارات
- التحقق من وضوح الاختبارات وسهولة فهمها

## الخطوات التالية

### تحسينات مقترحة
1. إضافة اختبارات الأداء (Performance Tests)
2. إضافة اختبارات الأمان (Security Tests)
3. إضافة اختبارات التحميل (Load Tests)
4. تحسين تغطية الكود إلى 90%+

### أدوات إضافية
- **NBomber** - اختبارات الأداء والتحميل
- **SpecFlow** - اختبارات BDD
- **Testcontainers** - اختبارات مع containers حقيقية
