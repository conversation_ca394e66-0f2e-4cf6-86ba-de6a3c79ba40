﻿using Tamkeen.Inetlab.SMPP.PDU;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities
{
    /// <summary>
    /// Represents a SMS message. Long SMS text message can be represented as collection of <see cref="SmscSMSMessage"/> instances.
    /// </summary>
    public class SmscSMSMessage
    {
        /// <summary>
        /// Local message id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Sequence number of received SubmitSm from client
        /// </summary>
        public uint SequenceNumber { get; set; }

        /// <summary>
        /// Sequence number of SubmitSm sent to the SMSC.
        /// </summary>
        public uint RemoteSequence { get; set; }

        /// <summary>
        /// MessageId received from SMSC server in SubmitSmResp.
        /// </summary>
        public string RemoteMessageId { get; set; }

        /// <summary>
        /// Delivery receipt received from SMSC server.
        /// </summary>
        public DeliverSm Receipt { get; set; }

        /// <summary>
        /// Delivery state from the receipt
        /// </summary>
        public string DeliveryState { get; set; }

        /// <summary>
        /// Username of the client that sent SubmitSm PDU.
        /// </summary>
        public string SystemID { get; set; }

        // Constructor
        public SmscSMSMessage(string id, uint sequenceNumber, uint remoteSequence, string remoteMessageId, string systemID)
        {
            Id = id;
            SequenceNumber = sequenceNumber;
            RemoteSequence = remoteSequence;
            RemoteMessageId = remoteMessageId;
            SystemID = systemID;
        }

        // Factory method to create an instance from a serialized string
        public static SmscSMSMessage FromString(string serializedMessage)
        {
            var parts = serializedMessage.Split(':');
            if (parts.Length == 5)
            {
                return new SmscSMSMessage(
                    parts[0],
                    uint.Parse(parts[1]),
                    uint.Parse(parts[2]),
                    parts[3],
                    parts[4]
                );
            }

            throw new ArgumentException("Invalid serialized message format");
        }

        // ToString override to serialize the message
        public override string ToString()
        {
            return $"{Id}:{SequenceNumber}:{RemoteSequence}:{RemoteMessageId}:{SystemID}";
        }
    }
}
