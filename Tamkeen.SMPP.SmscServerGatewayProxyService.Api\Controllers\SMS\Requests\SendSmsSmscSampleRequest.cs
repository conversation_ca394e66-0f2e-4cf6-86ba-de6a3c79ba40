﻿using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests
{
    public class SendSmsSmscSampleRequest : BaseRequest
    {
        public string dstAdr { get; set; }
        public string smsText { get; set; }
        public SmsDstType smsDstType { get; set; } = SmsDstType.local;
        //local 1
        //other 2
    }
}
