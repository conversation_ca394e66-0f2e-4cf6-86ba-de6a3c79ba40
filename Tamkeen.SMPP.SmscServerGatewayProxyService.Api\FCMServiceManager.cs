﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Swashbuckle.AspNetCore.Annotations;
using System.Net;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.FCM.Requests;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class FCMServiceManager
    {
    }

    
    public async Task<IHttpActionResult> SendFCM(FCMMessageInfoRequest info)
    {
        SendFCMResponse sendFCMResponse = new SendFCMResponse();
        try
        {
            if (ApiConfig.Mode != "Live")
            {
                info.body = info.body + "\n" + ApiConfig.Mode;
            }
            _logRegister.LogInfo("Cash_SendFCM", info.LogRef, "FCM-WhatsApp", " ", info, 1, 1);

            string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
            if (tkn != null)
            {
                messages messages = new messages()
                {
                    message = new message()
                    {
                        token = tkn,

                        data = new data()
                        {
                            title = info.title,
                            body = info.body,

                        },
                        notification = new notification
                        {
                            body = info.body,
                            title = info.title,
                        }
                    }
                };
                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);

                //RedisCalls redisCalls = new RedisCalls("************", 6379);
                string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");
                string r = string.Empty;

                if (!string.IsNullOrEmpty(token))
                {
                    r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Cash);
                    _logRegister.LogInfo("Cash_SendFCM", JsonConvert.SerializeObject(r), info.LogRef, "FCM-WhatsApp", "Api", 1, 2);

                }
                else
                {
                    // _logger.Information("Cash_SendFCM", "FcmTokenNotFound");
                    _logRegister.LogInfo("Cash_SendFCM", "FcmTokenNotFound", info.LogRef, "FCM-WhatsApp", "Api", 2, 2);
                }

                _logRegister.LogInfo("Cash_SendFCM", JsonConvert.SerializeObject(r), info.LogRef, "FCM-WhatsApp", "Api", 1, 2);

                //JObject jObject = Object.Parse(r);
                return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));
            }
            else
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
            }

        }
        catch (TimeoutException ex)
        {
            _logRegister.LogInfo("Cash_SendFCMTimeoutException", info.LogRef, ex.Message, "FCM-WhatsApp", "Api", 2, 2);
            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("Cash_SendFCMException", info.LogRef, ex.Message, "FCM-WhatsApp", "Api", 2, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }
    }

    [HttpPost]
    // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/GetMobileFCM")]
    [SwaggerOperation("GetMobileFCM")]
    [SwaggerResponse(200, "GetMobileFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> GetMobileFCM(GetMobileFCMRequest info)
    {
        BaseResponse GetFCMResponse = new BaseResponse();
        try
        {
            _logRegister.LogInfo("Cash_GetMobileFCM", info.msisdn, "FCM-WhatsApp", "Api", info, 1, 1);

            string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.msisdn }, "", Project.PMB_Cash);
            if (tkn != null)
            {
                return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(tkn));
            }
            else
            {
                GetFCMResponse.ResultCode = -1;
                GetFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(GetFCMResponse));
            }

        }
        catch (TimeoutException ex)
        {

            _logRegister.LogInfo("Cash_GetMobileFCMTimeoutException", "FCM-WhatsApp", info.msisdn, ex.Message, 1, 2);
            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("Cash_GetMobileFCMException", "FCM-WhatsApp", info.msisdn, ex.Message, 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }

    }

    //FCM​/SendFCMIOS


    [HttpPost]
    // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/SendFCMIOS")]
    [SwaggerOperation("SendFCMIOS")]
    [SwaggerResponse(200, "SendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> SendFCMIOS(FCMMessageInfo info)
    {
        SendFCMResponse sendFCMResponse = new SendFCMResponse();
        try
        {
            if (ApiConfig.Mode != "Live")
            {
                info.body = info.body + "\n" + ApiConfig.Mode;
            }
            _logRegister.LogInfo("Cash_SendFCMIOS", info.LogRef, "FCM-WhatsApp", "Api", info, 1, 1);

            string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
            if (tkn != null)
            {
                messages messages = new messages()
                {
                    message = new message()
                    {
                        token = tkn,// "fuGKGJnzJ0qaj5uJiRnTih:APA91bGldlxWZleIPEtoWMgjH4k11e1yCP-p-X-V4dt3W6GPPHfQyWMUx0CP21cWyEN1exfdFQTZ-7OxacV5TV7GZ7wkXtmIs4oRYq1av6LVUQqLtak8uosfBbAiqRHYZ2aHedtdfVPj",

                        data = new data()
                        {
                            title = info.title,
                            body = info.body,
                        },
                        notification = new notification
                        {
                            body = info.body,
                            title = info.title,
                        }
                    }
                };

                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");
                string r = string.Empty;

                if (!string.IsNullOrEmpty(token))
                {
                    r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Cash);
                    _logRegister.LogInfo("Cash_SendFCMIOS", JsonConvert.SerializeObject(r), r, " ", 1, 2);

                }
                else
                {
                    _logRegister.LogInfo("Cash_SendFCMIOS", "FCMTokenNotFound", r, " ", 1, 2);

                }
                //JObject jObject = Object.Parse(r);
                return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));
            }
            else
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
            }

        }
        catch (TimeoutException ex)
        {


            _logRegister.LogInfo("Cash_SendFCMIOSTimeoutException", info.LogRef, ex.Message, " ", 1, 2);
            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("Cash_SendFCMIOSException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }

    }
    ///FCM/SendFCMV2

    [HttpPost]
    //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/SendFCMIOSV2")]
    [SwaggerOperation("SendFCMIOSV2")]
    [SwaggerResponse(200, "SendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> SendFCMIOSV2(ExpMessageInfo info)
    {
        SendFCMResponse sendFCMResponse = new SendFCMResponse();
        try
        {
            if (ApiConfig.Mode != "Live")
            {
                info.body = info.body + "\n" + ApiConfig.Mode;
            }
            _logRegister.LogInfo("Cash_FCMIOSV2", info.LogRef, "FCM-WhatsApp", "", info, 1, 1);

            if (string.IsNullOrEmpty(info.fcmToken))
            {

                string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                if (tkn != null)
                {
                    info.fcmToken = tkn;
                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
                }

            }

            messages messages = new messages()
            {
                message = new message()
                {
                    token = info.fcmToken,// "fuGKGJnzJ0qaj5uJiRnTih:APA91bGldlxWZleIPEtoWMgjH4k11e1yCP-p-X-V4dt3W6GPPHfQyWMUx0CP21cWyEN1exfdFQTZ-7OxacV5TV7GZ7wkXtmIs4oRYq1av6LVUQqLtak8uosfBbAiqRHYZ2aHedtdfVPj",

                    data = new data()
                    {
                        title = info.title,
                        body = info.body,
                    },
                    notification = new notification
                    {
                        body = info.body,
                        title = info.title,
                    }
                }
            };

            RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
            string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");
            string r = string.Empty;

            if (!string.IsNullOrEmpty(token))
            {
                r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Cash);
                _logRegister.LogInfo("Cash_SendFCM", JsonConvert.SerializeObject(r), r, " ", 1, 2);

            }
            else
            {
                _logger.Information("Cash_FCMIOSV2", "FCMTokenNotFound", " ", 2, 2);
            }

            //_logRegister.LogInfo("Cash_SendFCM", info.LogRef, r, 2);

            //JObject jObject = Object.Parse(r);
            return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));




        }
        catch (TimeoutException ex)
        {


            _logRegister.LogInfo("Cash_SendFCMTimeoutException", info.LogRef, " ", ex.Message, 1, 2);
            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("Cash_SendFCMException", info.LogRef, " ", ex.Message, 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }

    }


    [HttpPost]
    // [Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/ExpSendFCM")]
    [SwaggerOperation("ExpSendFCM")]
    [SwaggerResponse(200, "ExpSendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> ExpSendFCM(ExpMessageInfo info)
    {
        SendFCMResponse sendFCMResponse = new SendFCMResponse();
        try
        {
            if (ApiConfig.Mode != "Live")
            {
                info.body = info.body + "\n" + ApiConfig.Mode;
            }
            _logRegister.LogInfo("ExpSendFCM", info.LogRef, "FCM-WhatsApp", " ", info, 1, 1);

            if (string.IsNullOrEmpty(info.fcmToken))
            {
                string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "E", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Expatriate);
                if (tkn != null)
                {
                    info.fcmToken = tkn;
                    //  info.fcmToken = "eVehZ0OaQ8q1FSykxzdJiV:APA91bH61FeI9L2WaiCtQmnaECKsKRTQLFTaHxiErKIZNemaOnRw9y8cpj8Sp8zSa6DMDW3nn00xzt4asencfpciZMzRmfZApHzYMWrq8N4QDDCL7EATNn28E1xhXjxtsmmhhomSKkxj";
                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
                }
            }
            messages messages = new messages()
            {
                message = new message()
                {
                    token = info.fcmToken,

                    data = new data()
                    {
                        title = info.title,
                        body = info.body,
                    }
                }
            };

            RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
            string token = await redisCalls.RetrieveFromRedis<string>("ExpatriateFirebase");
            string r = string.Empty;

            if (!string.IsNullOrEmpty(token))
            {
                r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Expatriate);
                _logRegister.LogInfo("Exp_SendFCM", JsonConvert.SerializeObject(r), r, " ", 1, 2);

            }
            else
            {
                _logRegister.LogInfo("Exp_SendFCM", JsonConvert.SerializeObject(r), "TokenNotFound", " ", 1, 2);

            }

            return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));

        }
        catch (TimeoutException ex)
        {
            _logRegister.LogInfo("ExpSendFCMOTPTimeoutException", info.LogRef, ex.Message, " ", 1, 2);
            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("ExpSendFCMException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }
    }


    [HttpPost]
    //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/NewSendFCM")]
    [SwaggerOperation("NewSendFCM")]
    [SwaggerResponse(200, "ExpSendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> NewSendFCM(ExpMessageInfo info)
    {
        try
        {
            SendFCMResponse sendFCMResponse = new SendFCMResponse();
            if (ApiConfig.Mode != "Live")
            {
                info.body = info.body + "\n" + "(" + ApiConfig.Mode + ")";
            }
            _logRegister.LogInfo("CashApp_SendFCM", info.LogRef, "FCM-WhatsApp", "Api", info, 1, 1);

            if (string.IsNullOrEmpty(info.fcmToken))
            {

                string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                if (tkn != null)
                {
                    info.fcmToken = tkn;
                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
                }

            }
            messages messages = new messages()
            {
                message = new message()
                {
                    token = info.fcmToken,
                    //notification = new notification
                    //{
                    //    title = info.title,
                    //    body = info.body,
                    //},
                    data = new data()
                    {
                        title = info.title,
                        body = info.body,
                    }
                }
            };
            RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
            string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");

            var r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Cash); //change to PMB CashExp after create project in firebase
            _logRegister.LogInfo("CashApp_SendFCM", info.LogRef, r, " ", 2, 2);

            //JObject jObject = JObject.Parse(r);
            return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));
        }
        catch (TimeoutException ex)
        {

            _logRegister.LogInfo("CashApp_SendFCMTimeoutException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("CashApp_SendFCMException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }

    }

    [HttpPost]
    //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/BusSendFCM")]
    [SwaggerOperation("BusSendFCM")]
    [SwaggerResponse(200, "BusSendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> BusSendFCM(ExpMessageInfo info)
    {
        SendFCMResponse sendFCMResponse = new SendFCMResponse();
        try
        {
            if (ApiConfig.Mode != "Live")
            {
                info.body = info.body + "\n" + "(" + ApiConfig.Mode + ")";
            }
            _logRegister.LogInfo("Bus_SendFCM", info.LogRef, "FCM-WhatsApp", " ", 1, 1);

            if (string.IsNullOrEmpty(info.fcmToken))
            {

                string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "B", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Business);
                if (tkn != null)
                {
                    info.fcmToken = tkn;
                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
                }
            }
            messages messages = new messages()
            {
                message = new message()
                {
                    token = info.fcmToken,
                    notification = new notification
                    {
                        title = info.title,
                        body = info.body,
                    },
                    data = new data()
                    {
                        title = info.title,
                        body = info.body,
                    }

                }
            };

            RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
            string token = await redisCalls.RetrieveFromRedis<string>("BusinessFirebase");

            var r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Business);
            _logRegister.LogInfo("Bus_SendFCM", info.LogRef, r, " ", 1, 2);

            //JObject jObject = JObject.Parse(r);}
            return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));

        }
        catch (TimeoutException ex)
        {
            _logRegister.LogInfo("Bus_SendFCMTimeoutException", info.LogRef, "", ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("Bus_SendFCMException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }

    }
    [HttpPost]
    //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/RemittanceSendFCM")]
    [SwaggerOperation("RemittanceSendFCM")]
    [SwaggerResponse(200, "RemittanceSendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> RemittanceSendFCM(ExpMessageInfo info)
    {
        try
        {
            SendFCMResponse sendFCMResponse = new SendFCMResponse();
            _logRegister.LogInfo("Remittance_SendFCM", info.LogRef, "FCM-WhatsApp", "Api", info, 1, 1);
            if (string.IsNullOrEmpty(info.fcmToken))
            {

                string tkn = await _helper.GetFCMToken(new GetFCmReq { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                if (tkn != null)
                {
                    info.fcmToken = tkn;
                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    return Content(HttpStatusCode.BadRequest, JsonConvert.SerializeObject(sendFCMResponse));
                }
            }
            messages messages = new messages()
            {
                message = new message()
                {
                    token = info.fcmToken,
                    //notification = new notification
                    //{
                    //    title = info.title,
                    //    body = info.body,
                    //},
                    data = new data()
                    {
                        title = info.title,
                        body = info.body,
                    }
                }
            };
            RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
            string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");

            var r = await _helper.PostFCMFireBase(messages, token, Project.PMB_Cash); //change to PMB CashExp after create project in firebase
            _logRegister.LogInfo("Remittance_SendFCM", info.LogRef, r, " ", 1, 2);

            //JObject jObject = JObject.Parse(r);
            return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));
        }
        catch (TimeoutException ex)
        {
            _logRegister.LogInfo("Remittance_SendFCMTimeoutException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("Remittance_SendFCMException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }

    }


    [HttpPost]
    //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
    [Route("FCM/RemittanceConditionSendFCM")]
    [SwaggerOperation("RemittanceConditionSendFCM")]
    [SwaggerResponse(200, "RemittanceConditionSendFCMResponse", typeof(IHttpActionResult))]
    public async Task<IHttpActionResult> RemittanceConditionSendFCM(ConditionMessageInfo info)
    {
        try
        {
            messagesForRem messagesifo = new messagesForRem();
            SendFCMResponse sendFCMResponse = new SendFCMResponse();
            _logRegister.LogInfo("RemittanceConditionSendFCM", info.LogRef, "RemittanceConditionSendFCM", "Api", info, 1, 1);
            if (info.Condition != null && info.Condition == "com.tamkeen.sms")
            {


                messagesifo = new messagesForRem()
                {
                    message = new messageRem
                    {

                        data = new data()
                        {
                            title = info.title,
                            body = info.body,

                        },
                        topic = info.Condition// $"('{info.Condition}' in topics)",
                                              // content_available=true

                    }

                };
            }
            else if (info.Condition != null && info.Condition == "com.tamkeen.ios")
            {
                messagesifo = new messagesForRem()
                {
                    message = new messageRem
                    {
                        notification = new notification
                        {
                            title = info.title,
                            body = info.body,
                        },
                        data = new data()
                        {
                            title = info.title,
                            body = info.body,

                        },
                        topic = info.Condition// $"('{info.Condition}' in topics)",
                                              // content_available=true

                    }

                };
            }

            else
            {
                return Content(HttpStatusCode.BadRequest, "Condition info is wrong");
            }

            RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
            string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");

            var r = await _helper.PostFCMFireBase(messagesifo, token, Project.PMB_Cash); //change to PMB CashExp after create project in firebase
            _logRegister.LogInfo("RemittanceConditionSendFCM", info.LogRef, r, " ", 1, 2);

            //JObject jObject = JObject.Parse(r);
            return Content(HttpStatusCode.OK, JsonConvert.SerializeObject(r));
        }
        catch (TimeoutException ex)
        {
            _logRegister.LogInfo("RemittanceConditionSendFCMTimeoutException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.RequestTimeout, ex);
        }
        catch (Exception ex)
        {
            _logRegister.LogInfo("RemittanceConditionSendFCMException", info.LogRef, ex.Message, " ", 1, 2);

            return Content(HttpStatusCode.ExpectationFailed, ex);
        }


    }
}
