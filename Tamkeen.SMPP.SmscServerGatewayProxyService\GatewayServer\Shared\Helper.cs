﻿using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Org.BouncyCastle.Asn1.Crmf;
using RabbitMQ.Client;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Shared
{
    internal class Helper
    {
        public async Task<string> PostFCM<T>(T req, string serverKey)
        {

            var client = new RestClient("https://fcm.googleapis.com/fcm/send");
            var request = new RestRequest("https://fcm.googleapis.com/fcm/send", Method.Post);
            request.AddHeader("Authorization", serverKey);
            request.AddHeader("Content-Type", "application/json");

            request.AddBody(req);
            request.RequestFormat = DataFormat.Json;
           // request.Timeout = 30000;

            var response = await client.ExecuteAsync(request);
            string re = response.Content;
            return re;
        }
        //

        public async Task<string> PostFCMFireBase<T>(T req, string token, Project project)
        {
            RestRequest restRequest = new RestRequest();
            RestClient restClient = new RestClient();
            if (project == Project.PMB_Cash)
            {
                restClient = new RestClient("https://fcm.googleapis.com/v1/projects/cash-3a041/messages:send");
                restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/cash-3a041/messages:send", Method.Post);
                restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            }
            else if (project == Project.PMB_Business)
            {
                restClient = new RestClient("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send");
                restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send", Method.Post);
                restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            }
            else if (project == Project.PMB_Expatriate)
            {
                restClient = new RestClient("https://fcm.googleapis.com/v1/projects/expatriate-be128/messages:send");
                restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/expatriate-be128/messages:send", Method.Post);
                restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            }
            //else if (project == Project.PMB_CashIOS)
            //{
            //    restClient = new RestClient("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send");
            //    restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send", Method.POST);
            //    restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            //}

            restRequest.AddHeader("Content-Type", "application/json");

            restRequest.AddBody(req);
            restRequest.RequestFormat = DataFormat.Json;

            var response = await restClient.ExecuteAsync(restRequest);
            string re = response.Content;
            return re;
        }

        public async Task<string> GetFCMToken(GetFCmReq req, string token, Project project)
        {
            try
            {
               // ApiConfig _mySettings = new ApiConfig();
                RestRequest restRequest = new RestRequest();
                RestClient restClient = new RestClient();
                ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
                string url = _.DalUrl + "/api/ExpatriateApp/GetFcm";
                restClient = new RestClient(url);
                restRequest = new RestRequest();

                restRequest.AddHeader("Content-Type", "application/json");

                restRequest.AddJsonBody(req);
                restRequest.RequestFormat = DataFormat.Json;

                var response = await restClient.ExecuteAsync(restRequest,Method.Post);
                var res = JsonConvert.DeserializeObject<GetFCMRespons>(response.Content);

                return res.FCM;

            }
            catch (Exception e)
            {
                return null;
            }




        }
        public enum Project
        {
            PMB_Business = 1,
            PMB_Expatriate = 2,
            PMB_Cash = 3,
            PMB_CashIOS = 4
        }


    }
}
