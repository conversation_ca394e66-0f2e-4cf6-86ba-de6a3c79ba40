# Logging Settings
LOGGING__LOGLEVEL__DEFAULT=Debug
LOGGING__LOGLEVEL__MICROSOFT=Information
LOGGING__LOGLEVEL__MICROSOFT_HOSTING_LIFETIME=Information
LOGGING__LOGLEVEL__SYSTEM_NET_HTTP=Trace

# Kestrel Settings
KESTREL__LOGLEVEL__DEFAULT=Debug
KESTREL__LOGLEVEL__MICROSOFT_ASPNETCORE_SERVER_KESTREL=Trace

# App Options
REDIS_CONNECTION_STRING=localhost:6379
AUTHENTICATION_SERVER_URI=
ACTIVATE_SWAGGER=true
CORS_ENABLE_URIS=[]

# Serilog Settings
ELASTICSEARCH_URI=http://*************:9200
ELASTICSEARCH_INDEX_FORMAT=tamkeensmppsmscserverproxyservice-{0:yyyy.MM.dd}
ELASTICSEARCH_MIN_LEVEL=Warning
SERILOG_CONSOLE_MIN_LEVEL=Information

# Health Checks
HEALTH_CHECKS__NAME=Clean Template Project Service
HEALTH_CHECKS__URI=http://localhost:5015/health
HEALTH_CHECKS_EVALUATION_TIME=5

# FCM WhatsApp Settings
FCM_WHATSAPP_ENV_TYPE=1
FCM_WHATSAPP_APPLICATION_NAME=FCMWhatsappAPI
FCM_WHATSAPP_AUTH_TOKEN=EAAU5guXD6OcBOwC0eZCv7mW6LxSMJGwkZBaxfVFtJeklS6R01d7t45pWZCc7Jeu36xqnJJZAEzCEfOCZCvT2zxGCuLcbFojftQ8doZC3zRx7MsP1mlphK8Ecr986PD0Y573P9V2bp2w1i4ZBwcK9mZAheRhvQZCD9ZAGeUHgd5Ax49sbJtpaa0EjB1dGR9
FCM_WHATSAPP_PHONE_NUMBER_ID=101178529310077
FCM_WHATSAPP_PHONE_NUMBER_ID_EXP=110898085304010
FCM_WHATSAPP_VERSION=v20.0
FCM_WHATSAPP_DAL_URL=https://************:8083
FCM_WHATSAPP_SECRET_KEY=50645367556B58703273357638792F423F4528482B4D6251655468576D597133743677397A24432646294A404E635266556A586E327234753778214125442A47
FCM_WHATSAPP_LOG_FILE_PATH=C:\\log\\WhatsAppBusinessAPI\\v1\\WhatsAppBusinessAPI_v1.log
FCM_WHATSAPP_SEQ_URL=http://localhost:5341
FCM_WHATSAPP_REDIS_HOST=************
FCM_WHATSAPP_REDIS_PORT=6379
FCM_WHATSAPP_MODE=Live

# SMSC Settings
SMSC_YGSM_CLIENT_URI=tcp://**************:5020
SMSC_YGSM_SYSTEM_ID=Tamkeen
SMSC_YGSM_PASSWORD_ENCRYPTED=QWRtaW5AMTIzNDU2Nzg5MA==
SMSC_YGSM_MOBILE_PATTERN=^(96770|0096770|70|\\+96770)
SMSC_YGSM_SERVER_PORT=28020

SMSC_YOU_TRANSMITTER_CLIENT_URI=tcp://**************:8313
SMSC_YOU_TRANSMITTER_SYSTEM_ID=9670110002313
SMSC_YOU_TRANSMITTER_PASSWORD_ENCRYPTED=WW0yMDIxRW5jcnlwdGVk
SMSC_YOU_TRANSMITTER_MOBILE_PATTERN=^(96773|0096773|73|\\+96773)
SMSC_YOU_TRANSMITTER_SERVER_PORT=28013

SMSC_YOU_RECEIVER_CLIENT_URI=tcp://**************:8313
SMSC_YOU_RECEIVER_SYSTEM_ID=9670110002313
SMSC_YOU_RECEIVER_PASSWORD=Ym2021
SMSC_YOU_RECEIVER_MOBILE_PATTERN=^(96773|0096773|73|\\+96773)
SMSC_YOU_RECEIVER_SERVER_PORT=28014

SMSC_SABAFON_TRANSMITTER_CLIENT_URI=tcp://**************:6694
SMSC_SABAFON_TRANSMITTER_SYSTEM_ID=Tamkeen
SMSC_SABAFON_TRANSMITTER_PASSWORD_ENCRYPTED=VGFtc2FiYTFFbmNyeXB0ZWQ=
SMSC_SABAFON_TRANSMITTER_MOBILE_PATTERN=^(96771|0096771|71|\\+96771)
SMSC_SABAFON_TRANSMITTER_SERVER_PORT=28094

SMSC_SABAFON_RECEIVER_CLIENT_URI=tcp://**************:6694
SMSC_SABAFON_RECEIVER_SYSTEM_ID=Tamkeen
SMSC_SABAFON_RECEIVER_PASSWORD=Tamsaba1
SMSC_SABAFON_RECEIVER_MOBILE_PATTERN=^(96771|0096771|71|\\+96771)
SMSC_SABAFON_RECEIVER_SERVER_PORT=28095

SMSC_YEMENMOBILE_CLIENT_URI=tcp://*************:9083
SMSC_YEMENMOBILE_SYSTEM_ID=TAMKEEN
SMSC_YEMENMOBILE_PASSWORD=BxEs@56
SMSC_YEMENMOBILE_MOBILE_PATTERN=^(96777|0096777|77|\\+96777|96778|0096778|78|\\+96778)
SMSC_YEMENMOBILE_SERVER_PORT=28016

# RabbitMQ Settings
RABBITMQ_HOST=*************
RABBITMQ_USER=admin
RABBITMQ_PASS=admin
RABBITMQ_PORT=5672

# Health Check Settings
HEALTHCHECK_REINITIALIZE_THRESHOLD_SECONDS=5
HEALTHCHECK_RESTART_THRESHOLD_SECONDS=10

# Tamkeen Logging Config
TAMKEEN_PROJECT_NAME=TamkeenSMPPSmscServerProxyService
TAMKEEN_LAYER_NAME=ServiceLayer
TAMKEEN_SERILOG_SEQ_URL=http://localhost:5341
TAMKEEN_SERILOG_FILE_PATH=C:\\Logs\\TamkeenSMPPSmscServerProxyService\\log-.txt
TAMKEEN_ELASTICSEARCH_URL=http://*************:9200
TAMKEEN_ELASTICSEARCH_INDEX_NAME=TamkeenSMPPSmscServerProxyServiceProduction-logs
TAMKEEN_ELASTICSEARCH_INDEX_FORMAT=TamkeenSMPPSmscServerProxyServiceProduction-{0:yyyy.MM.dd}
TAMKEEN_ELASTICSEARCH_API_KEY=ApiKey abc123
TAMKEEN_USE_SERILOG=true
TAMKEEN_USE_ELASTICSEARCH=true
TAMKEEN_USE_CONSOLE=true

# Other SMSC Server Settings
OTHER_SMSC_SERVER_PORT=29000
OTHER_SMSC_SERVER_NAME=OtherSmscServer
OTHER_SMSC_WHATSAPP_URL=http://localhost:5015
OTHER_SMSC_RESOURCE=/api/WhatsApp/SendToWhatsApp

# مفتاح التشفير (يجب أن يكون في Key Vault)
ENCRYPTION_KEY_NAME=tamkeen-encryption-key
