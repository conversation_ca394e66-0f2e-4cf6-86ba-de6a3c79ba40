﻿using System;
using System.Threading.Tasks;
using static Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ.SmscRabbitMQGeneral;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer
{
    public class SmscServerProxyNew
    {
        private readonly SmscServerProxyClient _client;
        private readonly SmscRabbitMQSendSmsService _rabbitMqService;
        private readonly SmscInMemoryMessageStore _inMemoryStore; // Fallback for in-memory storage

        public SmscServerProxyNew(SmscServerProxyClient client, SmscRabbitMQSendSmsService rabbitMqService)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _rabbitMqService = rabbitMqService ?? throw new ArgumentNullException(nameof(rabbitMqService));
            _inMemoryStore = new SmscInMemoryMessageStore(); // Fallback for in-memory usage
        }

        public async Task SendMessageAsync(SmscSMSMessage message)
        {
            if (_client.IsConnected)
            {
                await _client.SendMessageAsync(message);
            }
            else
            {
                // Store the message in RabbitMQ if the client is offline
                await _rabbitMqService.StoreMessageAsync(message);
            }
        }

        public async Task ProcessStoredMessagesAsync()
        {
            if (_client.IsConnected)
            {
                // Retrieve messages from RabbitMQ and send them to the client
                var storedMessages = await _rabbitMqService.RetrieveMessagesAsync();
                foreach (var message in storedMessages)
                {
                    await _client.SendMessageAsync(message);
                }
            }
        }
    }

}