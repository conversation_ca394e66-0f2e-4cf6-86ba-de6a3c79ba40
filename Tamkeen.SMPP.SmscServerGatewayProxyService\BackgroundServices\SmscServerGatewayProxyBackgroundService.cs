﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public class SmscServerGatewayProxyBackgroundService : BackgroundService, ISmscServerGatewayProxyBackgroundService
    {
        private bool _isRunning;
        private readonly string _transceiverSystemId;
        private readonly IServiceProvider _serviceProvider;
        public SmscServerProxy _smscServerProxy;
        private readonly ILogger _logger;
        //private CancellationTokenSource _cancellationTokenSource; // Instance-specific CancellationTokenSource
        private readonly SmscServerGatewayProxySettings _smscServerGatewayProxySettings;
        public SmscServerGatewayProxySettings smscServerGatewayProxySettings => _smscServerGatewayProxySettings;

        //bool firstRun = true;
        //private readonly Dictionary<string, ConnectionMode> modeMap = new Dictionary<string, ConnectionMode>
        //{
        //    { "transceiver", ConnectionMode.Transceiver },
        //    { "receiver", ConnectionMode.Receiver },
        //    { "transmitter", ConnectionMode.Transmitter }
        //};
        public SmscServerGatewayProxyBackgroundService(IServiceProvider serviceProvider,
            SmscServerGatewayProxySettings smscServerGatewayProxySettings,
            SmscServerProxy smscServerProxy)
        {
            _smscServerGatewayProxySettings = smscServerGatewayProxySettings;
            _transceiverSystemId = smscServerGatewayProxySettings.smscClientSettings.transceiverSystemId;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
            _smscServerProxy = smscServerProxy ?? throw new ArgumentNullException(nameof(smscServerProxy));
            //_cancellationTokenSource = new CancellationTokenSource(); // Initialize a new CancellationTokenSource
        }
        //public SmscServerGatewayProxyBackgroundService(IServiceProvider serviceProvider,
        //    IOptions<List<SmscServerGatewayProxySettings>> smscServerGatewayProxySettings)
        //{
        //    _transceiverSystemId = smscServerGatewayProxySettings.Value.FirstOrDefault(s => s.smscClientSettings.transceiverSystemId == "YemenMobile").smscClientSettings.transceiverSystemId;
        //    _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        //    _smscServerProxy = new SmscServerProxy(serviceProvider, smscServerGatewayProxySettings.Value.FirstOrDefault(s => s.smscClientSettings.transceiverSystemId == "YemenMobile"));
        //    _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
        //}
        public string TransceiverSystemId => _transceiverSystemId;
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _isRunning = true;
                _logger.Information("Background service started");
                //await _smscServerProxy.Run(); // Run SmscServerProxy continuously
                //await Task.Delay(TimeSpan.FromSeconds(60), stoppingToken);
                //_cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken); // Link the tokens

                // Ensure that the token used here is the linked token source's token
                await StartSmscServerClient(stoppingToken);
                //if (!stoppingToken.IsCancellationRequested)
                //{
                //    _cancellationTokenSource = new CancellationTokenSource();
                //}
                //else
                //{
                //    await _cancellationTokenSource.CancelAsync();
                //}
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Error($"Error in ExecuteAsync: {ex.Message}");
                throw;
            }
            //finally
            //{
            //    //_isRunning = false;
            //    //firstRun = false;
            //}
        }

        //// Stop the background service
        //public void StopService()
        //{
        //    _logger.Information("Stopping Background Smsc Server Proxy Service.", "StopService", _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.systemId);
        //    _cancellationTokenSource.Cancel();
        //}

        // Restart the background service
        //public async void RestartService()
        //{
        //    _logger.Information("Restarting Background Smsc Server Proxy Service.", "RestartService", _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.systemId);
        //    await ClientDisconnect();
        //    await _smscServerProxy.StopAsync();
        //    // Cancel the current task
        //    _cancellationTokenSource.Cancel();

        //    // Reset the cancellation token source
        //    _cancellationTokenSource = new CancellationTokenSource();

        //    // Start the service again
        //    _ = ExecuteAsync(_cancellationTokenSource.Token);
        //}
        public async Task RestartServiceAsync(CancellationToken cancellationToken)
        {
            if (_smscServerProxy != null)
            {
                _logger.Information("Restarting Background Smsc Server Proxy Service.", "RestartService", _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.systemId);

                await StopAsync(cancellationToken); // Use StopAsync to ensure proper shutdown

                await StartAsync(cancellationToken); // Start the service again 
            }
            else
            {
                await StartAsync(cancellationToken);
            }
        }
        private async Task StartSmscServerClient(CancellationToken stoppingToken)
        {
            bool isStartedToRun = false;
            int retryCount = 0; // Track the number of retries
            const int maxRetries = 10; // Maximum number of retries before reinitializing
            try
            {
                _isRunning = true;

                _logger.Information($"Attempting to connect client: {_smscServerProxy.SmppServer.Name} and to connect client: {_smscServerProxy.SmscServerProxyClient}");

                while (!stoppingToken.IsCancellationRequested)
                {
                    // Add timeout for the Run task
                    if (_smscServerProxy != null && !isStartedToRun)
                    {
                        isStartedToRun = true;
                        if (!_smscServerProxy.SmppServer.IsRun)
                        {
                            _logger.Information("Starting _smscServerProxy.Run()");
                            var runTask = _smscServerProxy.Run();

                            // Wait for the task to complete or timeout, while respecting the cancellation token
                            if (await Task.WhenAny(runTask, Task.Delay(TimeSpan.FromSeconds(30), stoppingToken)) == runTask)
                            {
                                _logger.Information("Run completed.");
                            }
                            else
                            {
                                _logger.Information("Run timed out.");
                            }
                        }

                        if (_smscServerProxy != null)
                        {
                            if (_smscServerProxy.SmscServerProxyClient._clientConnectionSettings.enable &&
                                                _smscServerProxy.SmscServerProxyClient._proxyClient.Status != ConnectionStatus.Bound)
                            {
                                //_logger.Information($"Attempting to connect client: {_smscServerProxy.SmscServerProxyClient}");

                                ////if (_smscServerProxy.SmscServerProxyClient._clientConnectionSettings.mode != null &&
                                ////    modeMap.ContainsKey(_smscServerProxy.SmscServerProxyClient._clientConnectionSettings.mode.ToLowerInvariant()))
                                ////{
                                ////    string lowercasedMode = _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.mode.ToLowerInvariant();
                                ////    _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.connectionMode = modeMap[lowercasedMode];
                                ////}

                                ////await ClientDisconnect();

                                //// Attempt to connect and respect the cancellation token
                                //var connectTask = _smscServerProxy.SmscServerProxyClient.RunAsync();
                                //if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30), stoppingToken)) == connectTask)
                                //{
                                //    _logger.Information($"Client {_smscServerProxy.SmscServerProxyClient} connected.");
                                //}
                                //else
                                //{
                                //    _logger.Information($"Client {_smscServerProxy.SmscServerProxyClient} connection attempt timed out.");
                                //}
                                _logger.Information($"Attempting to connect client: {_smscServerProxy.SmscServerProxyClient}");

                                // Attempt to connect and respect the cancellation token
                                var connectTask = _smscServerProxy.SmscServerProxyClient.RunAsync();

                                // Check if the connection attempt was successful
                                if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30), stoppingToken)) == connectTask)
                                {
                                    _logger.Information($"Client {_smscServerProxy.SmscServerProxyClient} connected.");
                                    retryCount = 0; // Reset retry count after successful connection
                                }
                                else
                                {
                                    _logger.Information($"Client {_smscServerProxy.SmscServerProxyClient} connection attempt timed out.");
                                    retryCount++; // Increment the retry count

                                    // If the connection fails 10 times, reinitialize the client
                                    if (retryCount >= maxRetries)
                                    {
                                        _logger.Information($"Client {_smscServerProxy.SmscServerProxyClient} failed to connect after {maxRetries} attempts. Reinitializing...");

                                        await _smscServerProxy.ReInitializeSmscServerProxyClientAsync();

                                        retryCount = 0; // Reset the retry count after reinitialization
                                    }
                                }
                            }
                        }
                        isStartedToRun = false;
                    }
                    else
                    {
                        isStartedToRun = false;
                        return;
                    }

                    // Wait for 5 seconds or exit early if the token is canceled
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.Information($"StartSmscServerClient was {TransceiverSystemId} canceled by cancellation token.");
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Error($"Error in StartSmscServerClient {TransceiverSystemId}: {ex.Message}");
            }
            finally
            {
                isStartedToRun = false;
                _isRunning = false;
                _logger.Information($"StartSmscServerClient {TransceiverSystemId} has exited.");
                //_cancellationTokenSource = new CancellationTokenSource();
            }
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            cancellationToken = new CancellationTokenSource().Token;
            //_cancellationTokenSource = new CancellationTokenSource(); // Reset the CancellationTokenSource on Start
            if (_smscServerProxy == null)
            {
                throw new InvalidOperationException("SmscServerProxy is not initialized.");
            }
            _logger.Information("Starting Background Smsc Server Proxy Service.", "StartAsync", _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.systemId);
            // Start ExecuteAsync in a new task and do not await it here
            if (!_smscServerProxy.SmppServer.IsRun || !_smscServerProxy.SmscServerProxyClient.IsConnected)
            {
                _ = Task.Run(() => ExecuteAsync(cancellationToken));
            }
            // Optionally, you can add a small delay to ensure that ExecuteAsync has started
            await Task.Delay(1000, cancellationToken);
        }

        //// Get the last run time
        //public DateTime GetLastRunTime()
        //{
        //    return _lastRunTime.ToLocalTime();
        //}

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            try
            {
                if (_smscServerProxy != null)
                {
                    _logger.Information("Background Smsc Server Proxy Service is stopping.", "StopAsync", _smscServerProxy.SmscServerProxyClient._clientConnectionSettings.systemId);

                    // Signal cancellation to the running loop in StartSmscServerClient
                    //_cancellationTokenSource.Cancel();

                    // Wait for the base class to stop and perform any additional cleanup
                    await base.StopAsync(cancellationToken);

                    await ClientDisconnect();

                    if (_smscServerProxy.SmppServer.IsRun)
                    {
                        await _smscServerProxy.SmppServer.StopAsync();
                    }
                    _smscServerProxy.Dispose();
                    _smscServerProxy = null!;
                    _logger.Information($"Background Smsc Server Proxy Service for {TransceiverSystemId} stopped.");
                }
                else
                {
                    _logger.Information($"Background Smsc Server Proxy Service for {TransceiverSystemId} is already stopped.");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in StopAsync: {ex.Message}");
            }
        }

        private async Task ClientDisconnect()
        {
            _logger.Information("Disconnect from SMPP server");

            if (_smscServerProxy.SmscServerProxyClient._proxyClient.Status == ConnectionStatus.Bound)
            {
                await ClientUnBind();
            }

            if (_smscServerProxy.SmscServerProxyClient._proxyClient.Status == ConnectionStatus.Open)
            {
                await _smscServerProxy.SmscServerProxyClient._proxyClient.DisconnectAsync();
            }
        }
        private async Task ClientUnBind()
        {
            _logger.Information("Unbind SmppClient");
            UnBindResp resp = await _smscServerProxy.SmscServerProxyClient._proxyClient.UnbindAsync();

            switch (resp.Header.Status)
            {
                case CommandStatus.ESME_ROK:
                    _logger.Information("UnBind succeeded: Status: {0}", resp.Header.Status);
                    break;
                default:
                    _logger.Information("UnBind failed: Status: {0}", resp.Header.Status);
                    await _smscServerProxy.SmscServerProxyClient._proxyClient.DisconnectAsync();
                    break;
            }

        }
        public bool IsRunning()
        {
            return _isRunning;
        }
        public IReadOnlyList<SmppConnectedClients> SmppServerConnectedClients()
        {
            if (_smscServerProxy == null || _smscServerProxy.SmppServer.ConnectedClients.Count == 0)
            {
                return new List<SmppConnectedClients>(); // إرجاع قائمة فارغة بدلاً من null
            }

            var connectedClients = new List<SmppConnectedClients>();

            foreach (var connectedClient in _smscServerProxy.SmppServer.ConnectedClients)
            {
                connectedClients.Add(new SmppConnectedClients
                {
                    ServerSystemId = connectedClient.SystemID,
                    ConnectedClientName = connectedClient.Name,
                    BindingMode = connectedClient.BindingMode.ToString(),
                    RemotEndPoint = connectedClient.RemoteEndPoint.ToString()
                });
            }

            return connectedClients.AsReadOnly(); // إرجاع قائمة للقراءة فقط
        }

        public string GetTransceiverSystemId()
        {
            return _transceiverSystemId;
        }

        public async Task<bool> IsSmscProxyClientWorking()
        {
            if (_smscServerProxy != null)
            {
                if (_smscServerProxy.SmppServer.IsRun && _smscServerProxy.SmscServerProxyClient._proxyClient.Status == ConnectionStatus.Bound)
                {
                    if (!_isRunning)
                    {
                        await StartAsync(new CancellationToken());
                    }
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        public async Task<IBaseSmscResponse> SendSmsAsync(SendSmsSmscCommand sendSmsSmscCommand, CancellationToken cancellationToken)
        {
            if (_smscServerProxy != null)
            {
                if (_smscServerProxy.SmppServer.IsRun && _smscServerProxy.SmscServerProxyClient._proxyClient.Status == ConnectionStatus.Bound)
                {
                    if (!_isRunning)
                    {
                        await StartAsync(new CancellationToken());
                    }
                    return await _smscServerProxy.SmscServerProxyClient.SendMessageAsync(sendSmsSmscCommand);
                }
                else
                {
                    return await SmscGeneralUtilities.MakeResponse(-1, "The SMSC server or the client is down");
                }
            }
            else
            {
                return await SmscGeneralUtilities.MakeResponse(-1, "The SMSC server is not working");
            }
        }

        public async Task<bool> ReInitializeSmscServerProxyClientAsync(CancellationToken cancellationToken)
        {
            try
            {
                if (_smscServerProxy != null)
                {
                    if (_smscServerProxy.SmppServer.IsRun)
                    {
                        return await _smscServerProxy.ReInitializeSmscServerProxyClientAsync();
                    }
                    else
                    {
                        _logger.Information($"Background Smsc Server Proxy Service for {TransceiverSystemId} is not running.");
                        return false;
                    }
                }
                else
                {
                    _logger.Information($"Background Smsc Server Proxy Service for {TransceiverSystemId} is stopped.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in ReInitializeSmscServerProxyClientAsync: {ex.Message}");
                return false;
            }
            
        }
        public SmscServerGatewayProxySettings GetSettings()
        {
            return smscServerGatewayProxySettings;
        }

    }
}