{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "vault"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:80"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "vault"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:443"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "vault"}, "applicationUrl": "https://localhost:44350", "sslPort": 44350}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "443", "ASPNETCORE_HTTP_PORTS": "80"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:37407", "sslPort": 44350}}}