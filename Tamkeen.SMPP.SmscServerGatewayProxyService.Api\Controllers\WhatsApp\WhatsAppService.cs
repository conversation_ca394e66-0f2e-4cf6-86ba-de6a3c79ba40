using System.Threading.Tasks;
using Mapster;
using Microsoft.Extensions.Options;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.WhatsApp;
using Tamkeen.SMPP.SmscServerGatewayProxyService;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp
{
    public class WhatsAppService : IWhatsAppService
    {
        private readonly WhatsAppBackgroundServices _whatsAppBackgroundServices;

        public WhatsAppService(IOptions<FcmWhatsappSettings> appSettings, ITamkeenLogRegister logRegister)
        {
            _whatsAppBackgroundServices = new WhatsAppBackgroundServices(logRegister, appSettings.Value);
        }

        public Task<BaseResponse> SendToWhatsApp(SendToWhatsAppRequest req)
        {
            var command = req.Adapt<SendToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendToWhatsApp(command);
        }

        public Task<BaseResponse> SendToWhatsAppExp(SendToWhatsAppRequest req)
        {
            var command = req.Adapt<SendToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendToWhatsAppExp(command);
        }

        public Task<BaseResponse> SendToWhatsAppCash(SendToWhatsAppRequest req)
        {
            var command = req.Adapt<SendToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendToWhatsAppCash(command);
        }

        public Task<BaseResponse> SendPDFToWhatsApp(SendPDFToWhatsAppRequest req)
        {
            var command = req.Adapt<SendPDFToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendPDFToWhatsApp(command);
        }

        public Task<BaseResponse> SendToWhatsAppAlert(SendToWhatsAppRequest req)
        {
            var command = req.Adapt<SendToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendToWhatsAppAlert(command);
        }

        public Task<BaseResponse> SendToWhatsAppOTP(SendToWhatsAppRequest req)
        {
            var command = req.Adapt<SendToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendToWhatsAppOTP(command);
        }

        public Task<BaseResponse> SendToWhatsAppCKR(SendToWhatsAppRequest req)
        {
            var command = req.Adapt<SendToWhatsAppCommand>();
            return _whatsAppBackgroundServices.SendToWhatsAppCKR(command);
        }
    }
}
