﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Builders;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.Logging;
using Tamkeen.Inetlab.SMPP.PDU;

namespace Tamkeen.SMPP.SmscServerGatewayProxyServiceTestLiveProduction
{
    internal class SmscClient
    {
        private readonly MessageComposer _messageComposer;
        private readonly ILog _log;
        public SmppClient _client;
        public string SystemId { get; private set; }
        public string Password { get; private set; }
        public string Mode { get; private set; }
        public Uri ServerUri { get; private set; }

        public SmscClient(string systemId, string password, string mode, string serverUri)
        {
            LogManager.SetLoggerFactory(new ConsoleLogFactory(LogLevel.Info));
            _client = new SmppClient();
            _client.ResponseTimeout = TimeSpan.FromSeconds(60);
            _client.EnquireLinkInterval = TimeSpan.FromSeconds(20);

            _client.evDisconnected += client_evDisconnected;
            _client.evDeliverSm += client_evDeliverSm;
            _client.evEnquireLink += client_evEnquireLink;
            _client.evUnBind += client_evUnBind;
            _client.evDataSm += client_evDataSm;
            _client.evRecoverySucceeded += ClientOnRecoverySucceeded;
            _client.evServerCertificateValidation += OnCertificateValidation;

            _messageComposer = new MessageComposer();
            _messageComposer.evFullMessageReceived += OnFullMessageReceived;
            _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;

            SystemId = systemId;
            Password = password;
            Mode = mode;
            ServerUri = new Uri(serverUri);
        }

        public async Task<bool> ConnectAsync()
        {
            await _client.RetryUntilConnectedAsync(ServerUri.Host, ServerUri.Port, TimeSpan.FromSeconds(5));
            BindResp bindResp = await _client.BindAsync(SystemId, Password, Enum.Parse<ConnectionMode>(Mode, true));

            if (bindResp.Header.Status == CommandStatus.ESME_ROK)
            {
                _client.Logger.Info("Bind succeeded: SystemId: {0}", bindResp.SystemId);
                return true;
            }
            else
            {
                _client.Logger.Warn("Bind failed: Status: {0}", bindResp.Header.Status);
                return false;
            }
        }

        public async Task SendTestMessageAsync(string from, string to)
        {
            var sourceAddress = new SmeAddress("Cash", AddressTON.Alphanumeric, AddressNPI.Unknown);
            var destinationAddress = new SmeAddress(to, AddressTON.International, AddressNPI.ISDN);
            ISubmitSmBuilder builder = SMS.ForSubmit()
                .From(sourceAddress)
                .To(destinationAddress)
                .Coding(DataCodings.UCS2)
                .Text("Test message from " + from)
                .ExpireIn(TimeSpan.FromMinutes(300))
                .DeliveryReceipt();

            try
            {
                IList<SubmitSmResp> resp = await _client.SubmitAsync(builder);
                if (resp.All(x => x.Header.Status == CommandStatus.ESME_ROK))
                {
                    _client.Logger.Info("Submit succeeded. MessageIds: {0}", string.Join(",", resp.Select(x => x.MessageId)));
                }
                else
                {
                    _client.Logger.Warn("Submit failed. Status: {0}", string.Join(",", resp.Select(x => x.Header.Status.ToString())));
                }
            }
            catch (Exception ex)
            {
                _client.Logger.Error("Submit failed. Error: {0}", ex.Message);
            }
        }
        private void client_evDataSm(object sender, DataSm data)
        {
            _log.Info("DataSm received : Sequence: {0}, SourceAddress: {1}, DestAddress: {2}, Coding: {3}, Text: {4}",
                data.Header.Sequence, data.SourceAddress, data.DestinationAddress, data.DataCoding, data.GetMessageText(_client.EncodingMapper));
        }
        private void client_evDisconnected(object sender) => _log.Info("SmppClient disconnected");
        private void client_evDeliverSm(object sender, DeliverSm data) => _log.Info("DeliverSm received: {0}", data);
        private void client_evEnquireLink(object sender, EnquireLink data) => _log.Info("EnquireLink received");
        private void client_evUnBind(object sender, UnBind data) => _log.Info("UnBind request received");
        private void ClientOnRecoverySucceeded(object sender, BindResp data) => _log.Info("Connection recovered.");
        private void OnCertificateValidation(object sender, CertificateValidationEventArgs args) => args.Accepted = true;
        private void OnFullMessageTimeout(object sender, MessageEventHandlerArgs args) => _log.Info("Message timeout");
        private void OnFullMessageReceived(object sender, MessageEventHandlerArgs args) => _log.Info("Full message received");
    }
}
