﻿using System;
using System.Collections.Concurrent;
using System.Text;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer;

public class RabbitMQMessageStore : IDisposable
{
    private readonly IConnection _connection;
    private readonly IModel _channel;
    private readonly ConcurrentDictionary<string, SmscSMSMessage> _store = new ConcurrentDictionary<string, SmscSMSMessage>();
    private readonly ConcurrentDictionary<string, SmscSMSMessage> _storeByRemoteMessageId = new ConcurrentDictionary<string, SmscSMSMessage>();

    public RabbitMQMessageStore(string hostname, string queueName)
    {
        var factory = new ConnectionFactory() { HostName = hostname };
        _connection = factory.CreateConnection();
        _channel = _connection.CreateModel();
        _channel.QueueDeclare(queueName, false, false, false, null);

        var consumer = new EventingBasicConsumer(_channel);
        consumer.Received += (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var messageContent = Encoding.UTF8.GetString(body);
            var messageId = ea.BasicProperties.MessageId;

            var message = SmscSMSMessage.FromString(messageContent);

            // Store the received message
            Add(message, null); // Assuming null EncodingMapper for simplicity
        };

        _channel.BasicConsume(queue: queueName, autoAck: true, consumer: consumer);
    }

    public void Add(SmscSMSMessage message, EncodingMapper encodingMapper)
    {
        lock (_store)
        {
            if (!_store.TryAdd(message.Id, message))
            {
                throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the same Id '{message.Id}' already exists.");
            }

            if (!string.IsNullOrEmpty(message.RemoteMessageId))
            {
                if (!_storeByRemoteMessageId.TryAdd(message.RemoteMessageId, message))
                {
                    throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the same remote MessageId '{message.RemoteMessageId}' already exists.");
                }
            }
        }
    }

    public SmscSMSMessage Update(string messageId, Action<SmscSMSMessage> update, EncodingMapper encodingMapper)
    {
        lock (_store)
        {
            return _store.AddOrUpdate(messageId,
                id => throw new InvalidOperationException($"SMS message with id {messageId} not found."),
                (s, message) =>
                {
                    update(message);

                    if (!string.IsNullOrEmpty(message.RemoteMessageId))
                    {
                        _storeByRemoteMessageId.TryAdd(message.RemoteMessageId, message);
                    }

                    return message;
                });
        }
    }

    public bool TryUpdateByRemoteMessageId(string remoteMessageId, Action<SmscSMSMessage> update, EncodingMapper encodingMapper, out SmscSMSMessage message)
    {
        lock (_store)
        {
            if (!_storeByRemoteMessageId.TryGetValue(remoteMessageId, out message)) return false;

            update(message);
            return true;
        }
    }

    public void Remove(string messageId)
    {
        SmscSMSMessage oldMessage;

        lock (_store)
        {
            if (!_store.TryRemove(messageId, out oldMessage))
            {
                throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the Id '{messageId}' doesn't exist.");
            }

            if (!string.IsNullOrEmpty(oldMessage.RemoteMessageId))
            {
                if (!_storeByRemoteMessageId.TryRemove(oldMessage.RemoteMessageId, out _))
                {
                    throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the Id '{oldMessage.RemoteMessageId}' doesn't exist.");
                }
            }
        }
    }

    public void SendMessage(SmscSMSMessage message, EncodingMapper encodingMapper, string queueName)
    {
        var body = Encoding.UTF8.GetBytes(message.ToString());
        var properties = _channel.CreateBasicProperties();
        properties.MessageId = message.Id;
        properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());

        _channel.BasicPublish(exchange: "", routingKey: queueName, basicProperties: properties, body: body);

        Add(message, encodingMapper);
    }

    public void Dispose()
    {
        _channel?.Close();
        _connection?.Close();
    }
}
