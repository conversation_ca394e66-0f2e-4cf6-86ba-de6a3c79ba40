services:
  tamkeen.smpp.smscservergatewayproxyservice.api:
    container_name: Tamkeen.SMPP.SmscServerGatewayProxyService.Api
    build:
      context: .
      dockerfile: Tamkeen.SMPP.SmscServerGatewayProxyService.Api/Dockerfile
    image: tamkeensmppsmscservergatewayproxyserviceapi:latest
    ports:
      - "5015:5015"
      - "7020:7020"
      - "8080:8080"
      - "8081:8081"
      - "28013:28013"
      - "28014:28014"
      - "28016:28016"
      - "28020:28020"
      - "28094:28094"
      - "28095:28095"
      - "29000:29000"
    env_file:
      - .env
    environment:
      ASPNETCORE_ENVIRONMENT: "Docker"
      ASPNETCORE_URLS: "http://+:5015;http://+:7020"
    depends_on: []
