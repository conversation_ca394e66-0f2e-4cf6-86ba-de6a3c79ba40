using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;
using Xunit;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Tests.Integration
{
    public class ApiIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public ApiIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureAppConfiguration((context, config) =>
                {
                    config.AddInMemoryCollection(new Dictionary<string, string>
                    {
                        ["Logging:LogLevel:Default"] = "Warning",
                        ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:enable"] = "true",
                        ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:transceiverSystemId"] = "YemenMobile",
                        ["SmscSettings:SmscYemenMobileServerGatewayProxySettings:SmscClientSettings:mobilePattern"] = "^(96777|77)",
                        ["FcmWhatsappSettings:DalUrl"] = "http://localhost:8083",
                        ["FcmWhatsappSettings:authToken"] = "test-token"
                    });
                });

                builder.ConfigureServices(services =>
                {
                    // Replace real services with mocks for testing
                    services.AddSingleton(Mock.Of<SmscProxyServiceManager>());
                    services.AddSingleton(Mock.Of<SmscServiceManager>());
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task HealthCheck_ReturnsHealthy()
        {
            // Act
            var response = await _client.GetAsync("/health");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Healthy");
        }

        [Fact]
        public async Task Swagger_IsAccessible()
        {
            // Act
            var response = await _client.GetAsync("/swagger/index.html");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task SendSmsNewVersionAsync_WithValidRequest_ReturnsOk()
        {
            // Arrange
            var request = new SendSmsSmscRequest
            {
                transceiverSystemId = "YemenMobile",
                dstAdr = "96777123456",
                smsText = "Test message",
                srcTon = Tamkeen.Inetlab.SMPP.Common.AddressTON.Alphanumeric,
                srcNpi = Tamkeen.Inetlab.SMPP.Common.AddressNPI.Unknown,
                deliveryReceipt = Tamkeen.Inetlab.SMPP.Common.SMSCDeliveryReceipt.SuccessOrFailure
            };

            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content);

            // Assert
            response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.BadRequest);
            // Note: BadRequest is acceptable here since we're using mocked services
        }

        [Fact]
        public async Task SendSmsNewVersionAsync_WithInvalidRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new { }; // Empty request
            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task SendSmsNewVersionAsync_WithMalformedJson_ReturnsBadRequest()
        {
            // Arrange
            var malformedJson = "{ invalid json }";
            var content = new StringContent(malformedJson, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData(null)]
        public async Task SendSmsNewVersionAsync_WithEmptyPhoneNumber_ReturnsBadRequest(string phoneNumber)
        {
            // Arrange
            var request = new SendSmsSmscRequest
            {
                transceiverSystemId = "YemenMobile",
                dstAdr = phoneNumber,
                smsText = "Test message"
            };

            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData(null)]
        public async Task SendSmsNewVersionAsync_WithEmptyMessage_ReturnsBadRequest(string message)
        {
            // Arrange
            var request = new SendSmsSmscRequest
            {
                transceiverSystemId = "YemenMobile",
                dstAdr = "96777123456",
                smsText = message
            };

            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task Api_HandlesLargePayload()
        {
            // Arrange
            var largeMessage = new string('A', 1000); // 1000 character message
            var request = new SendSmsSmscRequest
            {
                transceiverSystemId = "YemenMobile",
                dstAdr = "96777123456",
                smsText = largeMessage
            };

            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content);

            // Assert
            response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.BadRequest);
            // The API should handle large payloads gracefully
        }

        [Fact]
        public async Task Api_ReturnsCorrectContentType()
        {
            // Act
            var response = await _client.GetAsync("/health");

            // Assert
            response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
        }

        [Fact]
        public async Task Api_HandlesConcurrentRequests()
        {
            // Arrange
            var request = new SendSmsSmscRequest
            {
                transceiverSystemId = "YemenMobile",
                dstAdr = "96777123456",
                smsText = "Concurrent test message"
            };

            var json = JsonConvert.SerializeObject(request);
            var tasks = new List<Task<HttpResponseMessage>>();

            // Act - Send 10 concurrent requests
            for (int i = 0; i < 10; i++)
            {
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                tasks.Add(_client.PostAsync("/api/SendSmsc/SendSmsNewVersionAsync", content));
            }

            var responses = await Task.WhenAll(tasks);

            // Assert
            responses.Should().HaveCount(10);
            responses.Should().OnlyContain(r => 
                r.StatusCode == HttpStatusCode.OK || 
                r.StatusCode == HttpStatusCode.BadRequest ||
                r.StatusCode == HttpStatusCode.InternalServerError);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _client?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
