﻿
using System;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Shared
{
    public class RedisCalls
    {
        private static string redisHost;//= @"*************";
        private static int redisPort;//= 6379;
        public RedisCalls(string _redisHost, int _redisPort)
        {
            redisHost = _redisHost;
            redisPort = _redisPort;
        }
        public async Task SaveInRedis<T>(string key, T value, int timestamp = 5 * 60)
        {
            try
            {
                RedisClass obj = new RedisClass(redisHost, redisPort);
                
                    var cache = obj.Connection.GetDatabase();
                    //int tt =Convert.ToInt32( TimeSpan.FromMinutes(Convert.ToInt32(timestamp / 60)));
                    var data = Newtonsoft.Json.JsonConvert.SerializeObject(value, Newtonsoft.Json.Formatting.Indented);
                    var result = await cache.StringSetAsync(key, data, TimeSpan.FromSeconds((timestamp)));
                
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<T> RetrieveFromRedis<T>(string key)
        {
            try
            {
                RedisClass obj = new RedisClass(redisHost, redisPort);
                
                    var cache = obj.Connection.GetDatabase();
                    var value = await cache.StringGetAsync(key);
                    if (!value.IsNull)
                        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(value);
                    else
                    {
                        return default(T);
                    }
                
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<T> RetrieveFromRedisSync<T>(string key)
        {
            try
            {
                RedisClass obj = new RedisClass(redisHost, redisPort);
                
                    var cache = obj.Connection.GetDatabase();
                    var value = cache.StringGet(key);
                    if (!value.IsNull)
                        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(value);
                    else
                    {
                        return default(T);
                    }
                
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<string> RetrieveFromRedisReg<T>(string key)
        {
            try
            {
                RedisClass obj = new RedisClass(redisHost, redisPort);
                
                    var cache = obj.Connection.GetDatabase();
                    var value = await cache.StringGetAsync(key);
                    if (!value.IsNull)
                    {
                        return value;
                    }
                    else
                    {
                        return string.Empty;
                    }
                
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<bool> DeleteRedisKey(string key)
        {
            try
            {
                RedisClass obj = new RedisClass(redisHost, redisPort);
                
                    var cache = obj.Connection.GetDatabase();
                    //int tt =Convert.ToInt32( TimeSpan.FromMinutes(Convert.ToInt32(timestamp / 60)));
                    return await cache.KeyDeleteAsync(key);

                
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
