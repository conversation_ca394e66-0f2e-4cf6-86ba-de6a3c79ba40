﻿
using Microsoft.Extensions.Hosting;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public interface ISmscServerGatewayProxyBackgroundService : IHostedService
    {
        string TransceiverSystemId { get; }
        IReadOnlyList<SmppConnectedClients> SmppServerConnectedClients();
        bool IsRunning();
        Task RestartServiceAsync(CancellationToken cancellationToken);
        Task StopAsync(CancellationToken cancellationToken);
        string GetTransceiverSystemId();
        Task StartAsync(CancellationToken cancellationToken);
        Task<bool> IsSmscProxyClientWorking();
        Task<IBaseSmscResponse> SendSmsAsync(SendSmsSmscCommand sendSmsSmscCommand, CancellationToken cancellationToken);
        Task<bool> ReInitializeSmscServerProxyClientAsync(CancellationToken cancellationToken);
        public SmscServerGatewayProxySettings GetSettings();

    }
}