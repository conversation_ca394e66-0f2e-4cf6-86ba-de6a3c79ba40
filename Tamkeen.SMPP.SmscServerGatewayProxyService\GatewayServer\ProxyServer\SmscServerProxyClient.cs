﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Mail;
using System.Security.Authentication;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Builders;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.Logging;
using Tamkeen.Inetlab.SMPP.Parameters;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Interfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities;
using Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer
{
    public class SmscServerProxyClient : IDisposable
    {
        public SmscClientSettings _clientConnectionSettings { get; private set; }
        public readonly SmppClient _proxyClient;
        private readonly SmppServer _proxyServer;
        public SmppServerClient _proxyServerClient;
        private readonly SmscMessagesBridge _bridge;
        private readonly ILogger _logger;
        public readonly MessageComposer _messageComposer;
        public readonly SmscRabbitMQSendSmsServiceOld _smscRabbitMQSendSmsService;
        //private readonly SmscRabbitMQSendSmsServiceVersion2 _smscRabbitMQSendSmsServiceVersion2;
        //public readonly ProcessInteraptedSubmitSm _processInteraptedSubmitSm;
        private bool _disposed = false;
        public bool _isConnectedOnce = false;
        /// <summary>
        /// Initializes a new instance of the <see cref="SmscServerProxyClient"/> class.
        /// </summary>
        /// <param name="proxyServer">The SMPP server instance.</param>
        /// <param name="logger">The logger instance for logging information.</param>
        /// <param name="clientSettings">Settings for the SMPP client.</param>
        /// <param name="smscRabbitMQSendSmsServiceVersion2">RabbitMQ service for sending SMS messages.</param>
        /// <param name="serviceProvider">The service provider for resolving dependencies.</param>
        public SmscServerProxyClient(SmppServer proxyServer, ILogger logger, SmscServerGatewayProxySettings smscServerGatewayProxySettings, IServiceProvider serviceProvider)
        {
            _smscRabbitMQSendSmsService = new SmscRabbitMQSendSmsServiceOld(serviceProvider, smscServerGatewayProxySettings.smscClientSettings.transceiverSystemId);
            //_smscRabbitMQSendSmsServiceVersion2 = smscRabbitMQSendSmsServiceVersion2 ?? throw new ArgumentNullException(nameof(_smscRabbitMQSendSmsServiceVersion2));
            _clientConnectionSettings = smscServerGatewayProxySettings.smscClientSettings ?? throw new ArgumentNullException(nameof(_clientConnectionSettings)); ;
            //_processInteraptedSubmitSm = new ProcessInteraptedSubmitSm(serviceProvider);
            //_processInteraptedSubmitSm._smscRabbitMQSendSmsService = _smscRabbitMQSendSmsService;
            #region MessageComposer
            // Initialize MessageComposer for handling concatenated messages
            _messageComposer = new MessageComposer();
            _messageComposer.evFullMessageReceived += OnFullMessageReceived;
            _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;
            #endregion

            #region Bridg
            // Initialize message bridge for forwarding receipts and managing message state
            ISmscSMSMessageStore store = new SmscInMemoryMessageStore();
            _bridge = new SmscMessagesBridge(store);
            #endregion

            _bridge.ReceiptReadyForForward += WhenReceiptReadyForForward;

            // Initialize SMPP client and set up event handlers
            _proxyClient = new SmppClient
            {
                ResponseTimeout= TimeSpan.FromSeconds(60),
                EnquireLinkInterval= TimeSpan.FromSeconds(20),
                ConnectionRecovery = true,
                ConnectionRecoveryDelay = TimeSpan.FromSeconds(5),
                ReceiveTaskScheduler = TaskScheduler.Default,
                Name = _clientConnectionSettings.transceiverSystemId,
                EsmeAddress = new SmeAddress(_clientConnectionSettings.addressRange, _clientConnectionSettings.ton, _clientConnectionSettings.npi),
                EnabledSslProtocols = SslProtocols.None
        };

            if (!string.IsNullOrEmpty(_clientConnectionSettings.systemType))
            {
                _proxyClient.SystemType = _clientConnectionSettings.systemType;
            }

            _proxyClient.evDeliverSm += new DeliverSmEventHandler(client_evDeliverSm);
            _proxyClient.evDeliverSm += (sender, data) =>
            {
                if (data.MessageType == MessageTypes.SMSCDeliveryReceipt)
                {
                    Task.Run(() => _bridge.ReceiptReceived(data))
                        .ContinueWith(t =>
                        {
                            _proxyClient.Logger.Error(t.Exception, SmscErrorMessages.ReceiptReceivedFailed);
                        }, TaskContinuationOptions.OnlyOnFaulted);
                }
                else
                {
                    Task.Run(() => ForwardDeliverSm(data));
                }
            };

            _proxyClient.evUnBind += new UnBindEventHandler(client_evUnBind);
            _proxyClient.evDisconnected += new DisconnectedEventHandler(client_evDisconnected);
            _proxyClient.evRecoverySucceeded += ClientOnRecoverySucceeded;
            _smscRabbitMQSendSmsService._smppClient = this;
            _proxyServer = proxyServer;
            _logger = logger;

        }

        /// <summary>
        /// Asynchronously runs the proxy client and connects to the SMPP server.
        /// </summary>
        /// <param name="uri">The URI of the SMPP server.</param>
        /// <param name="mode">The connection mode (Transmitter, Receiver, or Transceiver).</param>
        /// <param name="systemId">The system ID for authentication.</param>
        /// <param name="password">The password for authentication.</param>
        /// <param name="connectionRecovery">Specifies whether connection recovery is enabled.</param>
        //public async Task RunAsync(Uri uri, ConnectionMode mode, string systemId, string password, bool connectionRecovery)
        //{


        //    // Create a CancellationTokenSource with a timeout of 1 second
        //    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(1)))
        //    {
        //        try
        //        {
        //            // Attempt to connect to the SMPP server
        //            await _proxyClient.RetryUntilConnectedAsync(uri.Host, uri.Port, TimeSpan.FromSeconds(5), 20, cancellationTokenSource.Token);
        //            //BindResp bindResp = await _proxyClient.BindAsync("TamOT", "T123", mode);
        //            BindResp bindResp = await _proxyClient.BindAsync(systemId, password, mode);
        //            _logger.Information("smscClient.RunAsync completed within 1 second");
        //            if (bindResp.Header.Status == CommandStatus.SMPPCLIENT_NOCONN)
        //            {
        //                _proxyClient.ConnectionRecovery = false;
        //            }
        //            else
        //            {
        //                _proxyClient.ConnectionRecovery = connectionRecovery;
        //                _isConnectedOnce = true;
        //            }
        //            // Send unacknowledged SMS messages upon successful connection
        //            //var connectTask = Task.Run(() => _smscRabbitMQSendSmsServiceVersion2.SendUnackSMS());
        //            //if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30))) == connectTask)
        //            //{
        //            //    _logger.Information($"RabbitMQ {_smscRabbitMQSendSmsServiceVersion2._queueName} started.");
        //            //}
        //            //else
        //            //{
        //            //    _logger.Information($"RabbitMQ  {_smscRabbitMQSendSmsServiceVersion2._queueName} connection attempt timed out.");
        //            //}
        //        }
        //        catch (OperationCanceledException)
        //        {
        //            _logger.Information("Timeout occurred");
        //        }
        //    }

        //    // Bind the client to the SMPP server

        //}

        //public async Task RunAsync()
        //{
        //    // Create a CancellationTokenSource with a timeout of 1 second
        //    using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
        //    {
        //        try
        //        {
        //            Uri uri = new Uri(_clientConnectionSettings.clientUri);
        //            // Attempt to connect to the SMPP server
        //            await _proxyClient.RetryUntilConnectedAsync(uri.Host, uri.Port, TimeSpan.FromSeconds(5), 20, cancellationTokenSource.Token);
        //            //BindResp bindResp = await _proxyClient.BindAsync("TamOT", "T123", mode);
        //            if (_proxyClient.Status == ConnectionStatus.Open&&_proxyClient.Status!= ConnectionStatus.Bound)
        //            {
        //                // Bind the client to the SMPP server
        //                BindResp bindResp = await _proxyClient.BindAsync(_clientConnectionSettings.transceiverSystemId, _clientConnectionSettings.password, _clientConnectionSettings.connectionMode);
        //                _logger.Information("SmscClient.RunAsync completed within 1 second");
        //                if (bindResp.Header.Status == CommandStatus.SMPPCLIENT_NOCONN)
        //                {
        //                    _proxyClient.IsClientConnected = false;
        //                }
        //                else
        //                {
        //                    _proxyClient.ConnectionRecovery = _clientConnectionSettings.connectionRecovery;
        //                    _isConnectedOnce = true;
        //                }
        //                //Send unacknowledged SMS messages upon successful connection
        //                var connectTask = Task.Run(() => _smscRabbitMQSendSmsService.SendUnackSMS());
        //                if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30))) == connectTask)
        //                {
        //                    _logger.Information($"RabbitMQ {_smscRabbitMQSendSmsService._queueName} started.");
        //                }
        //                else
        //                {
        //                    _logger.Information($"RabbitMQ  {_smscRabbitMQSendSmsService._queueName} connection attempt timed out.");
        //                } 
        //            }
        //        }
        //        catch (OperationCanceledException)
        //        {
        //            _logger.Information("Timeout occurred");
        //        }
        //    }
        //}

        private bool _isRunning; // To track if RunAsync is in progress
        private readonly SemaphoreSlim _runSemaphore = new SemaphoreSlim(1, 1); // To control concurrent access

        public async Task<bool> RunAsync()
        {
            // Ensure only one task runs at a time for this client
            await _runSemaphore.WaitAsync();

            bool success = false; // Variable to track success or failure

            try
            {
                if (_isRunning)
                {
                    _logger.Information(SmscNotificationMessages.RunAsyncAlreadyInProgress, _clientConnectionSettings.transceiverSystemId);
                    return false; // Return false because the operation was already in progress
                }

                _isRunning = true; // Mark that the process is running

                // Create a CancellationTokenSource with a timeout of 30 seconds
                using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                {
                    try
                    {
                        Uri uri = new Uri(_clientConnectionSettings.clientUri);

                        bool bSuccess = await _proxyClient.ConnectAsync(uri.Host, uri.Port);
                        if (!bSuccess)
                        {
                            // Attempt to connect to the SMPP server
                            await _proxyClient.RetryUntilConnectedAsync(uri.Host, uri.Port, TimeSpan.FromSeconds(5), 20, cancellationTokenSource.Token);

                        }
                        // Check if the connection is open but not yet bound
                        if (_proxyClient.Status == ConnectionStatus.Open && _proxyClient.Status != ConnectionStatus.Bound)
                        {
                            // Bind the client to the SMPP server
                            BindResp bindResp = await _proxyClient.BindAsync(_clientConnectionSettings.systemId, _clientConnectionSettings.password, _clientConnectionSettings.connectionMode);

                            _logger.Information("SmscClient.RunAsync completed within 30 seconds");
                            switch (bindResp.Header.Status)
                            {
                                case CommandStatus.ESME_ROK:
                                    _logger.Information(SmscNotificationMessages.BindSucceeded, bindResp.Header.Status, bindResp.SystemId);


                                    break;
                                default:
                                    _logger.Warning(SmscNotificationMessages.BindFailed, bindResp.Header.Status);

                                    break;
                            }
                            if (bindResp.Header.Status == CommandStatus.SMPPCLIENT_NOCONN)
                            {
                                _proxyClient.IsClientConnected = false;
                                return false; // Failed to bind, return false
                            }
                            else
                            {
                                _proxyClient.ConnectionRecovery = _clientConnectionSettings.connectionRecovery;
                                _isConnectedOnce = true;
                            }

                            // Send unacknowledged SMS messages upon successful connection
                            var connectTask = Task.Run(() => _smscRabbitMQSendSmsService.SendUnackSMS());
                            if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30))) == connectTask)
                            {
                                _logger.Information(RabbitMQErrorNotiMessages.RabbitMqStarted, _smscRabbitMQSendSmsService._queueName);
                            }
                            else
                            {
                                _logger.Information(RabbitMQErrorNotiMessages.RabbitMqTimeOut, _smscRabbitMQSendSmsService._queueName);
                                return false; // Timed out connecting to RabbitMQ, return false
                            }
                        }

                        success = true; // If all operations complete successfully, set success to true
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.Information("Timeout occurred while connecting.");
                        success = false; // Operation was canceled or timed out, set success to false
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error occurred in RunAsync: {ex.Message}");
                success = false; // An exception occurred, set success to false
            }
            finally
            {
                _isRunning = false; // Mark that the process has finished
                _runSemaphore.Release(); // Release the semaphore
                _logger.Information($"RunAsync for {_clientConnectionSettings.transceiverSystemId} has completed.");
            }

            return success; // Return whether the operation was successful or not
        }

        /// <summary>
        /// Handles the event when the client successfully recovers from a connection loss.
        /// </summary>
        private async void ClientOnRecoverySucceeded(object sender, BindResp data)
        {
            _proxyClient.IsClientConnected = true;
            var connectTask = Task.Run(() => _smscRabbitMQSendSmsService.SendUnackSMS());
            if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30))) == connectTask)
            {
                _logger.Information($"RabbitMQ {_smscRabbitMQSendSmsService._queueName} started.");
            }
            else
            {
                _logger.Information($"RabbitMQ  {_smscRabbitMQSendSmsService._queueName} connection attempt timed out.");
            }
        }

        /// <summary>
        /// Handles the UnBind event when the client receives an UnBind request.
        /// </summary>
        private void client_evUnBind(object sender, UnBind data)
        {
            _logger.Information("UnBind request received from " + _proxyClient.Name);
        }

        /// <summary>
        /// Handles the Disconnected event when the client disconnects from the SMPP server.
        /// </summary>
        private void client_evDisconnected(object sender)
        {
            _proxyClient.IsClientConnected = false;
            _logger.Information(_proxyClient.Name + " disconnected");
        }

        /// <summary>
        /// Forwards DeliverSm messages to the appropriate destination.
        /// </summary>
        /// <param name="data">The DeliverSm PDU received from the SMPP server.</param>
        private async Task ForwardDeliverSm(DeliverSm data)
        {
            try
            {
                var client = FindDestination(data.DestinationAddress);
                if (client != null)
                {
                    DeliverSmResp resp = await client.DeliverAsync(data);
                }
                else
                {
                    //await this._proxyServer.(data);
                    //client = data.Client;
                    // Save DeliverSm for delivery when the client connects
                }
            }
            catch (Exception ex)
            {
                _proxyClient.Logger.Error(ex, SmscErrorMessages.FailedForwardDeliverSm, data);
            }
        }

        /// <summary>
        /// Finds the destination client for the given address.
        /// </summary>
        /// <param name="address">The destination address to find the client for.</param>
        /// <returns>The matching SmppServerClient or null if not found.</returns>
        private SmppServerClient FindDestination(SmeAddress address)
        {
            foreach (SmppServerClient client in _proxyServer.ConnectedClients)
            {
                if (client.BindingMode == ConnectionMode.Transmitter) continue;

                if (!string.IsNullOrEmpty(client.EsmeAddress?.Address))
                {
                    Regex regex = new Regex(client.EsmeAddress.Address);
                    if (regex.IsMatch(address.Address))
                    {
                        return client;
                    }
                }
                else if(address.Address =="6655" || address.Address.ToLower()=="cash")
                {
                    return client;
                }
            }

            throw new InvalidOperationException($"No destination found for address {address.Address}.");
        }


        /// <summary>
        /// Handles the DeliverSm event when a message is received from the SMPP server.
        /// </summary>
        private async void client_evDeliverSm(object sender, DeliverSm data)
        {
            try
            {
                // Check if we received a Delivery Receipt
                if (data.MessageType == MessageTypes.SMSCDeliveryReceipt)
                {
                    string messageId = data.Receipt.MessageId + "-0";
                    MessageState deliveryStatus = data.Receipt.State;

                    _logger.Information(nameof(SmscServerProxyClient) + "-1:Delivery Receipt received: {0}", data.Receipt.ToString());
                }
                else
                {
                    // Handle concatenated message parts
                    if (data.Concatenation != null)
                    {
                        _messageComposer.AddMessage(data);

                        _logger.Information(nameof(SmscServerProxyClient) + "-2:DeliverSm part received: Sequence: {0}, SourceAddress: {1}, Concatenation ( {2} )" +
                                " Coding: {3}, Text: {4}",
                                data.Header.Sequence, data.SourceAddress, data.Concatenation, data.DataCoding, _proxyClient.EncodingMapper.GetMessageText(data));
                    }
                    else
                    {
                        _logger.Information(nameof(SmscServerProxyClient) + "-3:DeliverSm received : Sequence: {0}, SourceAddress: {1}, Coding: {2}, Text: {3}",
                            data.Header.Sequence, data.SourceAddress, data.DataCoding, _proxyClient.EncodingMapper.GetMessageText(data));
                    }

                    if (_proxyServerClient != null)
                    {
                        DeliverSmResp resp = await _proxyServerClient.DeliverAsync(data);
                        if (resp.Header.Status == CommandStatus.ESME_ROK)
                        {
                            _proxyServer.Logger.Info($"Delivered the Receipt: {resp}");
                        }
                        else
                        {
                            _proxyServer.Logger.Warn($"Failed to deliver the Receipt: {resp}");
                            _bridge.FailedToDeliverReceipt(data.Receipt.MessageId);
                        }
                    }

                    // Check if an ESME acknowledgement is required
                    if (data.Acknowledgement != SMEAcknowledgement.NotRequested)
                    {
                        string messageText = data.GetMessageText(_proxyClient.EncodingMapper);

                        var smBuilder = SMS.ForSubmit()
                            .From(data.DestinationAddress)
                            .To(data.SourceAddress)
                            .Coding(data.DataCoding)
                            .Concatenation(ConcatenationType.UDH8bit, _proxyClient.SequenceGenerator.NextReferenceNumber())
                            .Set(m => m.MessageType = MessageTypes.SMEDeliveryAcknowledgement)
                            .Text(new Receipt
                            {
                                DoneDate = DateTime.Now,
                                State = MessageState.Delivered,
                                ErrorCode = "0",
                                SubmitDate = DateTime.Now,
                                Text = messageText.Substring(0, Math.Min(20, messageText.Length))
                            }.ToString());
                        if (_clientConnectionSettings.optionalParameterList != null)
                        {
                            byte[] bytesarr = Encoding.UTF8.GetBytes(_clientConnectionSettings.optionalParameterList.value);
                            // Assuming _clientConnectionSettings.optionalParameterList.tag is a string
                            if (ushort.TryParse(_clientConnectionSettings.optionalParameterList.tag, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort))
                            {
                                smBuilder.AddParameter(tagAsUshort, bytesarr);
                            }
                        }
                        await _proxyClient.SubmitAsync(smBuilder);
                    }
                }
            }
            catch (Exception ex)
            {
                data.Response.Header.Status = CommandStatus.ESME_RX_T_APPN;
                _logger.Error(ex, "Failed to process DeliverSm");
            }
        }

        #region BridgFunction
        /// <summary>
        /// Handles the event when a receipt is ready for forwarding to the appropriate client.
        /// It's return the receipt to the appropriate client.
        /// </summary>
        private async Task WhenReceiptReadyForForward(string systemId, DeliverSm data)
        {
            var client = _proxyServer.ConnectedClients.FirstOrDefault(x => x.SystemID == systemId
    && (x.BindingMode == ConnectionMode.Receiver || x.BindingMode == ConnectionMode.Transceiver));

            if (client == null)
            {
                _proxyServer.Logger.Warn($"Unable to find the client '{systemId}' for the Receipt: {data}");
                return; // Early return if client is null
            }

            // Continue with the existing code as client is not null
            DeliverSmResp resp = await client.DeliverAsync(data);
            if (resp.Header.Status == CommandStatus.ESME_ROK)
            {
                _bridge.ReceiptDelivered(data.Receipt.MessageId);
            }
            else
            {
                _proxyServer.Logger.Warn($"Failed to deliver the Receipt: {resp}");
                _bridge.FailedToDeliverReceipt(data.Receipt.MessageId);
            }


        }
        private async Task<TimeSpan> GetTimeSpanFromValidityPeriod(string validityPeriod)
        {
            // Remove the '+' if it exists at the end of the string
            if (validityPeriod.EndsWith("+"))
            {
                validityPeriod = validityPeriod.Substring(0, validityPeriod.Length - 1);
            }

            // Validate the format (length and numeric content)
            if (validityPeriod.Length != 15 || !long.TryParse(validityPeriod, out _))
            {
                throw new ArgumentException("Invalid format. Validity period must be 15 digits long.");
            }

            // Parse components from the validity period string
            int year = int.Parse("20" + validityPeriod.Substring(0, 2)); // Extract year (20xx)
            int month = int.Parse(validityPeriod.Substring(2, 2));       // Extract month
            int day = int.Parse(validityPeriod.Substring(4, 2));         // Extract day
            int hour = int.Parse(validityPeriod.Substring(6, 2));        // Extract hour
            int minute = int.Parse(validityPeriod.Substring(8, 2));      // Extract minute
            int second = int.Parse(validityPeriod.Substring(10, 2));     // Extract second
            int millisecond = int.Parse(validityPeriod.Substring(12, 3));// Extract millisecond

            // Validate the extracted values
            if (month < 1 || month > 12 || day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59)
            {
                throw new ArgumentException("Invalid date or time components in the validity period.");
            }

            // Create a DateTime object from the extracted components
            DateTime parsedDateTime = new DateTime(year, month, day, hour, minute, second, millisecond, DateTimeKind.Utc);

            // Get the current time in UTC
            DateTime currentTime = DateTime.UtcNow;

            // Calculate the TimeSpan
            TimeSpan timeSpan = parsedDateTime - currentTime;

            return timeSpan;
        }
        /// <summary>
        /// Forwards SubmitSm messages to the SMPP client and processes the response.
        /// </summary>
        public async Task ForwardSubmitSm(SmppServerClient serverClient, SubmitSm data)
        {
            try
            {
                _logger.Information("Full message received From: {0}:{1}, Goes through {2}, To: {3}, Text: {4}", serverClient.Name, serverClient.RemoteEndPoint, serverClient.SystemID , data.DestinationAddress, data.MessageText);
                string messageId = data.Response.MessageId;
                if (data.ValidityPeriod.Contains("+"))
                {
                    TimeSpan validityTimeSpan = await GetTimeSpanFromValidityPeriod(data.ValidityPeriod);
                    data.ValidityPeriod = SmppTime.Format(validityTimeSpan);
                }

                //data.ValidityPeriod = SmppTime.Format(TimeSpan.FromSeconds(2));
                //250109143122012+ 000002000000000R
                //data.ValidityPeriod = "250109200000000+";
                _bridge.SubmitReceived(messageId, data);

                SubmitSm submitSm = data.ClonePDU();
                submitSm.Header.Sequence = 0;
                //Sent the SubmitSm to the SMPP client
                SubmitSmResp resp = await _proxyClient.SubmitWithRepeatAsync(submitSm, TimeSpan.FromSeconds(5));

                if (resp.Header.Status == CommandStatus.ESME_ROK)
                {
                    _logger.Information("Full message deliverd To: {0} through server:{1} with messageId:{2}, and resp: {3}", data.DestinationAddress, serverClient.Server.Name, messageId, resp.Header.Status);
                    _bridge.SubmitForwarded(messageId, resp);
                }
                //else if (resp.Header.Status == CommandStatus.ESME_RSYSERR)
                //{
                //    //await _processInteraptedSubmitSm.ProcessInteraptedSubmitSmAsync(submitSm, serverClient);
                //}
                else if (resp.Header.Status == CommandStatus.SMPPCLIENT_NOCONN || resp.Header.Status == CommandStatus.SMPPCLIENT_UNBOUND)
                {
                    await _smscRabbitMQSendSmsService.ProcessInteraptedSubmitSmAsync(submitSm, _proxyServerClient);
                }
                else
                {
                    _logger.Error(messageId, resp);
                    _bridge.SubmitForwarded(messageId, resp);
                }

                if (data.SMSCReceipt == SMSCDeliveryReceipt.NotRequested)
                {
                    _bridge.DeliveryReceiptNotRequested(messageId);
                }
                else if (resp.Header.Status != CommandStatus.ESME_ROK)
                {
                    _ = SendUndeliverableReceiptAsync(serverClient, data);
                }
            }
            catch (Exception ex)
            {
                try
                {
                    SubmitSm submitSm = data.ClonePDU();
                    submitSm.Header.Sequence = 0;
                    await _smscRabbitMQSendSmsService.ProcessInteraptedSubmitSmAsync(submitSm, _proxyServerClient);
                    serverClient.Logger.Info("The following SMS will be send by RabbitMQ :" + submitSm.ToString());
                }
                catch (Exception)
                {
                    serverClient.Logger.Error(ex, "Failed to process SubmitSm and save message in the RabbitMQ.");
                }
                //serverClient.Logger.Error(ex, "Failed to process SubmitSm.");
            }
        }
        
        //public async Task<SubmitSmResp> ForwardSubmitSmFromRabbitMQ(SmppServerClient serverClient, SubmitSm data)
        //{
        //    try
        //    {
        //        string messageId = data.Response.MessageId;

        //        _bridge.SubmitReceived(messageId, data);

        //        SubmitSm submitSm = data.ClonePDU();
        //        submitSm.Header.Sequence = 0;
        //        //Sent the SubmitSm to the SMPP client
        //        SubmitSmResp resp = await _proxyClient.SubmitWithRepeatAsync(submitSm, TimeSpan.FromSeconds(1));

        //        if (resp.Header.Status == CommandStatus.ESME_ROK)
        //        {
        //            _bridge.SubmitForwarded(messageId, resp);
        //        }
        //        //else if (resp.Header.Status == CommandStatus.ESME_RSYSERR)
        //        //{
        //        //    //await _processInteraptedSubmitSm.ProcessInteraptedSubmitSmAsync(submitSm, serverClient);
        //        //}
        //        //else if (resp.Header.Status == CommandStatus.SMPPCLIENT_NOCONN)
        //        //{
        //        //    await _smscRabbitMQSendSmsService.ProcessInteraptedSubmitSmAsync(submitSm, _proxyServerClient);
        //        //}

        //        if (data.SMSCReceipt == SMSCDeliveryReceipt.NotRequested)
        //        {
        //            _bridge.DeliveryReceiptNotRequested(messageId);
        //        }
        //        else if (resp.Header.Status != CommandStatus.ESME_ROK)
        //        {
        //            _ = SendUndeliverableReceiptAsync(serverClient, data);
        //        }
        //        return resp;
        //    }
        //    catch (Exception ex)
        //    {
        //        serverClient.Logger.Error(ex, "Failed to process SubmitSm.");
        //        SubmitSmResp submitSmResp = new SubmitSmResp()
        //        {
        //            Header = new SmppHeader(CommandSet.UnknownPacket,CommandStatus.ESME_RSYSERR,0)
        //        };
        //        return submitSmResp;
        //    }
        //}
        /// <summary>
        /// Sends an undeliverable receipt back to the source client.
        /// </summary>
        private async Task SendUndeliverableReceiptAsync(SmppServerClient serverClient, SubmitSm submitSm)
        {
            await serverClient.DeliverAsync(SMS.ForDeliver().From(submitSm.DestinationAddress).To(submitSm.SourceAddress).Receipt(
                    new Receipt
                    {
                        MessageId = submitSm.Response.MessageId,
                        DoneDate = DateTime.Now,
                        State = MessageState.Undeliverable
                    }
                    ));
        }
        #endregion

        #region messages
        /// <summary>
        /// Handles the event when a full concatenated message is received.
        /// </summary>
        private /*async*/ void OnFullMessageReceived(object sender, MessageEventHandlerArgs args)
        {
            SubmitSm pdu = args.GetFirst<SubmitSm>();
            _logger.Information("Full message received From: {0}, To: {1}, Text: {2}", args.GetFirst<DeliverSm>().SourceAddress, args.GetFirst<DeliverSm>().DestinationAddress, args.Text);
        }

        /// <summary>
        /// Handles the event when a full message times out.
        /// </summary>
        private void OnFullMessageTimeout(object sender, MessageEventHandlerArgs args)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region IDisposable
        /// <summary>
        /// Disposes the resources used by the SmscServerProxyClient.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes managed and unmanaged resources.
        /// </summary>
        protected virtual async void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                if (_proxyClient != null)
                {
                    await _proxyClient.DisconnectAsync();
                    _proxyClient.Dispose();
                }

                if (_proxyServerClient != null)
                {
                    _proxyServerClient.Dispose();
                }

                _messageComposer.evFullMessageReceived -= OnFullMessageReceived;
                _messageComposer.evFullMessageTimeout -= OnFullMessageTimeout;

                _bridge.ReceiptReadyForForward -= WhenReceiptReadyForForward;
            }

            _disposed = true;
        }

        /// <summary>
        /// Destructor for the SmscServerProxyClient class.
        /// </summary>
        ~SmscServerProxyClient()
        {
            Dispose(false);
        }
        #endregion

        public bool IsConnected { get; private set; }

        public async Task ConnectAsync()
        {
            // Logic to connect to the SMSC server
            IsConnected = true;

            // Once connected, process any stored messages in RabbitMQ
            //var proxy = new SmscServerProxyNew(this, new SmscRabbitMQSendSmsService());
            //await proxy.ProcessStoredMessagesAsync();
        }

        public async Task<IBaseSmscResponse> SendMessageAsync(SendSmsSmscCommand message)
        {
            //AddressTON srcTon = Enum.IsDefined(typeof(AddressTON), message.srcTon) ? message.srcTon : AddressTON.Unknown;
            //AddressNPI srcNpi = Enum.IsDefined(typeof(AddressNPI), message.srcNpi) ? message.srcNpi : AddressNPI.Unknown;
            // Create source and destination addresses for the SMS
            var sourceAddress = new SmeAddress(message.srcAdr, message.srcTon, message.srcNpi);
            var destinationAddress = new SmeAddress(message.dstAdr, message.dstTon, message.dstNpi);

            // Construct the SubmitSmBuilder for the SMS message
            var builder = Inetlab.SMPP.SMS.ForSubmit()
                .From(sourceAddress)
                .To(destinationAddress)
                .Coding(message.dataCodings)
                .Text(message.smsText)
                .ExpireIn(TimeSpan.FromDays(2));

            if (message.deliveryReceipt != SMSCDeliveryReceipt.NotRequested)
            {
                builder.DeliveryReceipt(message.deliveryReceipt);
            }

            switch (message.submitMode)
            {
                case SubmitMode.Payload:
                    builder.MessageInPayload();
                    break;
                case SubmitMode.ShortMessageWithSAR:
                    builder.Concatenation(ConcatenationType.SAR);
                    break;
            }
            if (_clientConnectionSettings.optionalParameterList != null)
            {
                byte[] bytesarr = Encoding.UTF8.GetBytes(_clientConnectionSettings.optionalParameterList.value);
                // Remove the "0x" prefix
                if (_clientConnectionSettings.optionalParameterList.tag.StartsWith("0x"))
                {
                    _clientConnectionSettings.optionalParameterList.tag = _clientConnectionSettings.optionalParameterList.tag.Substring(2);
                }
                // Assuming _clientConnectionSettings.optionalParameterList.tag is a string
                if (ushort.TryParse(_clientConnectionSettings.optionalParameterList.tag, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort))
                {
                    TLV tlv = new TLV(tagAsUshort, bytesarr);
                    builder.AddParameter(tlv);
                }
            }
            SubmitSm[] submitSmArray = builder.Create(_proxyClient);
            var submitSm = submitSmArray.FirstOrDefault();
            if (submitSm == null)
            {
                // Handle the case where there is no SubmitSm to process
                _proxyServer.Logger.Warn("No SubmitSm was created.");
                return await SmscGeneralUtilities.MakeResponse(-1, "No SMS was created to be sent.");
            }

            if (_proxyClient.IsClientConnected)
            {
                var response = await _proxyClient.SubmitWithRepeatAsync(submitSmArray, TimeSpan.FromSeconds(1));
                if (response.All(x => x.Header.Status == CommandStatus.SMPPCLIENT_NOCONN || x.Header.Status == CommandStatus.SMPPCLIENT_UNBOUND))
                {
                    await _smscRabbitMQSendSmsService.ProcessInteraptedSubmitSmAsync(submitSm, _proxyServerClient);
                    return await SmscGeneralUtilities.MakeResponse(-1, "The SMS saved in rabbitmq and will be sent when the SMSC client is connected again.");
                }
                else if (response.All(x => x.Header.Status == CommandStatus.ESME_ROK))
                {
                    return await SmscGeneralUtilities.MakeResponse(1, "SMS sent successfully");
                }
                else
                {
                    string error = $"Submit failed. Status: {string.Join(",", response.Select(x => x.Header.Status.ToString()))}";
                    _logger.Error(_proxyClient.Name + ":" + error);
                    return await SmscGeneralUtilities.MakeResponse(-1, error);
                }
            }
            else
            {
                await _smscRabbitMQSendSmsService.ProcessInteraptedSubmitSmAsync(submitSm, _proxyServerClient);
                return await SmscGeneralUtilities.MakeResponse(-1, "The SMS saved in rabbitmq and will be sent when the SMSC client is connected again.");
            }


        }
    }
}
