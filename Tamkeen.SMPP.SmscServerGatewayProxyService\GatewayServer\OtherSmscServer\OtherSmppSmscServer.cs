﻿using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.Logging;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.WhatsApp;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.OtherSmscServer
{
    public class OtherSmppSmscServer : IDisposable
    {
        public SmppServer _otherSmscServer;
        private readonly MessageComposer _messageComposer;
        private readonly OtherSmscServerSettings _otherSmscServerSettings;
        private readonly ILogger _logger;
        private bool _disposed = false;
        private long _messageIdCounter;
        private readonly string _whatsAppUrl;
        private readonly string _resource;
        public OtherSmppSmscServer(IServiceProvider serviceProvider, OtherSmscServerSettings otherSmscServerSettings)
        {
            _messageComposer = new MessageComposer();
            _messageComposer.evFullMessageReceived += OnFullMessageReceived;
            _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;
            _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
            LogManager.SetLoggerFactory(new SmscSeriLoggerFactoryAdapter(_logger, LogLevel.Debug));
            _otherSmscServerSettings = otherSmscServerSettings;
            _otherSmscServer = new SmppServer(new IPEndPoint(IPAddress.Any, Convert.ToInt32(_otherSmscServerSettings.SmscServerPort)));
            _otherSmscServer.evClientConnected += server_evClientConnected;
            _otherSmscServer.evClientDisconnected += server_evClientDisconnected;
            _otherSmscServer.evClientBind += server_evClientBind;
            _otherSmscServer.evClientUnBind += server_evClientUnbind;
            _otherSmscServer.evClientSubmitSm += server_evClientSubmitSm;
            _otherSmscServer.evClientSubmitMulti += server_evClientSubmitMulti;
            _otherSmscServer.evClientEnquireLink += ServerOnClientEnquireLink;
            _otherSmscServer.Name = _otherSmscServerSettings.SmscServerName;
            _otherSmscServer.evClientCertificateValidation += OnClientCertificateValidation;
            _whatsAppUrl = otherSmscServerSettings.WhatsAppUrl;
            _resource = otherSmscServerSettings.Resource;

        }
        public async Task Run()
        {            
            await _otherSmscServer.StartAsync();
        }
        private void server_evClientConnected(object sender, SmppServerClient client)
        {
            //Change number of threads that process received messages. Default is 3
            //client.ReceiveTaskScheduler = new WorkersTaskScheduler(5);

            //Change receive buffer size for client socket
            // client.ReceiveBufferSize = 30 * 1024 * 1024;
            //Change send buffer size for client socket
            //  client.SendBufferSize = 30 * 1024 * 1024;


            //Don't allow this client to send more than one message per second
            //client.ReceiveSpeedLimit = 1;
            //Set maximum number of unhandled messages in the receive queue for this client
            //client.ReceiveQueueLimit = 2;


            client.EncodingMapper.MapEncoding(DataCodings.Class1, new Inetlab.SMPP.Encodings.GSMPackedEncoding());


            _logger.Information("Client {0} connected.", client.RemoteEndPoint);


            if (client.ClientCertificate != null)
            {
                _logger.Information("Client Certificate {0}, Expire Date: {1}", client.ClientCertificate.Subject, client.ClientCertificate.GetExpirationDateString());
            }
        }
        private void server_evClientDisconnected(object sender, SmppServerClient client)
        {

            _logger.Information("Client {0} disconnected.", client.RemoteEndPoint);

        }
        private void server_evClientBind(object sender, SmppServerClient client, Bind data)
        {
            _logger.Information("Client {0} bind as {1}:{2}", client.RemoteEndPoint, data.SystemId, data.Password);

            //  data.Response.ChangeSystemId("NewServerId");

            //Check SMPP access, and if it is wrong retund non-OK status.
            if (data.SystemId == "")
            {
                data.Response.Header.Status = CommandStatus.ESME_RINVSYSID;
                _logger.Information("Client {0} tries to bind with invalid SystemId: {1}", client.RemoteEndPoint, data.SystemId);
                return;
            }
            if (data.Password == "")
            {
                _logger.Information($"Client {client.RemoteEndPoint} tries to bind with invalid Password.");

                data.Response.Header.Status = CommandStatus.ESME_RINVPASWD;
                return;
            }
        }

        private void server_evClientUnbind(object sender, SmppServerClient client, UnBind data)
        {
            if (client.Status != ConnectionStatus.Bound)
            {
                data.Response.Header.Status = CommandStatus.ESME_RINVBNDSTS;
            }
        }
        private void server_evClientSubmitSm(object sender, SmppServerClient client, SubmitSm data)
        {
            long messageId = Interlocked.Increment(ref _messageIdCounter);
            // You can set your own MessageId
            data.Response.MessageId = messageId.ToString();

            _logger.Information("Client {0} sends message From:{1}, To:{2}, Text: {3}",
                client.RemoteEndPoint, data.SourceAddress, data.DestinationAddress,
                data.GetMessageText(client.EncodingMapper));


            _messageComposer.AddMessage(data);



            // Set unsuccess response status
            //data.Response.Status = CommandStatus.ESME_RSUBMITFAIL;


            if (data.SMSCReceipt != SMSCDeliveryReceipt.NotRequested)
            {
                //Send Delivery Receipt when required

                string messageText = data.GetMessageText(client.EncodingMapper);

                var dlrBuilder = SMS.ForDeliver()
                    .From(data.DestinationAddress)
                    .To(data.SourceAddress)
                    .Receipt(new Receipt
                    {
                        DoneDate = DateTime.Now,
                        State = MessageState.Delivered,
                        MessageId = data.Response.MessageId,
                        ErrorCode = "0",
                        SubmitDate = DateTime.Now,
                        Text = messageText.Substring(0, Math.Min(20, messageText.Length))
                    });

                if (data.DataCoding == DataCodings.UCS2)
                {
                    //short_message field cannot contain user data longer than 255 octets,
                    //therefore for UCS2 encoding we are sending DLR in message_payload parameter
                    dlrBuilder.MessageInPayload();
                }

                client.DeliverAsync(dlrBuilder).ConfigureAwait(false);
            }



        }
        void server_evClientSubmitMulti(object sender, SmppServerClient client, SubmitMulti data)
        {

            _logger.Information("Client {0} sends message From:{1} to multiple destinations:{2}, Text: {3}",
                                       client.RemoteEndPoint, data.SourceAddress, data.DestinationAddresses.Count,
                                       data.GetMessageText(client.EncodingMapper));

            _messageComposer.AddMessage(data);

            if (data.RegisteredDelivery == 1)
            {
                SmeAddress destinationAddress = data.DestinationAddresses[0] as SmeAddress;

                string messageText = data.GetMessageText(client.EncodingMapper);

                //Send Delivery Receipt when required
                Task.Run(() => client.DeliverAsync(
                    SMS.ForDeliver()
                        .From(data.SourceAddress)
                        .To(destinationAddress)
                        .Coding(data.DataCoding)
                        .Receipt(new Receipt
                        {
                            DoneDate = DateTime.Now,
                            State = MessageState.Delivered,
                            MessageId = data.Response.MessageId,
                            ErrorCode = "0",
                            SubmitDate = DateTime.Now,
                            Text = messageText.Substring(0, Math.Max(20, messageText.Length))
                        }
                        )
                ));
            }
        }
        private void ServerOnClientEnquireLink(object sender, SmppServerClient client, EnquireLink data)
        {
            _logger.Information($"EnquireLink received from {client}");
        }
        private void OnClientCertificateValidation(object sender, CertificateValidationEventArgs args)
        {
            //accept all certificates
            args.Accepted = true;
        }
        private void OnFullMessageReceived(object sender, MessageEventHandlerArgs args)
        {
            try
            {



                SubmitSm pdu = args.GetFirst<SubmitSm>();
                var Mob = pdu.DestinationAddress;
#if DEBUG
                //Mob= Mob == null ? null :
                Mob.Address = "737333203";
#endif
                var Msg = args.Text;
                var ShortCode = pdu.SourceAddress;
                //using (var connection = new System.Data.SqlClient.SqlConnection(cs))
                //{
                //    var affectedRows = connection.Execute(sql, new { MS = Msg.ToString(), MB = Mob.ToString(), SC = ShortCode.ToString() });
                //}

                _otherSmscServer.Logger.Info(string.Format("message Received: {0} {1} ", Msg, DateTime.Now.ToString() + Environment.NewLine));

                try
                {
                    RestFulClient client = new RestFulClient(_logger);
                    string cleaned = Msg.Replace("\n", "").Replace("\r", "");
                    Msg = cleaned;
                    //if (Msg.ToLower().Contains("pin") || Msg.ToLower().Contains("الرمز"))
                    {
                        //var lo = /*await*/ vRestFulClient.CallApiAsync<Object, SendWhatAppDto>(_whatsAppUrl, _resource,
                        ////var lo = await vRestFulClient.CallApiAsync<Object, SendWhatAppDto>("https://www.tamkeen.com.ye:5059", "WhatsApp/SendToWhatsApp",
                        //new SendWhatAppDto { To = Mob.ToString(), Message = string.Format("{0}", Msg) }, false, 30000, new List<KeyValueDto> {
                        //    //new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CRXhwdHJpYXRlIiwibmJmIjoxNjYyODEzMTM3LCJleHAiOjE4MjA1Nzk1MzcsImlhdCI6MTY2MjgxMzEzNywiaXNzIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIiwiYXVkIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIn0.1Iqu__xTyKPrhl_tQ-tIfkF4kAiUcLpVJ7Op7_GVClw" } });//780077268
                        //    new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CQnVzaW5lc3MiLCJuYmYiOjE2NjMxNjM5NjMsImV4cCI6MTk3ODc4MzE2MywiaWF0IjoxNjYzMTYzOTYzLCJpc3MiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2giLCJhdWQiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2gifQ.Du4t1tXuxnxBtf7GcvYkQWfgcDzMvbVcAW7EDZniqNU" } });
                        var lo = /*await*/ client.CallApiAsync<Object, SendToWhatsAppCommand>(_whatsAppUrl, _resource,
                        //var lo = await vRestFulClient.CallApiAsync<Object, SendWhatAppDto>("https://www.tamkeen.com.ye:5059", "WhatsApp/SendToWhatsApp",
                        new SendToWhatsAppCommand { To = Mob.ToString(), Message = string.Format("{0}", Msg) }, false, 30000
                        //,new List<KeyValueDto> {
                        //    //new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CRXhwdHJpYXRlIiwibmJmIjoxNjYyODEzMTM3LCJleHAiOjE4MjA1Nzk1MzcsImlhdCI6MTY2MjgxMzEzNywiaXNzIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIiwiYXVkIjoiaHR0cDovL3d3dy50YW1rZWVuLmNvbS55ZS9DYXNoIn0.1Iqu__xTyKPrhl_tQ-tIfkF4kAiUcLpVJ7Op7_GVClw" } });//780077268
                        //    new KeyValueDto {  Key= "Authorization", Value= "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiUE1CQnVzaW5lc3MiLCJuYmYiOjE2NjMxNjM5NjMsImV4cCI6MTk3ODc4MzE2MywiaWF0IjoxNjYzMTYzOTYzLCJpc3MiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2giLCJhdWQiOiJodHRwOi8vd3d3LnRhbWtlZW4uY29tLnllL0Nhc2gifQ.Du4t1tXuxnxBtf7GcvYkQWfgcDzMvbVcAW7EDZniqNU" } 
                        //}
                        );
                        ////if (lo != null)
                        ////{
                        ////    _smppServer.Logger.Info(string.Format("WhatsApp Response: {0}", @lo.ToString().Replace("{", "{{").Replace("}", "}}")));
                        ////}
                    }

                }
                catch (Exception ex)
                {
                    _otherSmscServer.Logger.Error(string.Format("Exception Received: {0} {1} ", ex.Message, DateTime.Now.ToString() + Environment.NewLine));
                };
            }
            catch (Exception)
            {
                _otherSmscServer.Logger.Error(string.Format("Exception Received: {0} ", DateTime.Now.ToString() + Environment.NewLine));
            }
            _otherSmscServer.Logger.Info(string.Format("SMS Received: {0}", args.Text));

        }
        private void OnFullMessageTimeout(object sender, MessageEventHandlerArgs args)
        {
            _logger.Information("Incomplete SMS Received: {0}", args.Text);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // Dispose managed resources
                _otherSmscServer?.Dispose();

                _messageComposer.evFullMessageReceived -= OnFullMessageReceived;
                _messageComposer.evFullMessageTimeout -= OnFullMessageTimeout;
            }

            // Dispose unmanaged resources (if any)

            _disposed = true;
            //_initialized = false;  // Allow reinitialization
        }
    }
}
