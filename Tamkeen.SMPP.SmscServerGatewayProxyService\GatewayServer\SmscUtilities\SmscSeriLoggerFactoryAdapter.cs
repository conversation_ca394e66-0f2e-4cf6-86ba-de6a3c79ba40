﻿using Serilog.Events;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Logging;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities
{
    /// <summary>
    /// Adapter class to integrate Serilog with the SMPP library's logging system.
    /// </summary>
    public class SmscSeriLoggerFactoryAdapter : ILogFactory
    {
        private readonly ILogger _logger;
        private readonly LogLevel _minLevel;

        /// <summary>
        /// Initializes a new instance of the <see cref="SmscSeriLoggerFactoryAdapter"/> class with a default log level.
        /// </summary>
        /// <param name="logger">Serilog logger instance.</param>
        public SmscSeriLoggerFactoryAdapter(ILogger logger) : this(logger, LogLevel.Info)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SmscSeriLoggerFactoryAdapter"/> class with a specified log level.
        /// </summary>
        /// <param name="logger">Serilog logger instance.</param>
        /// <param name="minLevel">Minimum log level to log.</param>
        public SmscSeriLoggerFactoryAdapter(ILogger logger, LogLevel minLevel)
        {
            _logger = logger;
            _minLevel = minLevel;
        }

        /// <summary>
        /// Gets the logger for a specified name.
        /// </summary>
        /// <param name="name">The name of the logger.</param>
        /// <returns>An instance of <see cref="ILog"/>.</returns>
        public ILog GetLogger(string name)
        {
            return new SmscSeriLoggerLoggerFactoryLogger(_logger, _minLevel);
        }
    }

    /// <summary>
    /// Implementation of ILog for use with Serilog.
    /// </summary>
    public class SmscSeriLoggerLoggerFactoryLogger : ILog
    {
        private readonly ILogger _logger;
        private readonly LogLevel _minLevel;

        /// <summary>
        /// Initializes a new instance of the <see cref="SmscSeriLoggerLoggerFactoryLogger"/> class.
        /// </summary>
        /// <param name="logger">Serilog logger instance.</param>
        /// <param name="minLevel">Minimum log level to log.</param>
        public SmscSeriLoggerLoggerFactoryLogger(ILogger logger, LogLevel minLevel)
        {
            _logger = logger;
            _minLevel = minLevel;
        }

        /// <summary>
        /// Maps SMPP log levels to Serilog log levels.
        /// </summary>
        /// <param name="level">The SMPP log level.</param>
        /// <returns>The corresponding Serilog log level.</returns>
        private LogEventLevel GetLevel(LogLevel level)
        {
            return level switch
            {
                LogLevel.Verbose => LogEventLevel.Verbose,
                LogLevel.Debug => LogEventLevel.Debug,
                LogLevel.Info => LogEventLevel.Information,
                LogLevel.Warning => LogEventLevel.Warning,
                LogLevel.Error => LogEventLevel.Error,
                LogLevel.Fatal => LogEventLevel.Fatal,
                _ => LogEventLevel.Information, // Default to Information level
            };
        }

        /// <summary>
        /// Determines whether the specified log level is enabled.
        /// </summary>
        /// <param name="level">The log level.</param>
        /// <returns>True if the log level is enabled; otherwise, false.</returns>
        public bool IsEnabled(LogLevel level)
        {
            return level >= _minLevel;
        }

        /// <summary>
        /// Writes a log entry.
        /// </summary>
        /// <param name="level">The log level.</param>
        /// <param name="message">The log message.</param>
        /// <param name="exception">The exception associated with the log message.</param>
        /// <param name="values">Additional values to log.</param>
        public void Write(LogLevel level, string message, Exception exception = null, params object[] values)
        {
            if (level < _minLevel) return;

            _logger.Write(GetLevel(level), exception, message, values);
        }
    }
}
