﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>75d4a3cc-ab43-472c-abb7-b21b87050ed9</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\FCM\Requests\AppSettings.cs" />
    <Compile Remove="FCMServiceManager.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="nuget.config" />
    <Content Remove="Properties\launchSettingsLive.json" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Properties\launchSettingsLive.json" />
  </ItemGroup>

  <ItemGroup>
    <_WebToolingArtifacts Remove="Properties\launchSettingsLive.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="nuget.config">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Tamkeen.SMPP.SmscServerGatewayProxyService\Tamkeen.SMPP.SmscServerGatewayProxyService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\FCM\Responses\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Kube.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
