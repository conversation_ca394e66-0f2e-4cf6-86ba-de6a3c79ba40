﻿using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.BackgroundServiceWrapper;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class SmscServiceManager
    {
        private readonly ISmscServerBackgroundService _smscServerBackgroundService;

        public SmscServiceManager(SmscServerBackgroundService smscServerBackgroundService)
        {
            _smscServerBackgroundService = smscServerBackgroundService;
        }

        public bool IsSmscServiceRunning()
        {

                return _smscServerBackgroundService.IsRunning();
        }

        public async Task StartServiceAsync(CancellationToken cancellationToken)
        {

                await _smscServerBackgroundService.StartAsync(cancellationToken);

        }

        public async Task StopServiceAsync(CancellationToken cancellationToken)
        {

                await _smscServerBackgroundService.StopAsync(cancellationToken);
        }

        public async Task RestartServiceAsync(CancellationToken cancellationToken)
        {

                await _smscServerBackgroundService.RestartServiceAsync(cancellationToken);
        }
    }
}
