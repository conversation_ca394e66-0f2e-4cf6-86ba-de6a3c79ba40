﻿using RestSharp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Threading.Tasks;
using System.Web;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.OtherSmscServer
{
    public class GetResponseDto
    {
        public string TransID { get; set; }
        public string LogRef { get; set; }
        public string responseContent { get; set; }
    }
    public class RestFulClient
    {
        public event EventHandler<GetResponseDto> evOnGetResponse;
        public event EventHandler<GetResponseDto> evOnGetTimeOut;
        private readonly ILogger _looger;
        public RestFulClient(ILogger logger)
        {
            _looger = logger;
        }
        public void FireEvOnGetResponse(GetResponseDto e)
        {
            evOnGetResponse?.Invoke(this, e);
        }
        public void FireEvOnGetTimeOut(GetResponseDto e)
        {
            evOnGetTimeOut?.Invoke(this, e);
        }
        public async Task<T> CallApiAsync<T, TRequest>(string _baseUrl, string _resource, TRequest _dto, bool _needLog = true, int _timeOut = 30000, List<KeyValueDto> _headerLst = null, List<KeyValueDto> _quiryParaLst = null) where TRequest : class
        {
            try
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });

                if (_baseUrl.EndsWith("/"))
                    _baseUrl = _baseUrl.Remove(_baseUrl.Length - 1, 1);

                if (_resource.EndsWith("/"))
                    _resource = _resource.Remove(_resource.Length - 1, 1);

                if (_resource.StartsWith("/"))
                    _resource = _resource.Substring(1);

                var reqBody = _dto != null ? Newtonsoft.Json.JsonConvert.SerializeObject(_dto).ToString() : "";

                var client = new RestClient($"{_baseUrl}/{_resource}");
                var request = new RestRequest
                {
                    Method = _dto != null ? Method.Post : Method.Get,
                    Timeout = TimeSpan.FromMilliseconds(_timeOut)
                };

                if (_dto != null)
                    request.AddParameter("application/json", reqBody, ParameterType.RequestBody);

                if (_headerLst != null && _headerLst.Any())
                {
                    foreach (var item in _headerLst)
                        request.AddHeader(item.Key, item.Value);
                }

                if (_quiryParaLst != null && _quiryParaLst.Any())
                {
                    foreach (var item in _quiryParaLst)
                        request.AddParameter(item.Key, item.Value, ParameterType.QueryString);
                }

                var response = await client.ExecuteAsync(request);

                if (response.StatusCode == HttpStatusCode.GatewayTimeout ||
                    response.StatusCode == HttpStatusCode.RequestTimeout ||
                    response.ResponseStatus == ResponseStatus.TimedOut ||
                    response.ResponseStatus == ResponseStatus.Aborted)
                {
                    FireEvOnGetTimeOut(new GetResponseDto { responseContent = "timeOUt" });
                    return default;
                }

                FireEvOnGetResponse(new GetResponseDto { responseContent = response.Content });
                return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(response.Content);
            }
            catch (TimeoutException ex)
            {
                FireEvOnGetTimeOut(new GetResponseDto { responseContent = ex.Message });
                throw;
            }
            catch (HttpRequestException ex) when (ex.InnerException is TaskCanceledException)
            {
                FireEvOnGetTimeOut(new GetResponseDto { responseContent = ex.Message });
                return default;
            }
            catch (Exception ex) when (ex.InnerException is TaskCanceledException)
            {
                FireEvOnGetTimeOut(new GetResponseDto { responseContent = ex.Message });
                return default;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public T CallApiAsync2<T, TRequest>(string _baseUrl, string _resource, TRequest _dto, bool _needLog = true, int _timeOut = 30000, List<KeyValueDto> _headerLst = null, List<KeyValueDto> _quiryParaLst = null) where TRequest : class
        {
            try
            {
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });

                if (_baseUrl.EndsWith("/"))
                {
                    _baseUrl = _baseUrl.Remove(_baseUrl.Length - 1, 1);
                }
                if (_resource.EndsWith("/"))
                {
                    _resource = _resource.Remove(_resource.Length - 1, 1);
                }
                if (_resource.StartsWith("/"))
                {
                    _resource = _resource.Substring(1);
                }

                var reqBody = _dto != null ? Newtonsoft.Json.JsonConvert.SerializeObject(_dto).ToString() : "";

                var client = new RestClient($"{_baseUrl}/{_resource}");
                var request = new RestRequest
                {
                    Method = _dto != null ? Method.Post : Method.Get,
                    Timeout = TimeSpan.FromMilliseconds(_timeOut) // Convert milliseconds to TimeSpan
                };

                if (_dto != null)
                {
                    request.AddParameter("application/json", reqBody, ParameterType.RequestBody);
                }

                if (_headerLst != null && _headerLst.Any())
                {
                    foreach (var item in _headerLst)
                    {
                        request.AddHeader(item.Key, item.Value);
                    }
                }

                if (_quiryParaLst != null && _quiryParaLst.Any())
                {
                    foreach (var item in _quiryParaLst)
                    {
                        request.AddParameter(item.Key, item.Value, ParameterType.QueryString);
                    }
                }

                var response = client.Execute(request);

                if (response.StatusCode == HttpStatusCode.GatewayTimeout ||
                    response.StatusCode == HttpStatusCode.RequestTimeout)
                {
                    try
                    {
                        FireEvOnGetTimeOut(new GetResponseDto { responseContent = "timeOUt" });
                    }
                    catch
                    {
                        // Log or handle the exception if needed 
                    }
                    return default;
                }

                try
                {
                    FireEvOnGetResponse(new GetResponseDto { responseContent = response.Content });
                }
                catch
                {
                    // Log or handle the exception if needed 
                }

                return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(response.Content);
            }
            catch (TimeoutException ex)
            {
                try
                {
                    FireEvOnGetTimeOut(new GetResponseDto { responseContent = ex.Message });
                }
                catch
                {
                    // Log or handle the exception if needed 
                }
                throw;
            }
            catch (HttpRequestException ex) when (ex.InnerException is TaskCanceledException)
            {
                try
                {
                    FireEvOnGetTimeOut(new GetResponseDto { responseContent = ex.Message });
                }
                catch
                {
                    // Log or handle the exception if needed 
                }
                return default;
            }
            catch (Exception ex) when (ex.InnerException is TaskCanceledException)
            {
                try
                {
                    FireEvOnGetTimeOut(new GetResponseDto { responseContent = ex.Message });
                }
                catch
                {
                    // Log or handle the exception if needed 
                }
                return default;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public class KeyValueDto
        {
            public string Key { get; set; }
            public string Value { get; set; }
        }
    }
}
