#!/bin/bash

# انتظار حتى يصبح Vault جاهز<|im_start|>
sleep 10

# تفعيل KV secrets engine
vault secrets enable -path=secret kv-v2

# تفعيل Transit secrets engine للتشفير
vault secrets enable transit

# إنشاء مفتاح التشفير
vault write -f transit/keys/tamkeen-encryption-key

# إنشاء policy للتطبيق
vault policy write tamkeen-smpp-policy - <<EOF
# KV secrets
path "secret/data/tamkeen-smpp/*" {
  capabilities = ["read"]
}

path "secret/data/smsc-credentials/*" {
  capabilities = ["read"]
}

path "secret/data/database-credentials/*" {
  capabilities = ["read"]
}

path "secret/data/external-services/*" {
  capabilities = ["read"]
}

# Transit encryption
path "transit/encrypt/tamkeen-encryption-key" {
  capabilities = ["update"]
}

path "transit/decrypt/tamkeen-encryption-key" {
  capabilities = ["update"]
}
EOF

# إنشاء token للتطبيق
vault token create -policy=tamkeen-smpp-policy -id=tamkeen-app-token

# إضافة الأسرار
vault kv put secret/tamkeen-smpp \
  LOG_FILE_PATH="/app/logs/tamkeen-smpp.log" \
  ELASTICSEARCH_URL="http://elasticsearch:9200" \
  ELASTICSEARCH_INDEX_FORMAT="tamkeen-smpp-{0:yyyy.MM.dd}"

vault kv put secret/smsc-credentials \
  SMSC_YGSM_CLIENT_URI="tcp://82.114.181.210:5020" \
  SMSC_YGSM_SYSTEM_ID="Tamkeen" \
  SMSC_YGSM_PASSWORD="Admin@1234567890" \
  SMSC_YGSM_MOBILE_PATTERN="^(96770|0096770|70|\\+96770)" \
  SMSC_YGSM_SERVER_PORT="28020" \
  SMSC_YOU_TRANSMITTER_CLIENT_URI="tcp://**************:8313" \
  SMSC_YOU_TRANSMITTER_SYSTEM_ID="9670110002313" \
  SMSC_YOU_TRANSMITTER_PASSWORD="Ym2021Encrypted" \
  SMSC_YOU_TRANSMITTER_MOBILE_PATTERN="^(96773|0096773|73|\\+96773)" \
  SMSC_YOU_TRANSMITTER_SERVER_PORT="28013"

vault kv put secret/database-credentials \
  DB_SERVER="sqlserver" \
  DB_NAME="TamkeenSMPP" \
  DB_USER="sa" \
  DB_PASSWORD="YourStrongPassword123"

vault kv put secret/external-services \
  REDIS_CONNECTION_STRING="redis:6379" \
  RABBITMQ_HOST="rabbitmq" \
  RABBITMQ_PORT="5672" \
  RABBITMQ_USER="admin" \
  RABBITMQ_PASSWORD="admin123" \
  FCM_WHATSAPP_AUTH_TOKEN="your-fcm-token" \
  FCM_WHATSAPP_SECRET_KEY="your-secret-key"

echo "Vault initialization completed!"