﻿using FluentValidation;
using System.Text.RegularExpressions;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Validators
{
    public class SendSmsSmscSampleRequestValidator : AbstractValidator<SendSmsSmscSampleRequest>
    {
        public SendSmsSmscSampleRequestValidator()
        {
            RuleFor(request => request.dstAdr)
                .NotEmpty().WithMessage("Destination address cannot be empty.")
                .Must(BeValidPhoneNumber).WithMessage("{PropertyName} must be a valid phone number")
                .Must((dstAdr) => IsValidDst(dstAdr))
                .WithMessage("Destination address must start with '967' followed by one of '73', '71', '70', '77', '78', and contain 7 additional digits.");
        }
        private bool BeValidPhoneNumber(string dstAdr)
        {
            // Check if srcAdr is a number and has a length of 11 characters
            return Regex.IsMatch(dstAdr, @"^\d{12}$");
        }
        private bool IsValidDst(string dstAdr)
        {
            // Check if dstAdr starts with '967' and followed by '73', '71', '70', '77', or '78'
            if (dstAdr.StartsWith("967"))
            {
                var validDstPrefixes = new List<string> { "73", "71", "70", "77", "78" };
                var dstPrefix = dstAdr.Substring(3, 2); // Get the two characters after '967'
                return validDstPrefixes.Contains(dstPrefix);
            }
            return false;
        }
    }
}
