﻿using RabbitMQ.Client;
using RabbitMQ.Client.Exceptions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata.Ecma335;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ
{
    /// <summary>
    /// Handles general RabbitMQ operations for the SMSC.
    /// </summary>
    public class SmscRabbitMQGeneral : IDisposable
    {
        private readonly ConnectionFactory _connectionFactory;
        private IConnection _connection;
        public IChannel _channel;
        private bool _disposed = false;
        private readonly object _lock = new object();
        private readonly ILogger _logger;
        /// <summary>
        /// Initializes a new instance of the <see cref="SmscRabbitMQGeneral"/> class with the specified settings.
        /// </summary>
        /// <param name="rabbitMQSettings">The RabbitMQ settings.</param>
        public SmscRabbitMQGeneral(RabbitMQSettings rabbitMQSettings, ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            if (rabbitMQSettings == null)
            {
                throw new ArgumentNullException(nameof(rabbitMQSettings));
            }

            _connectionFactory = new ConnectionFactory
            {
                HostName = rabbitMQSettings.hostName,
                UserName = rabbitMQSettings.userName,
                Password = rabbitMQSettings.password,
                Port = rabbitMQSettings.port
            };
            this.InitializeAsync().ConfigureAwait(false);
        }
        /// <summary>
        /// Asynchronously initializes the RabbitMQ connection and channel.
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _connection = await _connectionFactory.CreateConnectionAsync();
                _channel = await _connection.CreateChannelAsync();
                _logger.Information("RabbitMQ connection and channel initialized successfully.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to initialize RabbitMQ connection.");
                _connection = null;
                _channel = null;
            }
        }
        //public Task<bool> EnsureChannelIsOpen()
        //{
        //    if (_disposed)
        //    {
        //        throw new ObjectDisposedException(nameof(SmscRabbitMQGeneral), "Attempted to use a disposed RabbitMQ channel.");
        //    }

        //    if (_channel == null || !_channel.IsOpen)
        //    {
        //        lock (_lock)
        //        {
        //            if (_channel == null || !_channel.IsOpen)
        //            {
        //                _connection?.Dispose();
        //                _connection = _connectionFactory.CreateConnection();
        //                _channel = _connection.CreateModel();

        //                if (_channel != null)
        //                {
        //                    return Task.FromResult(true); // Return a completed task with the result true
        //                }
        //                else
        //                {
        //                    return Task.FromResult(false); // Return a completed task with the result false
        //                }
        //            }
        //        }
        //    }

        //    return Task.FromResult(true); // Return a completed task with the result true
        //}
        public async Task<bool> EnsureChannelIsOpen()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SmscRabbitMQGeneral), "Attempted to use a disposed RabbitMQ channel.");
            }

            int maxRetryAttempts = 5;
            int retryDelay = 2000;

            for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    if (_channel == null || !_channel.IsOpen)
                    {
                        // Removed `lock` statement as `await` cannot be used inside a lock.
                        _logger.Warning("RabbitMQ channel is closed, attempting to reconnect...");

                        if (_connectionFactory == null)
                        {
                            _logger.Error("Connection factory is null. Cannot create RabbitMQ connection.");
                            return false;
                        }

                        // Dispose of old connection if it exists
                        _connection?.Dispose();

                        _connection = await _connectionFactory.CreateConnectionAsync();
                        if (_connection == null)
                        {
                            _logger.Error("Failed to create RabbitMQ connection.");
                            return false;
                        }

                        _channel = await _connection.CreateChannelAsync();
                        if (_channel == null)
                        {
                            _logger.Error("Failed to create RabbitMQ channel.");
                            return false;
                        }

                        _logger.Information("RabbitMQ channel successfully opened on attempt {0}.", attempt);
                        return true;
                    }
                    else
                    {
                        return true;
                    }
                }
                catch (BrokerUnreachableException ex)
                {
                    _logger.Warning("RabbitMQ broker unreachable on attempt {0} of {1}. Exception: {2}", attempt, maxRetryAttempts, ex.Message);

                    if (attempt == maxRetryAttempts)
                    {
                        _logger.Error("Failed to connect to RabbitMQ after {0} attempts. Exception: {1}", maxRetryAttempts, ex.Message);
                        return false;
                    }
                    await Task.Delay(retryDelay);
                }
                catch (Exception ex)
                {
                    _logger.Error("An unexpected error occurred while trying to open RabbitMQ channel. Exception: {0}", ex.Message);
                    return false;
                }
            }

            return false;
        }

        /// <summary>
        /// Disposes the resources used by the RabbitMQ connection.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes managed resources.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources; otherwise, false.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                _channel?.Dispose();
                _connection?.Dispose();
            }

            _disposed = true;
        }

        /// <summary>
        /// Destructor (finalizer) for the <see cref="SmscRabbitMQGeneral"/> class.
        /// </summary>
        ~SmscRabbitMQGeneral()
        {
            Dispose(false);
        }
    }
}
