using System.ComponentModel.DataAnnotations;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM
{
    public class FcmWhatsappSettings
    {
        //public string CS { get; set; }
        [Required]
        public string ApplicationName { get; set; }

        [Required]
        public string authToken { get; set; }

        [Required]
        public string LogFilePath { get; set; }

        [Range(0, int.MaxValue)]
        public int envType { get; set; }

        // public string EXCS { get; set; }
        [Required]
        public string RedisHost { get; set; }

        [Range(1, int.MaxValue)]
        public int RedisPort { get; set; }

        [Required]
        public string RedisEndPoint { get; set; }

        // public string LogFilePath { get; set; }
        [Range(0, double.MaxValue)]
        public double TokenSessionTimeExpiration { get; set; }

        [Required]
        public string CommonTimeExpiration { get; set; }

        [Required]
        public string SeqUrl { get; set; }

        [Required]
        public string DalUrl { get; set; }

        public string Sec { get; }

        [Required]
        public string PhoneNumberID { get; set; }

        [Required]
        public string PhoneNumberIDExp { get; set; }

        [Required]
        public string Version { get; set; }

        [Required]
        public string SecretKey { get; set; }

        [Required]
        public string ExpServerKey { get; set; }

        [Range(0, int.MaxValue)]
        public int ServerKey { get; set; }

        [Range(0, int.MaxValue)]
        public int TimeTransExpired { get; set; }

        [Required]
        public string Mode { get; set; }
    }
}

