using System;
using System.Threading;
using System.Threading.RateLimiting;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RateLimiting
{
    public class LeakyBucketRateLimiter : IRateLimiter
    {
        private readonly PartitionedRateLimiter<string> _limiter;

        public LeakyBucketRateLimiter()
        {
            _limiter = PartitionedRateLimiter.Create<string, string>(key =>
                RateLimitPartition.GetTokenBucketLimiter(key, _ => new TokenBucketRateLimiterOptions
                {
                    TokenLimit = 100,
                    TokensPerPeriod = 100,
                    ReplenishmentPeriod = TimeSpan.FromSeconds(1),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    AutoReplenishment = true
                }));
        }

        public async Task<bool> PermitAsync(string partitionKey, CancellationToken cancellationToken = default)
        {
            var lease = await _limiter.AcquireAsync(partitionKey, 1, cancellationToken);
            return lease.IsAcquired;
        }

        public void RecordResult(string partitionKey, bool success)
        {
            // no-op for now
        }
    }
}
