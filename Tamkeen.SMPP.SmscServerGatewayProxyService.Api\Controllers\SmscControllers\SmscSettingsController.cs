﻿using Microsoft.AspNetCore.Mvc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SmscControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SmscSettingsController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public SmscSettingsController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [HttpGet]
        [Route("get")]
        public IActionResult GetSmscSettings()
        {
            var smscSettingsSection = _configuration.GetSection("SmscSettings");
            var smscSettings = new Dictionary<string, object>();

            foreach (var section in smscSettingsSection.GetChildren())
            {
                var settings = section.Get<SmscServerGatewayProxySettings>();
                smscSettings[section.Key] = settings ?? null;
            }

            return Ok(smscSettings);
        }

        [HttpGet]
        [Route("get/{key}")]
        public IActionResult GetSpecificSmscSetting(string key)
        {
            var section = _configuration.GetSection($"SmscSettings:{key}");
            if (!section.Exists())
            {
                return NotFound($"No settings found for the key '{key}'.");
            }

            var settings = section.Get<SmscServerGatewayProxySettings>();
            if (settings == null)
            {
                return NotFound($"Settings for the key '{key}' could not be deserialized.");
            }

            return Ok(settings);
        }
        /// <summary>
        /// تُرجع الإعدادات الحية الجارية فعليًا من الذاكرة (المستخدمة فعليًا)
        /// </summary>
        [HttpGet]
        [Route("live")]
        public IActionResult GetLiveRunningSettings([FromServices] SmscProxyServiceManager manager)
        {
            var liveSettings = manager.GetAllRunningSettings();
            return Ok(liveSettings);
        }
    }
}
