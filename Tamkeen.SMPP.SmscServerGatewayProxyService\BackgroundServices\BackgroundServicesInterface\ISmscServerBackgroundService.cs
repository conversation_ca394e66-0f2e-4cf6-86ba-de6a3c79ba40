﻿
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public interface ISmscServerBackgroundService
    {
        //string TransceiverSystemId { get; }
        bool IsRunning();
        Task RestartServiceAsync(CancellationToken cancellationToken);
        Task StopAsync(CancellationToken cancellationToken);
        //string GetTransceiverSystemId();
        Task StartAsync(CancellationToken cancellationToken);
        //Task<bool> IsSmscProxyWorking();
        //Task<IBaseSmscResponse> SendSmsAsync(SendSmsSmscCommand sendSmsSmscCommand, CancellationToken cancellationToken);
    }
}