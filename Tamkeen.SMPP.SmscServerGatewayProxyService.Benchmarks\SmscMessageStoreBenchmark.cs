using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using BenchmarkDotNet.Attributes;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Benchmarks
{
    [MemoryDiagnoser]
    public class SmscMessageStoreBenchmark
    {
        [Params(100, 1000)]
        public int MessageCount;

        private List<Message> _messages = new List<Message>();

        [GlobalSetup]
        public void Setup()
        {
            _messages = Enumerable.Range(0, MessageCount)
                .Select(i => new Message(Guid.NewGuid().ToString()))
                .ToList();
        }

        [Benchmark]
        public void LockedStore_AddAndUpdate()
        {
            var store = new LockedConcurrentMessageStore();
            foreach (var msg in _messages)
            {
                store.Add(msg);
            }
            foreach (var msg in _messages)
            {
                store.Update(msg.Id, m => m.DeliveryState = "DELIVERED");
            }
        }

        [Benchmark]
        public void SimpleStore_AddAndUpdate()
        {
            var store = new SimpleConcurrentMessageStore();
            foreach (var msg in _messages)
            {
                store.Add(msg);
            }
            foreach (var msg in _messages)
            {
                store.Update(msg.Id, m => m.DeliveryState = "DELIVERED");
            }
        }
    }

    internal class Message
    {
        public string Id { get; }
        public string DeliveryState { get; set; } = string.Empty;

        public Message(string id)
        {
            Id = id;
        }
    }

    internal class LockedConcurrentMessageStore
    {
        private readonly ConcurrentDictionary<string, Message> _store = new();
        private readonly object _lock = new();

        public void Add(Message message)
        {
            lock (_lock)
            {
                if (!_store.TryAdd(message.Id, message))
                {
                    throw new InvalidOperationException($"Message with the same Id '{message.Id}' already exists.");
                }
            }
        }

        public Message Update(string messageId, Action<Message> update)
        {
            lock (_lock)
            {
                return _store.AddOrUpdate(messageId,
                    _ => throw new InvalidOperationException($"Message with id {messageId} not found."),
                    (s, message) =>
                    {
                        update(message);
                        return message;
                    });
            }
        }
    }

    internal class SimpleConcurrentMessageStore
    {
        private readonly ConcurrentDictionary<string, Message> _store = new();

        public void Add(Message message)
        {
            if (!_store.TryAdd(message.Id, message))
            {
                throw new InvalidOperationException($"Message with the same Id '{message.Id}' already exists.");
            }
        }

        public Message Update(string messageId, Action<Message> update)
        {
            return _store.AddOrUpdate(messageId,
                _ => throw new InvalidOperationException($"Message with id {messageId} not found."),
                (s, message) =>
                {
                    update(message);
                    return message;
                });
        }
    }
}
