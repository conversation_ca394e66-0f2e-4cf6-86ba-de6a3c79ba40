using Serilog;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ;

public class RabbitMQIntegrationTests
{
    [Fact]
    public async Task Initialize_WithFakeService_ReturnsFalse()
    {
        var settings = new RabbitMQSettings
        {
            hostName = "fake-host",
            userName = "user",
            password = "pass",
            port = 5672
        };
        ILogger logger = new LoggerConfiguration().CreateLogger();
        var general = new SmscRabbitMQGeneral(settings, logger);

        await general.StartAsync();
        var result = await general.EnsureChannelIsOpen();

        Assert.False(result);
    }
}
