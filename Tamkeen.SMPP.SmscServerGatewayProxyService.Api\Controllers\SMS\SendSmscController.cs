﻿using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS
{
    [Route("api/[controller]")]
    [ApiController]
    public class SendSmscController : ControllerBase
    {
        private readonly SmscProxyServiceManager _serviceManager;
        private readonly WhatsAppController _whatsappService;

        public SendSmscController(SmscProxyServiceManager serviceManager, WhatsAppController whatsAppController)
        {
            _serviceManager = serviceManager;
            _whatsappService = whatsAppController; // Correct assignment
        }
        //private readonly IEnumerable<SmscServerProxyClient> _smscClients;
        //private readonly SmscServerProxy _smscServerProxy;
        //private readonly IEnumerable<SmscRabbitMQSendSmsServiceVersion2> _smscRabbitMQSendSmsServiceVersion2s;
        //private readonly SmscRabbitMQSendSmsService _rabbitMQService;
        //private readonly Dictionary<string, ConnectionMode> modeMap
        //    = new Dictionary<string, ConnectionMode>
        //    {
        //        { "transceiver", ConnectionMode.Transceiver },
        //        { "receiver", ConnectionMode.Receiver },
        //        { "transmitter", ConnectionMode.Transmitter }
        //    };
        //public SmscController(/*SmscRabbitMQSendSmsService rabbitMQService*//*IEnumerable<SmscRabbitMQSendSmsServiceVersion2> smscRabbitMQSendSmsServiceVersion2s*//*, SmscServerProxy smscServerProxy*//*,*/ IServiceProvider serviceProvider)
        //{
        //    //_smscClients = smscClients;
        //    //_smscServerProxy=smscServerProxy;
        //    _smscServerProxy = serviceProvider.GetService<SmscServerProxy>();
        //    //_rabbitMQService=rabbitMQService;
        //    //_smscRabbitMQSendSmsServiceVersion2s = smscRabbitMQSendSmsServiceVersion2s;
        //}
        //[AllowAnonymous]
        //[HttpGet("GetClients")]
        //[SwaggerOperation("Get available clients")]
        //public async Task<IActionResult> GetClients()
        //{
        //    AvailableClientsResponse response = new AvailableClientsResponse()
        //    {
        //        availableClient= new List<string>()
        //    };
        //    foreach (var smscClient in _smscServerProxy._gatewayClients)
        //    {
        //        if (smscClient.Value._clientConnectionSettings.enable && !smscClient.Value._client.IsClientConnected)
        //        {
        //            //ConnectionMode mode = ConnectionMode.None;

        //            if (smscClient.Value._clientConnectionSettings.mode != null && modeMap.ContainsKey(smscClient.Value._clientConnectionSettings.mode))
        //            {
        //                string lowercasedMode = smscClient.Value._clientConnectionSettings.mode.ToLowerInvariant(); // Convert mode to lowercase for case-insensitive comparison
        //                smscClient.Value._clientConnectionSettings.connectionMode = modeMap[lowercasedMode];
        //            }
        //            await smscClient.Value.RunAsync(
        //            new Uri(smscClient.Value._clientConnectionSettings.clientUri),
        //            smscClient.Value._clientConnectionSettings.connectionMode,
        //            smscClient.Value._clientConnectionSettings.systemId,
        //            smscClient.Value._clientConnectionSettings.password,
        //            smscClient.Value._clientConnectionSettings.connectionRecovery);
        //        }
        //    }
        //    foreach (var smscClient in _smscServerProxy._gatewayClients)
        //    {
        //        if (smscClient.Value._client.IsClientConnected)
        //        {
        //            response.availableClient.Add(smscClient.Value._clientConnectionSettings.transceiverSystemId);
        //        }
        //    }
        //    if (response.availableClient.Count>0)
        //    {
        //        response.resultCode=1;
        //        response.resultMessage=$"The available clients that you can send to are {response.availableClient.Count.ToString()}, you can send through any of them using the name of the client";
        //    }
        //    //// Filter or select specific Smsc instances based on client name
        //    //var selectedClients = _smscClients.Where(client => client._clientConnectionSettings.transceiverSystemId == "YouTrans").ToList();

        //    //// Now you can work with selectedClients

        //    //return Ok(selectedClients);
        //    return Ok(response);
        //}
        //[AllowAnonymous]
        //[HttpGet("GetClientsNewVersion")]
        //[SwaggerOperation("Get available clients ")]
        //public async Task<IActionResult> GetClientsNewVersion()
        //{
        //    var result = await Mediator.Send(new GetAvailableSmscClientsQueryRequest());
        //    return new ApiResult<GetAvailableSmscClientsResponse>(result);
        //}

        //[AllowAnonymous]
        //[HttpGet("GetServer")]
        //[SwaggerOperation("Run Server")]
        //public async Task<IActionResult> GetServer()
        //{
        //    if (_smscServerProxy != null)
        //    {
        //        var result = await Mediator.Send(new GetSmscServerStatusQueryRequest());
        //        return new ApiResult<GetSmscServerStatusResponse>(result);
        //    }
        //    else
        //    {
        //        return BadRequest(MakeResponse(-1, "The SMPP server is not enable from the setting of the project"));
        //    }
        //}
        //[AllowAnonymous]
        //[HttpPost("SendSmsAsync")]
        //[SwaggerOperation("Send SMS")]
        //public async Task<IActionResult> SendSmsAsync([FromBody] SendSmsSmscRequest request)
        //{
        //    if (_smscServerProxy._gatewayClients.ContainsKey(request.transceiverSystemId))
        //    {
        //        if (_smscServerProxy._gatewayClients[request.transceiverSystemId]._client.Status != ConnectionStatus.Bound)
        //        {
        //            _smscServerProxy._smppServer.Logger.Error("Before sending messages, please connect to SMPP server.");
        //            return BadRequest(MakeResponse(-1, $"The {request.transceiverSystemId} SMPP server that you reqested is not connected, please check the get connected servers"));
        //        }
        //        else
        //        {
        //            DataCodings coding = (DataCodings)Enum.Parse(typeof(DataCodings), request.DataCodings);


        //            var sourceAddress = new SmeAddress(request.srcAdr, (AddressTON)byte.Parse(request.srcTon), (AddressNPI)byte.Parse(request.srcNpi));

        //            var destinationAddress = new SmeAddress(request.dstAdr, (AddressTON)byte.Parse(request.dstTon), (AddressNPI)byte.Parse(request.dstNpi));

        //            _smscServerProxy._smppServer.Logger.Info("Submit message To: {0}. Text: {1}", request.dstAdr, request.smsText);


        //            //ISubmitSmBuilder builder = Inetlab.SMPP.SMS.ForSubmit()
        //            //    .From(sourceAddress)
        //            //    .To(destinationAddress)
        //            //    .Coding(coding)
        //            //    .Text(request.smsText)
        //            //    .Concatenation(ConcatenationType.UDH8bit)
        //            //    //Or you can set data 
        //            //    //.Data(HexStringToByteArray("024A3A6949A59194813D988151A004004D215D2690568698A22820C49A4106288A126A8A22C2A820C22820C2A82342AC30820C4984106288A12628A22C2A420800"))

        //            //    //Apply italic style for all text  (mobile device should support basic EMS)
        //            //    //.Set(delegate(SubmitSm sm)
        //            //    //         {
        //            //    //             sm.UserDataPdu.Headers.Add(
        //            //    //                 InformationElementIdentifiers.TextFormatting, new byte[] {0, 1, ToByte("00100011")});
        //            //    //         })

        //            //    // Set ValidityPeriod expired in 2 days
        //            //    .ExpireIn(TimeSpan.FromDays(2))

        //            //    //Request delivery receipt
        //            //    .DeliveryReceipt();
        //            ////Add custom TLV parameter
        //            ////.AddParameter(0x1403, "free")

        //            ////Change SubmitSm sequence to your own number.
        //            ////.Set(delegate(SubmitSm sm) { sm.Sequence = _client.SequenceGenerator.NextSequenceNumber();})

        //            ISubmitSmBuilder builder = Inetlab.SMPP.SMS.ForSubmit()
        //            .From(sourceAddress)
        //            .To(destinationAddress)
        //            .Coding(coding)
        //            .Text(request.smsText)
        //            //Or you can set data 
        //            //.Data(HexStringToByteArray("024A3A6949A59194813D988151A004004D215D2690568698A22820C49A4106288A126A8A22C2A820C22820C2A82342AC30820C4984106288A12628A22C2A420800"))

        //            //Apply italic style for all text  (mobile device should support basic EMS)
        //            //.Set(delegate(SubmitSm sm)
        //            //         {
        //            //             sm.UserDataPdu.Headers.Add(
        //            //                 InformationElementIdentifiers.TextFormatting, new byte[] {0, 1, ToByte("00100011")});
        //            //         })

        //            // Set ValidityPeriod expired in 2 days
        //            .ExpireIn(TimeSpan.FromDays(2))

        //            //Request delivery receipt
        //            .DeliveryReceipt();
        //            //builder.Concatenation(ConcatenationType.SAR);


        //            //var factory = new ConnectionFactory()
        //            //{
        //            //    HostName = "***************",
        //            //    UserName = "admin",
        //            //    Password = "admin"
        //            //};

        //            //using (var connection = factory.CreateConnection())
        //            //using (var channel = connection.CreateModel())
        //            //{
        //            //    // تحديد اسم الطابور (Queue) المستهدف
        //            //    string queueName = "messages_queue";

        //            //    // إعلان الطابور (Queue) إذا لم يكن موجودًا بالفعل
        //            //    channel.QueueDeclare(queue: queueName,
        //            //                         durable: false,
        //            //                         exclusive: false,
        //            //                         autoDelete: false,
        //            //                         arguments: null);


        //            //    // تحويل الرسالة إلى سلسلة نصية (JSON)
        //            string message = JsonConvert.SerializeObject(request);

        //            //    // تحويل الرسالة إلى مصفوفة من البايتات
        //            //var body = System.Text.Encoding.UTF8.GetBytes(message);
        //            //_rabbitMQService._channel.BasicPublish(exchange: "",
        //            //    routingKey: _rabbitMQService._queueName,
        //            //    basicProperties: null,
        //            //    body: body);
        //            //    // إرسال الرسالة إلى الطابور (Queue)
        //            //    channel.BasicPublish(exchange: "",
        //            //                         routingKey: queueName,
        //            //                         basicProperties: null,
        //            //                         body: body);

        //            //    Console.WriteLine("تم إرسال الرسالة: {0}", message);
        //            //}
        //            try
        //            {
        //                IList<SubmitSmResp> resp = await _smscServerProxy._gatewayClients[request.transceiverSystemId]._client.SubmitAsync(builder);

        //                if (resp.All(x => x.Header.Status == CommandStatus.ESME_ROK))
        //                {
        //                    _smscServerProxy._smppServer.Logger.Info("Submit succeeded. MessageIds: {0}", string.Join(",", resp.Select(x => x.MessageId)));
        //                    return Ok(MakeResponse(1, $"Submit succeeded. MessageIds: {string.Join(",", resp.Select(x => x.MessageId))}"));
        //                }
        //                else
        //                {
        //                    _smscServerProxy._smppServer.Logger.Info("Submit failed. Status: {0}", string.Join(",", resp.Select(x => x.Header.Status.ToString())));
        //                    return Ok(MakeResponse(1, $"Submit failed. Status: {string.Join(",", resp.Select(x => x.Header.Status.ToString()))}"));

        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                _smscServerProxy._smppServer.Logger.Info("Submit failed. Error: {0}", ex.Message);
        //                return BadRequest(MakeResponse(-1, $"Submit failed. Error: {ex.Message}"));
        //            }

        //            // When you received success result, you can later query message status on SMSC 
        //            // if (resp.Count > 0 && resp[0].Status == CommandStatus.ESME_ROK)
        //            // {
        //            //     _log.Info("QuerySm for message " + resp[0].MessageId);
        //            //     QuerySmResp qresp = _client.Query(resp[0].MessageId,
        //            //         srcTon, srcNpi,srcAdr);
        //            // }
        //        }
        //    }
        //    else if (!_smscServerProxy._gatewayClients.ContainsKey(request.transceiverSystemId))
        //    {
        //        return BadRequest(MakeResponse(-1, $"The {request.transceiverSystemId} SMPP server that you reqested is not exist, please use the get connected server to get the list o connected servers"));
        //    }

        //    return BadRequest(MakeResponse(-1, $"Submit failed. Error: {request.smsText}"));
        //    //var command = request.Adapt<AddProductCommand>();

        //    //var result = await Mediator.Send(command);

        //    //return new ApiResult<int>(result);
        //}

        [AllowAnonymous]
        [HttpPost("SendSmsNewVersionAsync")]
        [SwaggerOperation("Send SMS")]
        public async Task<IActionResult> SendSmsNewVersionAsync([FromBody] SendSmsSmscRequest request, CancellationToken cancellationToken)
        {
            try
            {
                //// Check if SMPP client exists
                //if (!_smscServerProxy._gatewayClients.ContainsKey(request.transceiverSystemId))
                //{
                //    return BadRequest(MakeResponse(-1, $"The {request.transceiverSystemId} SMPP server that you requested does not exist."));
                //}
                // Check if SMPP client is connected
                //var client = _smscServerProxy._gatewayClients[request.transceiverSystemId];
                //if (client._proxyClient.Status != ConnectionStatus.Bound)
                //{
                //    var smscRabbitMQSaveSMSCommand = request.Adapt<SmscRabbitMQSaveSMSCommand>();
                //    //smscRabbitMQSaveSMSCommand.smscServerProxyClient = client;
                //    var response = await Mediator.Send(smscRabbitMQSaveSMSCommand);
                //    return Ok(MakeResponse(response.resultCode, response.resultMessage));
                //}
                //else
                {

                    var command = request.Adapt<SendSmsSmscCommand>();
                    //command.smscServerProxyClient = client;
                    var response = await _serviceManager.SendSmsAsync(request.transceiverSystemId, command, cancellationToken);
                    //var response = await Mediator.Send(command);
                    return Ok(MakeResponse(response.resultCode, response.resultMessage));
                }
                //return Ok(response);

            }
            catch (Exception ex)
            {
                return BadRequest(MakeResponse(-1, $"Submit failed. Error: {ex.Message}"));
            }
        }

        [AllowAnonymous]
        [HttpPost("SendSmsSampleVersionAsync")]
        [SwaggerOperation("Send SMS")]
        public async Task<IActionResult> SendSmsSampleVersionAsync([FromBody] SendSmsSmscSampleRequest request, CancellationToken cancellationToken)
        {
            try
            {

                SendSmsSmscRequest sendSmsSmscRequest = new SendSmsSmscRequest();

                if (request.smsDstType == SmsDstType.local)
                {
                    string dstAdr = "";
                    if (request.dstAdr.Length == 12)
                    {
                        dstAdr = request.dstAdr.Substring(3, 2);
                    }
                    else
                    {
                        dstAdr = request.dstAdr.Substring(0, 2);
                    }
                    switch (dstAdr)
                    {
                        case "77":
                        case "78":
                            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.YemenMobile);
                            sendSmsSmscRequest.srcTon = Inetlab.SMPP.Common.AddressTON.Alphanumeric;
                            sendSmsSmscRequest.srcNpi = Inetlab.SMPP.Common.AddressNPI.Unknown;
                            break;
                        case "71":
                            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.SabaFonTrans);
                            sendSmsSmscRequest.srcTon = Inetlab.SMPP.Common.AddressTON.Alphanumeric;
                            sendSmsSmscRequest.srcNpi = Inetlab.SMPP.Common.AddressNPI.Unknown;
                            break;
                        case "73":
                            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.YouTrans);
                            sendSmsSmscRequest.srcTon = Inetlab.SMPP.Common.AddressTON.Alphanumeric;
                            sendSmsSmscRequest.srcNpi = Inetlab.SMPP.Common.AddressNPI.Unknown;
                            break;
                        case "70":
                            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.YGsm);
                            sendSmsSmscRequest.srcTon = Inetlab.SMPP.Common.AddressTON.Alphanumeric;
                            sendSmsSmscRequest.srcNpi = Inetlab.SMPP.Common.AddressNPI.Unknown;
                            break;
                        default:
                            sendSmsSmscRequest.transceiverSystemId = nameof(TransceiverSystemIds.Other);
                            break;
                    }
                    sendSmsSmscRequest.dstAdr = request.dstAdr;
                    sendSmsSmscRequest.smsText = request.smsText;
                    sendSmsSmscRequest.deliveryReceipt = Inetlab.SMPP.Common.SMSCDeliveryReceipt.SuccessOrFailure;

                    var command = sendSmsSmscRequest.Adapt<SendSmsSmscCommand>();
                    //command.smscServerProxyClient = client;
                    if (sendSmsSmscRequest.transceiverSystemId != nameof(TransceiverSystemIds.Other))
                    {
                        var response = _serviceManager.SendSmsAsync(sendSmsSmscRequest.transceiverSystemId, command, cancellationToken);
                        return Ok(MakeResponse(response.Result.resultCode, response.Result.resultMessage));
                    }
                    else
                    {
                        return BadRequest(MakeResponse(-1, "There is an error on the number that you want to send the sms to"));
                    }
                }
                //var response = await Mediator.Send(command);

                else
                {
                    // Handle messages with TransceiverSystemIds.Other by calling SendToWhatsApp
                    var sendToWhatsAppRequest = new SendToWhatsAppRequest
                    {
                        To = request.dstAdr,
                        Message = request.smsText
                    };

                    // Assuming you have a service or way to invoke the SendToWhatsApp method
                    var whatsappResponse = await _whatsappService.SendToWhatsApp(sendToWhatsAppRequest);
                    return Ok(whatsappResponse);
                }

            }
            catch (Exception ex)
            {
                return BadRequest(MakeResponse(-1, $"Submit failed. Error: {ex.Message}"));
            }
        }

        [AllowAnonymous]
        [HttpPost("SendSmsSampleVersionAsyncV2")]
        [SwaggerOperation("Send SMS")]
        public async Task<IActionResult> SendSmsSampleVersionAsyncV2(
        [FromBody] SendSmsSmscSampleRequest request,
        CancellationToken cancellationToken,
        [FromServices] ISmscRoutingService routing)          // <‑ inject
        {
            try
            {
                // ------------------------‑‑‑ common validation  ‑‑‑--------------------
                if (request.smsDstType != SmsDstType.local)
                {
                    // Handle messages with TransceiverSystemIds.Other by calling SendToWhatsApp
                    var sendToWhatsAppRequest = new SendToWhatsAppRequest
                    {
                        To = request.dstAdr,
                        Message = request.smsText
                    };

                    // Assuming you have a service or way to invoke the SendToWhatsApp method
                    var whatsappResponse = await _whatsappService.SendToWhatsApp(sendToWhatsAppRequest);
                    return Ok(whatsappResponse);
                }

                // ----------------------‑‑‑  build request  ‑‑‑------------------------
                var sendReq = new SendSmsSmscRequest
                {
                    dstAdr = request.dstAdr,
                    smsText = request.smsText,
                    deliveryReceipt = SMSCDeliveryReceipt.SuccessOrFailure
                };

                // -------------------------------------------------- routing ----------
                if (!routing.TryResolve(request.dstAdr, out var info))
                {
                    return BadRequest(MakeResponse(-1,
                        "There is an error in the destination number"));
                }

                sendReq.transceiverSystemId = info.TransceiverSystemId;
                sendReq.srcTon = info.SrcTon;
                sendReq.srcNpi = info.SrcNpi;

                // ------------------------------------------------- send --------------
                var command = sendReq.Adapt<SendSmsSmscCommand>();
                var response = await _serviceManager
                        .SendSmsAsync(sendReq.transceiverSystemId, command, cancellationToken);

                return Ok(MakeResponse(response.resultCode, response.resultMessage));
            }
            catch (Exception ex)
            {
                return BadRequest(MakeResponse(-1, $"Submit failed. Error: {ex.Message}"));
            }
        }


        private async Task<IBaseSmscResponse> MakeResponse(int resultCode, string resultMessage)
        {
            return new BaseSmscResponse()
            {
                resultCode = resultCode,
                resultMessage = resultMessage
            };
        }
    }
}
