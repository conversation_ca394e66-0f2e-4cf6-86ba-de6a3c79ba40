﻿using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;
using Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS
{
    [Route("api/[controller]")]
    [ApiController]
    public class SendSmscController : ControllerBase
    {
        private readonly SmscProxyServiceManager _serviceManager;

        public SendSmscController(SmscProxyServiceManager serviceManager)
        {
            _serviceManager = serviceManager;
        }

        [AllowAnonymous]
        [HttpPost("send")]
        [SwaggerOperation("SendSms")]
        public async Task<IActionResult> SendAsync([FromBody] SendSmsSmscRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var command = request.Adapt<SendSmsSmscCommand>();
                var response = await _serviceManager.SendSmsAsync(request.transceiverSystemId, command, cancellationToken);
                return Ok(MakeResponse(response.resultCode, response.resultMessage));

            }
            catch (Exception ex)
            {
                return BadRequest(MakeResponse(-1, $"Submit failed. Error: {ex.Message}"));
            }
        }



        private async Task<IBaseSmscResponse> MakeResponse(int resultCode, string resultMessage)
        {
            return new BaseSmscResponse()
            {
                resultCode = resultCode,
                resultMessage = resultMessage
            };
        }
    }
}
