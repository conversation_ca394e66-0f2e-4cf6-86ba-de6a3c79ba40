﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc
{
    public class SendSmsSmscCommandHandler
    {
        private readonly IServiceProvider _serviceProvider;
        public SendSmsSmscCommandHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            //_smscServerProxy = serviceProvider.GetService<SmscServerProxy>();
            //if (_smscServerProxy == null)
            //{
            //    _smscClients = serviceProvider.GetServices<SmscClient>();
            //}
        }
        public async Task<SendSmsSmscResponse> Handle(SendSmsSmscCommand request, CancellationToken cancellationToken)
        {
            try
            {
                GetSmscSmppClient getSmppClient = new GetSmscSmppClient(_serviceProvider);
                var smppClient = getSmppClient.GetClient(request.transceiverSystemId);
                if (smppClient.Status != ConnectionStatus.Bound)
                {
                    return await SmscRabbitMQSaveSMS(request);
                }
                // Create source and destination addresses for the SMS
                var sourceAddress = new SmeAddress(request.srcAdr, (AddressTON)byte.Parse(request.srcTon), (AddressNPI)byte.Parse(request.srcNpi));
                var destinationAddress = new SmeAddress(request.dstAdr, (AddressTON)byte.Parse(request.dstTon), (AddressNPI)byte.Parse(request.dstNpi));

                // Construct the SubmitSmBuilder for the SMS message
                var builder = Inetlab.SMPP.SMS.ForSubmit()
                    .From(sourceAddress)
                    .To(destinationAddress)
                    .Coding((DataCodings)Enum.Parse(typeof(DataCodings), request.DataCodings))
                    .Text(request.smsText)
                    .ExpireIn(TimeSpan.FromDays(2))
                    .DeliveryReceipt();

                // Submit the SMS via the appropriate smppClient and get the response
                var response = await SubmitSmsAsync(smppClient, builder);
                if (response.All(x => x.Header.Status == CommandStatus.SMPPCLIENT_NOCONN))
                {
                    return await SmscRabbitMQSaveSMS(request);
                }
                // Create and return the response object
                return CreateSendSmsSmscResponse(response);
            }
            catch (Exception ex)
            {
                // Return an error response if an exception occurs
                return new SendSmsSmscResponse
                {
                    resultCode = -1,
                    resultMessage = $"Error: {ex.Message}"
                };
            }
        }

    }
}
