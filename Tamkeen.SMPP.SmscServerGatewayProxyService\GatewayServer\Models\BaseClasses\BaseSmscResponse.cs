﻿using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses
{
    /// <summary>
    /// Represents the base class for all SMSC responses.
    /// Contains common properties like result code and message that indicate the outcome of the request.
    /// </summary>
    public class BaseSmscResponse : IBaseSmscResponse
    {
        /// <summary>
        /// Gets or sets the result code indicating the success or failure of the request.
        /// </summary>
        public int resultCode { get; set; }

        /// <summary>
        /// Gets or sets the message describing the result of the request.
        /// </summary>
        public string resultMessage { get; set; }
    }
}
