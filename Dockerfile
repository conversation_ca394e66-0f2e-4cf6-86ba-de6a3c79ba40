# Base image for running the application
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

# Add a non-root user
RUN adduser --disabled-password --gecos "" appuser && chown -R appuser /app
USER appuser

# Expose required ports
EXPOSE 5015 7020 28020 28013 28014 28094 28095 28016 29000

# Base image for building the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files and restore dependencies
COPY ["Tamkeen.SMPP.SmscServerGatewayProxyService.Api/Tamkeen.SMPP.SmscServerGatewayProxyService.Api.csproj", "Tamkeen.SMPP.SmscServerGatewayProxyService.Api/"]
COPY ["Tamkeen.SMPP.SmscServerGatewayProxyService/Tamkeen.SMPP.SmscServerGatewayProxyService.csproj", "Tamkeen.SMPP.SmscServerGatewayProxyService/"]
COPY Tamkeen.SMPP.SmscServerGatewayProxyService.Api/nuget.config /root/.nuget/NuGet/nuget.config
RUN dotnet restore "Tamkeen.SMPP.SmscServerGatewayProxyService.Api/Tamkeen.SMPP.SmscServerGatewayProxyService.Api.csproj" --configfile /root/.nuget/NuGet/nuget.config

# Copy the entire solution and build
COPY . .
WORKDIR "/src/Tamkeen.SMPP.SmscServerGatewayProxyService.Api"
RUN dotnet build "Tamkeen.SMPP.SmscServerGatewayProxyService.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Publish the application
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Tamkeen.SMPP.SmscServerGatewayProxyService.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Final stage for running the application
FROM base AS final
WORKDIR /app

# Copy published output from the publish stage
COPY --from=publish /app/publish .

# Switch to root for privileged commands
USER root

# Add SSL certificate
RUN mkdir -p /app/https
COPY Tamkeen.SMPP.SmscServerGatewayProxyService.Api/certificates/aspnetapp.pfx /app/https
COPY Tamkeen.SMPP.SmscServerGatewayProxyService.Api/certificates/rootca.crt /usr/local/share/ca-certificates/
RUN update-ca-certificates

# Verify file exists
RUN ls -l /app/https

# Environment configuration
ENV ASPNETCORE_URLS="https://+:7020;http://+:5015"
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_Kestrel__Certificates__Default__Path=/app/https/aspnetapp.pfx

# Do not store sensitive data in the Dockerfile
# Set this value at runtime using `docker run -e`
# ENV ASPNETCORE_Kestrel__Certificates__Default__Password=S24GNhaqCcX7VLu3

# Switch back to non-root user
USER appuser

# Run the application
ENTRYPOINT ["dotnet", "Tamkeen.SMPP.SmscServerGatewayProxyService.Api.dll"]
