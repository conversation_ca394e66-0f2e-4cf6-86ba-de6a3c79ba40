﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class SmscSenderConfiguration {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SmscSenderConfiguration() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Tamkeen.SMPP.SmscServerGatewayProxyService.ResourceManager.SmscSenderConfiguratio" +
                            "n", typeof(SmscSenderConfiguration).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 00.
        /// </summary>
        public static string OtherValidInternationalNumberDZero {
            get {
                return ResourceManager.GetString("OtherValidInternationalNumberDZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to +.
        /// </summary>
        public static string OtherValidInternationalNumberPlus {
            get {
                return ResourceManager.GetString("OtherValidInternationalNumberPlus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 71.
        /// </summary>
        public static string SabaFonStart {
            get {
                return ResourceManager.GetString("SabaFonStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash.
        /// </summary>
        public static string SenderId {
            get {
                return ResourceManager.GetString("SenderId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 967.
        /// </summary>
        public static string SenderYemenInternational {
            get {
                return ResourceManager.GetString("SenderYemenInternational", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} {PropertyValue} is invalid. Must be one of:.
        /// </summary>
        public static string ValidatorInvalidEnum {
            get {
                return ResourceManager.GetString("ValidatorInvalidEnum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {PropertyName} is required..
        /// </summary>
        public static string ValidatorRequired {
            get {
                return ResourceManager.GetString("ValidatorRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 77.
        /// </summary>
        public static string YemenMobileStart77 {
            get {
                return ResourceManager.GetString("YemenMobileStart77", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 78.
        /// </summary>
        public static string YemenMobileStart78 {
            get {
                return ResourceManager.GetString("YemenMobileStart78", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 70.
        /// </summary>
        public static string YGsmStart {
            get {
                return ResourceManager.GetString("YGsmStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 73.
        /// </summary>
        public static string YouStart {
            get {
                return ResourceManager.GetString("YouStart", resourceCulture);
            }
        }
    }
}
