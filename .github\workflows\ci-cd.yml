name: CI/CD Pipeline - Tamkeen SMPP Gateway

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  DOTNET_VERSION: '8.0.x'
  SOLUTION_PATH: './Tamkeen.SMPP.SmscServerGatewayProxyService.Api.sln'
  TEST_PROJECT_PATH: './Tamkeen.SMPP.SmscServerGatewayProxyService.Tests'

jobs:
  # مرحلة البناء والاختبار
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: Cache NuGet packages
      uses: actions/cache@v3
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-
          
    - name: Restore dependencies
      run: dotnet restore ${{ env.SOLUTION_PATH }}
      
    - name: Build solution
      run: dotnet build ${{ env.SOLUTION_PATH }} --no-restore --configuration Release
      
    - name: Run unit tests
      run: |
        dotnet test ${{ env.TEST_PROJECT_PATH }} \
          --no-build \
          --configuration Release \
          --filter "FullyQualifiedName!~Integration" \
          --collect:"XPlat Code Coverage" \
          --logger "trx;LogFileName=unit-test-results.trx" \
          --results-directory ./TestResults
          
    - name: Run integration tests
      run: |
        dotnet test ${{ env.TEST_PROJECT_PATH }} \
          --no-build \
          --configuration Release \
          --filter "FullyQualifiedName~Integration" \
          --collect:"XPlat Code Coverage" \
          --logger "trx;LogFileName=integration-test-results.trx" \
          --results-directory ./TestResults
          
    - name: Generate code coverage report
      uses: danielpalme/ReportGenerator-GitHub-Action@5.2.0
      with:
        reports: 'TestResults/**/coverage.cobertura.xml'
        targetdir: 'coveragereport'
        reporttypes: 'Html;Cobertura;JsonSummary'
        
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        files: ./coveragereport/Cobertura.xml
        fail_ci_if_error: false
        verbose: true
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: TestResults/
        
    - name: Upload coverage report
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coveragereport/

  # مرحلة فحص الأمان
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: Install security scan tools
      run: |
        dotnet tool install --global security-scan
        dotnet tool install --global dotnet-outdated-tool
        
    - name: Run security scan
      run: security-scan ${{ env.SOLUTION_PATH }} --export security-report.json
      continue-on-error: true
      
    - name: Check for outdated packages
      run: dotnet outdated ${{ env.SOLUTION_PATH }} --output outdated-packages.json
      continue-on-error: true
      
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          security-report.json
          outdated-packages.json

  # مرحلة بناء Docker Image
  docker-build:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [build-and-test, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: tamkeen/smpp-gateway
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Tamkeen.SMPP.SmscServerGatewayProxyService.Api/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # مرحلة النشر للبيئة التجريبية
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Deploy to staging environment
      run: |
        echo "Deploying to staging environment..."
        # هنا يمكن إضافة خطوات النشر الفعلية
        # مثل استخدام kubectl أو docker-compose
        
  # مرحلة النشر للبيئة الإنتاجية
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Deploy to production environment
      run: |
        echo "Deploying to production environment..."
        # هنا يمكن إضافة خطوات النشر الفعلية

  # مرحلة إشعارات النتائج
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [build-and-test, security-scan, docker-build]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.build-and-test.result == 'success' && needs.security-scan.result == 'success' }}
      run: |
        echo "✅ Pipeline completed successfully!"
        # يمكن إضافة إشعارات Slack أو Teams هنا
        
    - name: Notify failure
      if: ${{ needs.build-and-test.result == 'failure' || needs.security-scan.result == 'failure' }}
      run: |
        echo "❌ Pipeline failed!"
        # يمكن إضافة إشعارات فشل هنا
