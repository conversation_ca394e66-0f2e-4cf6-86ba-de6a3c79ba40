﻿namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces
{
    /// <summary>
    /// Interface defining the contract for all SMSC response classes.
    /// </summary>
    public interface IBaseSmscResponse
    {
        /// <summary>
        /// Gets or sets the result code of the request processing.
        /// Indicates whether the request was successful or if an error occurred.
        /// </summary>
        int resultCode { get; set; }

        /// <summary>
        /// Gets or sets the message that describes the result of the request.
        /// Provides additional information about the outcome of the request.
        /// </summary>
        string resultMessage { get; set; }
    }
}
