﻿using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Serilog;
using System.Net;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public class Startup
    {
        public IConfiguration Configuration { get; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        // تسجيل الخدمات
        public void ConfigureServices(IServiceCollection services)
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            services.AddControllers();
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen();

            //// تهيئة Serilog
            //Log.Logger = new LoggerConfiguration()
            //    .ReadFrom.Configuration(Configuration.GetSection("Serilog"))
            //    .MinimumLevel.Debug()
            //    .WriteTo.File("c:\\log\\Tamkeen.log", rollingInterval: RollingInterval.Day)
            //    .CreateLogger();

            //services.AddSingleton(Log.Logger);

            // تهيئة SmscServices
            var smscSettings = Configuration.GetSection("SmscSettings");
            services.AddSmscServerGatewayProxyServices(Configuration, smscSettings);

            services.AddCustomServices(Configuration);

            //FcmWhatsappSettings fcmWhatsappSettings = Configuration.GetSection(nameof(FcmWhatsappSettings)).Get<FcmWhatsappSettings>();
            //var appSet = services.Configure<FcmWhatsappSettings>(FcmWhatsappSettings1);
            //var appSet = builder.Services.Configure<FcmWhatsappSettings>(FcmWhatsappSettings1);
        }

        // إعداد الـ Middleware
        public void Configure(IApplicationBuilder app)
        {
            app.UseExceptionHandler(errorApp =>
            {
                errorApp.Run(async context =>
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";

                    var errorFeature = context.Features.Get<IExceptionHandlerFeature>();
                    var exception = errorFeature?.Error;

                    if (exception != null)
                    {
                        // Log the error details including stack trace
                        Log.Logger.Error(exception, "An unhandled exception occurred: {Message}", exception.Message);

                        // Optionally include detailed information in the response for local development
                        var response = new
                        {
                            StatusCode = context.Response.StatusCode,
                            ErrorMessage = exception.Message,
                            StackTrace = exception.StackTrace
                        };

                        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
                    }
                });
            });

            //if (app.ApplicationServices.GetService<Microsoft.AspNetCore.Hosting.IWebHostEnvironment>()?.IsDevelopment() ?? false)
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            //app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                //endpoints.MapHealthChecksUI();
                //endpoints.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions()
                //{
                //    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                //}); // Add health checks
                endpoints.MapHealthChecks("/health", new HealthCheckOptions
                {
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                });
                endpoints.MapHealthChecks("/health/proxy", new HealthCheckOptions
                {
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                });
                endpoints.MapControllers();
            });
        }
    }
}
