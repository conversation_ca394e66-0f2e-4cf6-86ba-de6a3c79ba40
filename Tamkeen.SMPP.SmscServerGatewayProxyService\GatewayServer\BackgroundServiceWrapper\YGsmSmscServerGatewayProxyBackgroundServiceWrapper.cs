﻿using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.BackgroundServiceWrapper
{
    public class YGsmSmscServerGatewayProxyBackgroundServiceWrapper : ISmscServerGatewayProxyBackgroundService, IHostedService
    {
        private readonly SmscServerGatewayProxyBackgroundService _smscService;
        private CancellationTokenSource _cancellationTokenSource;

        public YGsmSmscServerGatewayProxyBackgroundServiceWrapper(IServiceProvider serviceProvider, SmscServerGatewayProxySettings settings)
        {
            _smscService = new SmscServerGatewayProxyBackgroundService(serviceProvider, settings);
            _cancellationTokenSource = new CancellationTokenSource();
            //_smscService.StopAsync(default);
        }

        public string TransceiverSystemId => _smscService.TransceiverSystemId;

        public async Task RestartServiceAsync(CancellationToken cancellationToken)
        {
            await _smscService.RestartServiceAsync(_cancellationTokenSource.Token);
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _cancellationTokenSource = new CancellationTokenSource();
            return _smscService.StartAsync(_cancellationTokenSource.Token);
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _cancellationTokenSource.CancelAsync();
            return _smscService.StopAsync(_cancellationTokenSource.Token);
        }
        public string GetTransceiverSystemId()
        {
            return _smscService.TransceiverSystemId; // Assuming TransceiverSystemId is a property in the smscService
        }
        public bool IsRunning()
        {
            return _smscService.IsRunning();
        }
        public async Task<bool> IsSmscProxyClientWorking()
        {
            return await _smscService.IsSmscProxyClientWorking();
        }
        public async Task<IBaseSmscResponse> SendSmsAsync(SendSmsSmscCommand sendSmsSmscCommand, CancellationToken cancellationToken)
        {
            return await _smscService.SendSmsAsync(sendSmsSmscCommand, cancellationToken);
        }
        public Task<bool> ReInitializeSmscServerProxyClientAsync(CancellationToken cancellationToken)
        {
            return _smscService.ReInitializeSmscServerProxyClientAsync(_cancellationTokenSource.Token);
        }
        public IReadOnlyList<SmppConnectedClients> SmppServerConnectedClients()
        {
            return _smscService.SmppServerConnectedClients();
        }
        public SmscServerGatewayProxySettings GetSettings()
        {
            return _smscService.smscServerGatewayProxySettings;
        }
    }
}
