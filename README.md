# Tamkeen Notification

This project hosts the SMPP gateway proxy service used by Tamkeen. It contains the gateway server and supporting services such as RabbitMQ integration and rate limiting.

## Architecture

The gateway exposes an SMPP server (`SmscServerProxy`) that forwards traffic to upstream providers and to RabbitMQ when the client is offline. A lightweight `IRateLimiter` based on a leaky bucket algorithm protects the proxy from overload.

## Running

```
dotnet build
dotnet test
```

The service expects RabbitMQ and Redis instances according to the configuration files.

## Examples

### SMS submission builder

```csharp
ISubmitSmBuilder builder = Inetlab.SMPP.SMS.ForSubmit()
    .From(sourceAddress)
    .To(destinationAddress)
    .Coding(coding)
    .Text(request.smsText)
    .ExpireIn(TimeSpan.FromDays(2))
    .DeliveryReceipt();
```
