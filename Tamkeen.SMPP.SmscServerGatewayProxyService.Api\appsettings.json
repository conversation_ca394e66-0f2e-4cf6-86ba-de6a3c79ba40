{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"LogPath": "c:/logs/TamkeenSMPPSmscServerProxyService"}, "RabbitMQSettings": {"hostName": "localhost", "userName": "guest", "password": "guest", "port": 5672, "MaxMessageAgeMinutes": 1440}, "HealthCheckOptions": {"Self": {"Name": "self", "Tags": ["live"]}, "SmscProxyServer": {"Name": "Proxy Server Health Check", "Tags": ["proxy"]}, "Elasticsearch": {"Name": "Elasticsearch Health Check", "Tags": ["elasticsearch"]}, "Redis": {"Name": "Redis Health Check", "Tags": ["redis"]}, "DalUrl": {"Name": "DAL URL Health Check", "Tags": ["dal"]}, "SmscService": {"Name": "Smsc Service Health Check", "Tags": ["smsc"]}}}