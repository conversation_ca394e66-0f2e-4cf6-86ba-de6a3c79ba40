using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;
using System.Net;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Tools
{
    /// <summary>
    /// أداة لاختبار الاتصال مع HashiCorp Vault وتشخيص المشاكل
    /// </summary>
    public class VaultTester
    {
        private readonly ILogger<VaultTester> _logger;
        private readonly IVaultClient _vaultClient;
        private readonly string _vaultAddress;
        private readonly string _mountPoint;
        private readonly string _secretPath;

        public VaultTester(IConfiguration configuration, ILogger<VaultTester> logger)
        {
            _logger = logger;
            
            _vaultAddress = configuration["Vault:Address"];
            var vaultToken = configuration["Vault:Token"];
            _mountPoint = configuration["Vault:MountPoint"] ?? "secret";
            _secretPath = configuration["Vault:SecretPath"] ?? "data/tamkeen-smpp";
            
            var ignoreSslErrors = bool.Parse(configuration["Vault:IgnoreSslErrors"] ?? "false");
            
            if (ignoreSslErrors)
            {
                ServicePointManager.ServerCertificateValidationCallback = 
                    (sender, cert, chain, sslPolicyErrors) => true;
            }
            
            var authMethod = new TokenAuthMethodInfo(vaultToken);
            var vaultClientSettings = new VaultClientSettings(_vaultAddress, authMethod);
            _vaultClient = new VaultClient(vaultClientSettings);
        }

        /// <summary>
        /// اختبار شامل للاتصال مع Vault
        /// </summary>
        public async Task<VaultTestResult> RunComprehensiveTestAsync()
        {
            var result = new VaultTestResult();
            
            _logger.LogInformation("Starting comprehensive Vault test...");
            
            // 1. اختبار الاتصال الأساسي
            result.ConnectionTest = await TestConnectionAsync();
            
            // 2. اختبار صحة Vault
            result.HealthTest = await TestVaultHealthAsync();
            
            // 3. اختبار قراءة الأسرار
            result.SecretReadTest = await TestSecretReadAsync();
            
            // 4. اختبار مسارات مختلفة
            result.PathTests = await TestDifferentPathsAsync();
            
            // 5. اختبار التوكن
            result.TokenTest = await TestTokenValidityAsync();
            
            result.OverallSuccess = result.ConnectionTest && result.HealthTest && result.SecretReadTest;
            
            _logger.LogInformation("Vault test completed. Overall success: {Success}", result.OverallSuccess);
            
            return result;
        }

        /// <summary>
        /// اختبار الاتصال الأساسي مع Vault
        /// </summary>
        private async Task<bool> TestConnectionAsync()
        {
            try
            {
                _logger.LogInformation("Testing basic connection to Vault at {VaultAddress}", _vaultAddress);
                
                // محاولة الاتصال الأساسي
                var health = await _vaultClient.V1.System.GetHealthStatusAsync();
                
                _logger.LogInformation("✅ Basic connection successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Basic connection failed: {ErrorMessage}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// اختبار صحة Vault
        /// </summary>
        private async Task<bool> TestVaultHealthAsync()
        {
            try
            {
                _logger.LogInformation("Testing Vault health status...");
                
                var health = await _vaultClient.V1.System.GetHealthStatusAsync();
                
                _logger.LogInformation("✅ Vault health check successful. Initialized: {Initialized}, Sealed: {Sealed}", 
                    health.Initialized, health.Sealed);
                
                return health.Initialized && !health.Sealed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Vault health check failed: {ErrorMessage}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// اختبار قراءة الأسرار من المسار المحدد
        /// </summary>
        private async Task<bool> TestSecretReadAsync()
        {
            try
            {
                _logger.LogInformation("Testing secret read from path: {MountPoint}/{SecretPath}", _mountPoint, _secretPath);
                
                // تنظيف المسار
                var cleanPath = _secretPath?.TrimStart('/');
                if (cleanPath?.StartsWith("data/") == true)
                {
                    cleanPath = cleanPath.Substring(5);
                }
                
                _logger.LogInformation("Using cleaned path: {CleanPath}", cleanPath);
                
                var secrets = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(
                    path: cleanPath,
                    mountPoint: _mountPoint);
                
                if (secrets?.Data?.Data != null && secrets.Data.Data.Count > 0)
                {
                    _logger.LogInformation("✅ Successfully read {SecretCount} secrets", secrets.Data.Data.Count);
                    
                    // عرض أسماء المفاتيح فقط (بدون القيم للأمان)
                    foreach (var key in secrets.Data.Data.Keys)
                    {
                        _logger.LogDebug("Found secret key: {Key}", key);
                    }
                    
                    return true;
                }
                else
                {
                    _logger.LogWarning("⚠️ No secrets found at the specified path");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Secret read failed: {ErrorMessage}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// اختبار مسارات مختلفة لتحديد المسار الصحيح
        /// </summary>
        private async Task<Dictionary<string, bool>> TestDifferentPathsAsync()
        {
            var pathTests = new Dictionary<string, bool>();
            
            var pathsToTest = new[]
            {
                "tamkeen-smpp",
                "data/tamkeen-smpp", 
                "tamkeen-smpp/data",
                "smsc-credentials",
                "database-credentials",
                "external-services"
            };
            
            foreach (var path in pathsToTest)
            {
                try
                {
                    _logger.LogInformation("Testing path: {MountPoint}/{Path}", _mountPoint, path);
                    
                    var secrets = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(
                        path: path,
                        mountPoint: _mountPoint);
                    
                    var success = secrets?.Data?.Data != null && secrets.Data.Data.Count > 0;
                    pathTests[path] = success;
                    
                    if (success)
                    {
                        _logger.LogInformation("✅ Path {Path} contains {SecretCount} secrets", path, secrets.Data.Data.Count);
                    }
                    else
                    {
                        _logger.LogDebug("❌ Path {Path} is empty or not found", path);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("❌ Path {Path} failed: {ErrorMessage}", path, ex.Message);
                    pathTests[path] = false;
                }
            }
            
            return pathTests;
        }

        /// <summary>
        /// اختبار صحة التوكن
        /// </summary>
        private async Task<bool> TestTokenValidityAsync()
        {
            try
            {
                _logger.LogInformation("Testing token validity...");
                
                var tokenInfo = await _vaultClient.V1.Auth.Token.LookupSelfAsync();
                
                if (tokenInfo?.Data != null)
                {
                    _logger.LogInformation("✅ Token is valid. Policies: {Policies}", 
                        string.Join(", ", tokenInfo.Data.Policies ?? new List<string>()));
                    return true;
                }
                else
                {
                    _logger.LogWarning("⚠️ Token lookup returned no data");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Token validation failed: {ErrorMessage}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// اختبار سريع للتحقق من إمكانية الوصول لمسار محدد
        /// </summary>
        public async Task<bool> QuickTestAsync(string testPath = null)
        {
            try
            {
                var pathToTest = testPath ?? _secretPath;
                
                _logger.LogInformation("Quick test for path: {Path}", pathToTest);
                
                var cleanPath = pathToTest?.TrimStart('/');
                if (cleanPath?.StartsWith("data/") == true)
                {
                    cleanPath = cleanPath.Substring(5);
                }
                
                var secrets = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(
                    path: cleanPath,
                    mountPoint: _mountPoint);
                
                var success = secrets?.Data?.Data != null && secrets.Data.Data.Count > 0;
                
                _logger.LogInformation("Quick test result: {Success}", success ? "✅ SUCCESS" : "❌ FAILED");
                
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Quick test failed: {ErrorMessage}", ex.Message);
                return false;
            }
        }
    }

    /// <summary>
    /// نتيجة اختبار Vault
    /// </summary>
    public class VaultTestResult
    {
        public bool ConnectionTest { get; set; }
        public bool HealthTest { get; set; }
        public bool SecretReadTest { get; set; }
        public bool TokenTest { get; set; }
        public Dictionary<string, bool> PathTests { get; set; } = new();
        public bool OverallSuccess { get; set; }
        
        public override string ToString()
        {
            return $"Vault Test Results:\n" +
                   $"- Connection: {(ConnectionTest ? "✅" : "❌")}\n" +
                   $"- Health: {(HealthTest ? "✅" : "❌")}\n" +
                   $"- Secret Read: {(SecretReadTest ? "✅" : "❌")}\n" +
                   $"- Token: {(TokenTest ? "✅" : "❌")}\n" +
                   $"- Overall: {(OverallSuccess ? "✅ SUCCESS" : "❌ FAILED")}";
        }
    }
}
