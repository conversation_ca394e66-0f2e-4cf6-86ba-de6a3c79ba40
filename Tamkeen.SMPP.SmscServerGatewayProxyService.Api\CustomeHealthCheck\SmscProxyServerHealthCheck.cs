﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck
{
    public class SmscProxyServerHealthCheck : IHealthCheck
    {
        private readonly SmscProxyServiceManager _smscServiceManager;
        private readonly Serilog.ILogger _logger;
        private readonly HealthCheckSettings _healthCheckSettings; // Injected settings
        private static readonly ConcurrentDictionary<string, DateTime?> _unhealthySince = new();

        public SmscProxyServerHealthCheck(IServiceProvider serviceProvider, IOptions<HealthCheckSettings> healthCheckSettings)
        {
            _smscServiceManager = serviceProvider.GetService<SmscProxyServiceManager>()
                                  ?? throw new ArgumentNullException(nameof(SmscProxyServiceManager));
            _logger = serviceProvider.GetService<Serilog.ILogger>()
                      ?? throw new ArgumentNullException(nameof(Serilog.ILogger));
            _healthCheckSettings = healthCheckSettings?.Value
                                  ?? throw new ArgumentNullException(nameof(healthCheckSettings));
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var statuses = await _smscServiceManager.GetAllSmscProxysStatusesAsync();

                foreach (var status in statuses)
                {
                    var transceiverSystemId = status.GetType().GetProperty("TransceiverSystemId")?.GetValue(status)?.ToString();
                    var isProxyClientWorking = (bool?)status.GetType().GetProperty("IsSmscProxyClientWorking")?.GetValue(status) ?? false;
                    var isServiceRunning = (bool?)status.GetType().GetProperty("IsSmscServiceRunning")?.GetValue(status) ?? false;

                    if (transceiverSystemId == null)
                        continue;

                    if (!isProxyClientWorking || !isServiceRunning)
                    {
                        var unhealthyStartTime = _unhealthySince.GetValueOrDefault(transceiverSystemId);
                        if (unhealthyStartTime == null)
                        {
                            unhealthyStartTime = DateTime.UtcNow;
                            _unhealthySince[transceiverSystemId] = unhealthyStartTime; // Update dictionary
                        }

                        var downtime = DateTime.UtcNow - unhealthyStartTime.Value;

                        if (!isServiceRunning && downtime.TotalSeconds > _healthCheckSettings.RestartThresholdSeconds)
                        {
                            _logger.Warning("Restarting service for {TransceiverSystemId} due to prolonged downtime.", transceiverSystemId);
                            await _smscServiceManager.RestartSmscServerProxyServiceAsync(transceiverSystemId, cancellationToken);
                        }
                        else if (!isProxyClientWorking && isServiceRunning && downtime.TotalSeconds > _healthCheckSettings.ReinitializeThresholdSeconds)
                        {
                            _logger.Warning("Reinitializing proxy client for {TransceiverSystemId} due to prolonged downtime.", transceiverSystemId);
                            await _smscServiceManager.ReInitializeSmscServerProxyServiceClientAsync(transceiverSystemId, cancellationToken);
                        }
                    }
                    else
                    {
                        // Reset unhealthy timer if the service is healthy again
                        _unhealthySince[transceiverSystemId] = null;
                    }
                }

                // Log and report unhealthy services
                var unhealthyServices = statuses
                    .Where(s =>
                        !(s.GetType().GetProperty("IsSmscProxyClientWorking")?.GetValue(s) as bool? ?? false) ||
                        !(s.GetType().GetProperty("IsSmscServiceRunning")?.GetValue(s) as bool? ?? false))
                    .Select(s => new
                    {
                        TransceiverSystemId = s.GetType().GetProperty("TransceiverSystemId")?.GetValue(s)?.ToString() ?? "Unknown",
                        IsSmscProxyClientWorking = s.GetType().GetProperty("IsSmscProxyClientWorking")?.GetValue(s) as bool? ?? false,
                        IsSmscServiceRunning = s.GetType().GetProperty("IsSmscServiceRunning")?.GetValue(s) as bool? ?? false
                    });

                if (unhealthyServices.Any())
                {
                    _logger.Warning("Some SMSC services are unhealthy: {@UnhealthyServices}", unhealthyServices);
                    return HealthCheckResult.Unhealthy("Some SMSC services are unhealthy.", null, unhealthyServices.ToDictionary(
                        s => s.TransceiverSystemId?.ToString() ?? "Unknown",
                        s => (object)s));
                }

                return HealthCheckResult.Healthy("All SMSC services are running and healthy.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "An error occurred while performing the health check.");
                return HealthCheckResult.Unhealthy("An exception occurred while checking health.", ex);
            }
        }
    }
}
