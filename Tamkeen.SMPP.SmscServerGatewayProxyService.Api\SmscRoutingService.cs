﻿using System.Text.RegularExpressions;
using Microsoft.Extensions.Configuration;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public interface ISmscRoutingService
    {
        bool TryResolve(string dstAdr, out RouteInfo info);
    }

    public sealed class SmscRoutingService : ISmscRoutingService
    {
        private readonly List<RouteInfo> _routes;

        public SmscRoutingService(IConfiguration cfg)
        {
            // 1️  Grab every section that looks like an SMSC definition
            IEnumerable<IConfigurationSection> smsSections =
                cfg.GetSection("SmscSettings").GetChildren()
                   .Concat(cfg.GetChildren()
                              .Where(s => s.Key.StartsWith("Smsc", StringComparison.OrdinalIgnoreCase) &&
                                          s.GetSection("SmscClientSettings").Exists()));

            // 2️  Build route table – **but only keep enabled *sender* clients**
            _routes = smsSections
                .Select(sec => sec.Get<SmscServerGatewayProxySettings>())
                .Where(s =>
                       s.smscClientSettings.enable &&
                       EndsWithTransmitterOrTrans(s.smscClientSettings.transceiverSystemId))
                .Select(s => new RouteInfo
                {
                    Pattern = new Regex(
                                              s.smscClientSettings.mobilePattern,
                                              RegexOptions.Compiled | RegexOptions.IgnoreCase),
                    TransceiverSystemId = s.smscClientSettings.transceiverSystemId,
                    SrcTon = s.smscClientSettings.ton,
                    SrcNpi = s.smscClientSettings.npi
                })
                .ToList();
        }

        public bool TryResolve(string dstAdr, out RouteInfo info)
        {
            info = _routes.FirstOrDefault(r => r.Pattern.IsMatch(dstAdr));
            return info is not null;
        }

        private static bool EndsWithTransmitterOrTrans(string id) =>
            //id.EndsWith("Transmitter", StringComparison.OrdinalIgnoreCase) ||
            id.EndsWith("Trans", StringComparison.OrdinalIgnoreCase);
    }

    public sealed class RouteInfo
    {
        public Regex Pattern { get; init; }
        public string TransceiverSystemId { get; init; }
        public AddressTON SrcTon { get; init; }
        public AddressNPI SrcNpi { get; init; }
    }

}