﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseInterfaces;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities
{
    /// <summary>
    /// Provides utility functions for SMSC operations.
    /// </summary>
    public class SmscGeneralUtilities
    {
        /// <summary>
        /// Asynchronously creates a response object with the specified result code and message.
        /// </summary>
        /// <param name="resultCode">The result code of the operation.</param>
        /// <param name="resultMessage">The result message of the operation.</param>
        /// <returns>A response object containing the result code and message.</returns>
        public static async Task<IBaseSmscResponse> MakeResponse(int resultCode, string resultMessage)
        {
            return new BaseSmscResponse()
            {
                resultCode = resultCode,
                resultMessage = resultMessage
            };
        }
    }
}
