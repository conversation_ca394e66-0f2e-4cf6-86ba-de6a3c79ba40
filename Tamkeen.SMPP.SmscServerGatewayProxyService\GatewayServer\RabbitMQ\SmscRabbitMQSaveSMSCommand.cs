﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ
{
    /// <summary>
    /// Command to save SMS to RabbitMQ for later processing.
    /// </summary>
    public class SmscRabbitMQSaveSMSCommand
    {
        public string transceiverSystemId { get; set; }
        //public DataCodings coding { get; set(DataCodings)Enum.Parse(typeof(DataCodings), string DataCodings); }
        //UCS2 for Arabic 1
        //Latin1 for English 2
        public DataCodings dataCodings { get; set; }
        public string srcAdr { get; set; }
        public AddressNPI srcNpi { get; set; }
        public AddressTON srcTon { get; set; }
        public string dstAdr { get; set; }
        public AddressNPI dstNpi { get; set; }
        public AddressTON dstTon { get; set; }
        public SMSCDeliveryReceipt deliveryReceipt { get; set; }

        public string smsText { get; set; }
        public SubmitMode submitMode { get; set; }
        //public SubmitMode mode  { get; set(SubmitMode)Enum.Parse(typeof(SubmitMode), string SubmitMode); }
        //ShortMessage 1
        //Payload 2
        //ShortMessageWithSAR 3
        public SmsDstType smsDstType { get; set; }
        //local 1
        //other 2
        //public SmscServerProxyClient smscServerProxyClient { get; set; }
        //public string MessageId { get; set; }

    }


}
