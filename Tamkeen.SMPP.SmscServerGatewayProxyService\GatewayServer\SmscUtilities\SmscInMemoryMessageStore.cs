﻿using System;
using System.Collections.Concurrent;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Interfaces;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.SmscUtilities
{
    public class SmscInMemoryMessageStore : ISmscSMSMessageStore
    {
        private readonly ConcurrentDictionary<string, SmscSMSMessage> _store = new ConcurrentDictionary<string, SmscSMSMessage>();
        private readonly ConcurrentDictionary<string, SmscSMSMessage> _storeByRemoteMessageId = new ConcurrentDictionary<string, SmscSMSMessage>();


        public void Add(SmscSMSMessage message, EncodingMapper encodingMapper)
        {
            lock (_store)
            {
                if (!_store.TryAdd(message.Id, message))
                {
                    throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the same Id '{message.Id}' already exists.");
                }

                if (!string.IsNullOrEmpty(message.RemoteMessageId))
                {

                    if (!_storeByRemoteMessageId.TryAdd(message.RemoteMessageId, message))
                    {
                        throw new InvalidOperationException(
                            $"{nameof(SmscSMSMessage)} with the same remote MessageId '{message.RemoteMessageId}' already exists.");
                    }

                }
            }

        }

        public SmscSMSMessage Update(string messageId, Action<SmscSMSMessage> update, EncodingMapper encodingMapper)
        {
            lock (_store)
            {

                return _store.AddOrUpdate(messageId,
                    id => throw new InvalidOperationException($"SMS message with id {messageId} not found."),
                    (s, message) =>
                    {
                        update(message);

                        if (!string.IsNullOrEmpty(message.RemoteMessageId))
                        {
                            _storeByRemoteMessageId.TryAdd(message.RemoteMessageId, message);
                        }

                        return message;
                    });

            }
        }

        public bool TryUpdateByRemoteMessageId(string remoteMessageId, Action<SmscSMSMessage> update,
            EncodingMapper clientEncodingMapper, out SmscSMSMessage message)
        {
            lock (_store)
            {
                if (!_storeByRemoteMessageId.TryGetValue(remoteMessageId, out message)) return false;

                update(message);



                return true;
            }
        }

        public void Remove(string messageId)
        {
            SmscSMSMessage oldMessage;

            lock (_store)
            {

                if (!_store.TryRemove(messageId, out oldMessage))
                {
                    throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the Id '{messageId}' doesn't exist.");
                }

                if (!string.IsNullOrEmpty(oldMessage.RemoteMessageId))
                {
                    SmscSMSMessage message;
                    if (!_storeByRemoteMessageId.TryRemove(oldMessage.RemoteMessageId, out message))
                    {
                        throw new InvalidOperationException($"{nameof(SmscSMSMessage)} with the Id '{oldMessage.RemoteMessageId}' doesn't exist.");
                    }

                }
            }


        }




    }


}