﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Serilog;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp;
using FluentValidation.AspNetCore;
using FluentValidation;
using System.Reflection;
using Microsoft.AspNetCore.Mvc.Filters;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Attributes;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Filters;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.ProjectSettings;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Services;


namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddCustomServices(this IServiceCollection services, IConfiguration configuration)
        {
            Log.Logger = new LoggerConfiguration()
                    .WriteTo.Console()
                    .WriteTo.File("c:/logs/TamkeenSMPPSmscServerProxyService/DependencyInjection-log.txt")
                    .CreateLogger();
            services.AddSingleton<SmscProxyServiceManager>();
            services.AddSingleton<SmscServiceManager>();
            services.AddSingleton<ISmscRoutingService, SmscRoutingService>();
            services.AddScoped<WhatsAppController>();
            services.AddProjectControllers();
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
            services.AddCustomFluentValidation();
            // Bind FcmWhatsappSettings directly from the configuration
            var fcmWhatsappSettings = configuration.GetSection(nameof(FcmWhatsappSettings)).Get<FcmWhatsappSettings>();
            services.Configure<HealthCheckSettings>(configuration.GetSection(nameof(HealthCheckSettings)));

            // Configure FcmWhatsappSettings for IOptions injection
            services.Configure<FcmWhatsappSettings>(configuration.GetSection(nameof(FcmWhatsappSettings)));
            #region Configure Serilog
            //var elasticSearchUrl = configuration["Serilog:ElasticSearchUrl"];
            //if (!string.IsNullOrEmpty(elasticSearchUrl))
            //{
            //    Log.Logger = new LoggerConfiguration()
            //        .ReadFrom.Configuration(configuration)
            //        .WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(elasticSearchUrl))
            //        {
            //            AutoRegisterTemplate = true,
            //            IndexFormat = "serilog-{0:yyyy.MM.dd}"
            //        })
            //        .CreateLogger();
            //}
            //else
            //{
            //    Log.Logger = new LoggerConfiguration()
            //        .ReadFrom.Configuration(configuration)
            //        .WriteTo.File("c:\\log\\Tamkeen.SMPP.Api.log", rollingInterval: RollingInterval.Day)
            //        .CreateLogger();
            //}
            try
            {
                services.AddSingleton<TamkeenLoggingConfig>(provider =>
                {
                    try
                    {
                        Log.Information("register of TamkeenLoggingConfig start");

                        var configuration = provider.GetRequiredService<IConfiguration>();
                        var tamkeenLoggingConfig = configuration.GetSection(nameof(TamkeenLoggingConfig)).Get<TamkeenLoggingConfig>();
                        if (tamkeenLoggingConfig == null)
                        {
                            throw new ArgumentNullException(nameof(TamkeenLoggingConfig), "TamkeenLoggingConfig is missing in the configuration.");
                        }

                        Log.Information("register of TamkeenLoggingConfig done");

                        return tamkeenLoggingConfig;
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error registering TamkeenLoggingConfig");
                        throw; // Re-throw the exception to ensure the application fails fast
                    }
                });

                services.AddSingleton<ITamkeenLogRegister>(provider =>
                {
                    try
                    {
                        Log.Information("register of ITamkeenLogRegister start");

                        var tamkeenLoggingConfig = provider.GetRequiredService<TamkeenLoggingConfig>();
                        var tamkeenLogRegister = new TamkeenLogRegister(tamkeenLoggingConfig);
                        if (tamkeenLogRegister == null)
                        {
                            throw new ArgumentNullException(nameof(TamkeenLoggingConfig), "TamkeenLoggingConfig is missing in the configuration.");
                        }

                        Log.Information("TamkeenLogRegister: {TamkeenLogRegister}", tamkeenLogRegister);

                        return tamkeenLogRegister;
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error registering ITamkeenLogRegister");
                        throw; // Re-throw the exception to ensure the application fails fast
                    }
                });

                services.AddSingleton<Serilog.ILogger>(provider =>
                {
                    try
                    {
                        var tamkeenLogRegister = provider.GetRequiredService<ITamkeenLogRegister>();
                        Log.Information("TamkeenLogRegister: {TamkeenLogRegister}", tamkeenLogRegister.Logger);
                        return tamkeenLogRegister.Logger;
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error registering Serilog.ILogger");
                        throw; // Re-throw the exception to ensure the application fails fast
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during service registration");
            }
            finally
            {
                Log.Information("The register finally end");
            }
            //var tamkeenLoggingConfig = configuration.GetSection(nameof(TamkeenLoggingConfig)).Get<TamkeenLoggingConfig>(); ;
            //TamkeenLogRegister tamkeenLogRegister = new TamkeenLogRegister(tamkeenLoggingConfig);
            //// Register LogRegister as Singleton
            //services.AddSingleton<ITamkeenLogRegister>(provider =>
            //{

            //    return tamkeenLogRegister;
            //});
            //services.AddSingleton(tamkeenLogRegister.Logger);
            #endregion


            #region Health Checks
            // إضافة Health Check بسيط
            services.AddHealthChecks()
    .AddCheck("self", () => HealthCheckResult.Healthy(), tags: new[] { "live" })
    .AddCheck<SmscProxyServerHealthCheck>("Proxy Server Health Check", tags: new[] { "proxy" });
            services.AddHealthChecks()
    .AddCheck<ElasticsearchHealthCheck>("Elasticsearch Health Check", tags: new[] { "elasticsearch" })
    .AddCheck<RedisHealthCheck>("Redis Health Check", tags: new[] { "redis" })
    .AddCheck<DalUrlHealthCheck>("DAL URL Health Check", tags: new[] { "dal" });


            var healthChecksConfig = configuration.GetSection(nameof(HealthChecksUI)).Get<HealthChecksUI>();

            services.AddHealthChecksUI(setupSettings =>
            {
                if (healthChecksConfig?.HealthChecks != null)
                {
                    foreach (var healthCheck in healthChecksConfig.HealthChecks)
                    {
                        setupSettings.AddHealthCheckEndpoint(healthCheck.Name, healthCheck.Uri);
                    }
                }
            }).AddInMemoryStorage(); // Using in-memory storage for simplicity

            services.AddHealthChecks()
    .AddCheck<SmscServiceHealthCheck>("Smsc Service Health Check", tags: new[] { "smsc" });

            #endregion

            // تسجيل خدمة التشفير
            services.AddSingleton<IEncryptionService, EncryptionService>();

            // تكوين SMSC Settings مع كلمات المرور المشفرة
            // ... قبل نهاية AddCustomServices
            // تسجيل خدمة التشفير
            services.AddSingleton<IEncryptionService, EncryptionService>();

            // تكوين SmscSettings مع كلمات المرور المُشفَّرة
            services.AddOptions<SmscSettings>()
         .Configure<IEncryptionService>((opts, enc) =>
         {
             /* ░░░ YGSM (Transceiver + بوابة الخادم) ░░░ */
             opts.SmscYGsmServerGatewayProxySettings ??= new();
             var ygsm = opts.SmscYGsmServerGatewayProxySettings.SmscClientSettings ??= new();
             ygsm.clientUri = configuration["SMSC_YGSM_CLIENT_URI"];
             ygsm.systemId = configuration["SMSC_YGSM_SYSTEM_ID"];
             ygsm.encryptedPassword = configuration["SMSC_YGSM_PASSWORD_ENCRYPTED"];
             ygsm.mobilePattern = configuration["SMSC_YGSM_MOBILE_PATTERN"];
             ygsm.SetEncryptionService(enc);
             opts.SmscYGsmServerGatewayProxySettings.SmscServerSetting ??= new();
             opts.SmscYGsmServerGatewayProxySettings.SmscServerSetting.SmscServerPort =
                int.Parse(configuration["SMSC_YGSM_SERVER_PORT"]);

             /* ░░░ YGSM (Transmitter) — إذا وُجد مستقبلًا متغيّرات خاصة به ░░░ */
             opts.SmscYGsmTransmitterServerGatewayProxySettings ??= new();

             /* ░░░ YOU – Transmitter ░░░ */
             opts.SmscYouTransmitterServerGatewayProxySettings ??= new();
             var youTx = opts.SmscYouTransmitterServerGatewayProxySettings.SmscClientSettings ??= new();
             youTx.clientUri = configuration["SMSC_YOU_TRANSMITTER_CLIENT_URI"];
             youTx.systemId = configuration["SMSC_YOU_TRANSMITTER_SYSTEM_ID"];
             youTx.encryptedPassword = configuration["SMSC_YOU_TRANSMITTER_PASSWORD_ENCRYPTED"];
             youTx.mobilePattern = configuration["SMSC_YOU_TRANSMITTER_MOBILE_PATTERN"];
             youTx.SetEncryptionService(enc);
             opts.SmscYouTransmitterServerGatewayProxySettings.SmscServerSetting ??= new();
             opts.SmscYouTransmitterServerGatewayProxySettings.SmscServerSetting.SmscServerPort =
                int.Parse(configuration["SMSC_YOU_TRANSMITTER_SERVER_PORT"]);

             /* ░░░ YOU – Receiver ░░░ */
             opts.SmscYouReceiverServerGatewayProxySettings ??= new();
             var youRx = opts.SmscYouReceiverServerGatewayProxySettings.SmscClientSettings ??= new();
             youRx.clientUri = configuration["SMSC_YOU_RECEIVER_CLIENT_URI"];
             youRx.systemId = configuration["SMSC_YOU_RECEIVER_SYSTEM_ID"];
             youRx.password = configuration["SMSC_YOU_RECEIVER_PASSWORD"];        // كلمة مرور غير مشفَّرة
             youRx.mobilePattern = configuration["SMSC_YOU_RECEIVER_MOBILE_PATTERN"];
             opts.SmscYouReceiverServerGatewayProxySettings.SmscServerSetting ??= new();
             opts.SmscYouReceiverServerGatewayProxySettings.SmscServerSetting.SmscServerPort =
                int.Parse(configuration["SMSC_YOU_RECEIVER_SERVER_PORT"]);

             /* ░░░ Sabafon – Transmitter ░░░ */
             opts.SmscSabaFonTransmitterServerGatewayProxySettings ??= new();
             var sabaTx = opts.SmscSabaFonTransmitterServerGatewayProxySettings.SmscClientSettings ??= new();
             sabaTx.clientUri = configuration["SMSC_SABAFON_TRANSMITTER_CLIENT_URI"];
             sabaTx.systemId = configuration["SMSC_SABAFON_TRANSMITTER_SYSTEM_ID"];
             sabaTx.encryptedPassword = configuration["SMSC_SABAFON_TRANSMITTER_PASSWORD_ENCRYPTED"];
             sabaTx.mobilePattern = configuration["SMSC_SABAFON_TRANSMITTER_MOBILE_PATTERN"];
             sabaTx.SetEncryptionService(enc);
             opts.SmscSabaFonTransmitterServerGatewayProxySettings.SmscServerSetting ??= new();
             opts.SmscSabaFonTransmitterServerGatewayProxySettings.SmscServerSetting.SmscServerPort =
                int.Parse(configuration["SMSC_SABAFON_TRANSMITTER_SERVER_PORT"]);

             /* ░░░ Sabafon – Receiver ░░░ */
             opts.SmscSabaFonReceiverServerGatewayProxySettings ??= new();
             var sabaRx = opts.SmscSabaFonReceiverServerGatewayProxySettings.SmscClientSettings ??= new();
             sabaRx.clientUri = configuration["SMSC_SABAFON_RECEIVER_CLIENT_URI"];
             sabaRx.systemId = configuration["SMSC_SABAFON_RECEIVER_SYSTEM_ID"];
             sabaRx.password = configuration["SMSC_SABAFON_RECEIVER_PASSWORD"];   // غير مشفَّرة
             sabaRx.mobilePattern = configuration["SMSC_SABAFON_RECEIVER_MOBILE_PATTERN"];
             opts.SmscSabaFonReceiverServerGatewayProxySettings.SmscServerSetting ??= new();
             opts.SmscSabaFonReceiverServerGatewayProxySettings.SmscServerSetting.SmscServerPort =
                int.Parse(configuration["SMSC_SABAFON_RECEIVER_SERVER_PORT"]);

             /* ░░░ Yemen Mobile ░░░ */
             opts.SmscYemenMobileServerGatewayProxySettings ??= new();
             var ym = opts.SmscYemenMobileServerGatewayProxySettings.SmscClientSettings ??= new();
             ym.clientUri = configuration["SMSC_YEMENMOBILE_CLIENT_URI"];
             ym.systemId = configuration["SMSC_YEMENMOBILE_SYSTEM_ID"];
             ym.password = configuration["SMSC_YEMENMOBILE_PASSWORD"];         // غير مشفَّرة
             ym.mobilePattern = configuration["SMSC_YEMENMOBILE_MOBILE_PATTERN"];
             opts.SmscYemenMobileServerGatewayProxySettings.SmscServerSetting ??= new();
             opts.SmscYemenMobileServerGatewayProxySettings.SmscServerSetting.SmscServerPort =
                int.Parse(configuration["SMSC_YEMENMOBILE_SERVER_PORT"]);
         });




            return services;
        }

        public static IApplicationBuilder UseCustomServices(this IApplicationBuilder app)
        {
            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                // إضافة Health Check على مسار "/health"
                endpoints.MapHealthChecks("/health");
            });

            return app;
        }
        public static void AddProjectControllers(this IServiceCollection services)
        {
            services.AddControllers(options =>
            {
                options.Filters.Add(typeof(ValidateModelStateAttribute));
                options.Filters.Add(new ApiExceptionFilter());
            });

            services.AddCors();
        }
        public static void AddCustomFluentValidation(this IServiceCollection services)
        {
            services.AddFluentValidationAutoValidation().AddFluentValidationClientsideAdapters();
        }
    }
}
