﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using Serilog;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.WhatsApp;
using FluentValidation.AspNetCore;
using FluentValidation;
using System.Reflection;
using Microsoft.AspNetCore.Mvc.Filters;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Attributes;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Filters;
using Tamkeen.SMPP.SmscServerGatewayProxyService.Api.ProjectSettings;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using System.IO;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddCustomServices(this IServiceCollection services, IConfiguration configuration)
        {
            var logPath = configuration["Serilog:LogPath"] ?? "c:/logs/TamkeenSMPPSmscServerProxyService";
            Log.Logger = new LoggerConfiguration()
                    .WriteTo.Console()
                    .WriteTo.File(Path.Combine(logPath, "DependencyInjection-log.txt"))
                    .CreateLogger();
            services.AddSingleton<SmscProxyServiceManager>();
            services.AddSingleton<SmscServiceManager>();
            services.AddSingleton<ISmscRoutingService, SmscRoutingService>();
            services.AddScoped<IWhatsAppService, WhatsAppService>();
            services.AddProjectControllers();
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
            services.AddCustomFluentValidation();
            // Bind and validate FcmWhatsappSettings using Options pattern
            services.AddOptions<FcmWhatsappSettings>()
                    .Bind(configuration.GetSection(nameof(FcmWhatsappSettings)))
                    .ValidateDataAnnotations();
            services.Configure<HealthCheckSettings>(configuration.GetSection(nameof(HealthCheckSettings)));
            services.Configure<HealthCheckOptions>(configuration.GetSection(nameof(HealthCheckOptions)));
            #region Configure Serilog
            //var elasticSearchUrl = configuration["Serilog:ElasticSearchUrl"];
            //if (!string.IsNullOrEmpty(elasticSearchUrl))
            //{
            //    Log.Logger = new LoggerConfiguration()
            //        .ReadFrom.Configuration(configuration)
            //        .WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(elasticSearchUrl))
            //        {
            //            AutoRegisterTemplate = true,
            //            IndexFormat = "serilog-{0:yyyy.MM.dd}"
            //        })
            //        .CreateLogger();
            //}
            //else
            //{
            //    Log.Logger = new LoggerConfiguration()
            //        .ReadFrom.Configuration(configuration)
            //        .WriteTo.File("c:\\log\\Tamkeen.SMPP.Api.log", rollingInterval: RollingInterval.Day)
            //        .CreateLogger();
            //}
            try
            {
                services.AddSingleton<TamkeenLoggingConfig>(provider =>
                {
                    try
                    {
                        Log.Information("register of TamkeenLoggingConfig start");

                        var configuration = provider.GetRequiredService<IConfiguration>();
                        var tamkeenLoggingConfig = configuration.GetSection(nameof(TamkeenLoggingConfig)).Get<TamkeenLoggingConfig>();
                        if (tamkeenLoggingConfig == null)
                        {
                            throw new ArgumentNullException(nameof(TamkeenLoggingConfig), "TamkeenLoggingConfig is missing in the configuration.");
                        }

                        Log.Information("register of TamkeenLoggingConfig done");

                        return tamkeenLoggingConfig;
                    }
                    catch (ArgumentNullException ex)
                    {
                        Log.Error(ex, "Error registering TamkeenLoggingConfig");
                        throw;
                    }
                    catch (InvalidOperationException ex)
                    {
                        Log.Error(ex, "Error retrieving configuration for TamkeenLoggingConfig");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Unexpected error registering TamkeenLoggingConfig");
                        throw;
                    }
                });

                services.AddSingleton<ITamkeenLogRegister>(provider =>
                {
                    try
                    {
                        Log.Information("register of ITamkeenLogRegister start");

                        var tamkeenLoggingConfig = provider.GetRequiredService<TamkeenLoggingConfig>();
                        var tamkeenLogRegister = new TamkeenLogRegister(tamkeenLoggingConfig);
                        if (tamkeenLogRegister == null)
                        {
                            throw new ArgumentNullException(nameof(TamkeenLoggingConfig), "TamkeenLoggingConfig is missing in the configuration.");
                        }

                        Log.Information("TamkeenLogRegister: {TamkeenLogRegister}", tamkeenLogRegister);

                        return tamkeenLogRegister;
                    }
                    catch (ArgumentNullException ex)
                    {
                        Log.Error(ex, "Error registering ITamkeenLogRegister");
                        throw;
                    }
                    catch (InvalidOperationException ex)
                    {
                        Log.Error(ex, "Error resolving dependencies for ITamkeenLogRegister");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Unexpected error registering ITamkeenLogRegister");
                        throw;
                    }
                });

                services.AddSingleton<Serilog.ILogger>(provider =>
                {
                    try
                    {
                        var tamkeenLogRegister = provider.GetRequiredService<ITamkeenLogRegister>();
                        Log.Information("TamkeenLogRegister: {TamkeenLogRegister}", tamkeenLogRegister.Logger);
                        return tamkeenLogRegister.Logger;
                    }
                    catch (InvalidOperationException ex)
                    {
                        Log.Error(ex, "Error resolving Serilog.ILogger");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Unexpected error registering Serilog.ILogger");
                        throw;
                    }
                });
            }
            catch (ArgumentNullException ex)
            {
                Log.Error(ex, "Error during service registration");
                throw;
            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex, "Invalid operation during service registration");
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unexpected error during service registration");
                throw;
            }
            finally
            {
                Log.Information("The register finally end");
            }
            //var tamkeenLoggingConfig = configuration.GetSection(nameof(TamkeenLoggingConfig)).Get<TamkeenLoggingConfig>(); ;
            //TamkeenLogRegister tamkeenLogRegister = new TamkeenLogRegister(tamkeenLoggingConfig);
            //// Register LogRegister as Singleton
            //services.AddSingleton<ITamkeenLogRegister>(provider =>
            //{

            //    return tamkeenLogRegister;
            //});
            //services.AddSingleton(tamkeenLogRegister.Logger);
            #endregion


            #region Health Checks
            var healthCheckOptions = configuration.GetSection(nameof(HealthCheckOptions)).Get<HealthCheckOptions>();
            var healthChecks = services.AddHealthChecks();

            if (healthCheckOptions?.Self != null)
            {
                healthChecks.AddCheck(
                    healthCheckOptions.Self.Name,
                    () => HealthCheckResult.Healthy(),
                    tags: healthCheckOptions.Self.Tags);
            }

            if (healthCheckOptions?.SmscProxyServer != null)
            {
                healthChecks.AddCheck<SmscProxyServerHealthCheck>(
                    healthCheckOptions.SmscProxyServer.Name,
                    tags: healthCheckOptions.SmscProxyServer.Tags);
            }

            if (healthCheckOptions?.Elasticsearch != null)
            {
                healthChecks.AddCheck<ElasticsearchHealthCheck>(
                    healthCheckOptions.Elasticsearch.Name,
                    tags: healthCheckOptions.Elasticsearch.Tags);
            }

            if (healthCheckOptions?.Redis != null)
            {
                healthChecks.AddCheck<RedisHealthCheck>(
                    healthCheckOptions.Redis.Name,
                    tags: healthCheckOptions.Redis.Tags);
            }

            if (healthCheckOptions?.DalUrl != null)
            {
                healthChecks.AddCheck<DalUrlHealthCheck>(
                    healthCheckOptions.DalUrl.Name,
                    tags: healthCheckOptions.DalUrl.Tags);
            }

            if (healthCheckOptions?.SmscService != null)
            {
                healthChecks.AddCheck<SmscServiceHealthCheck>(
                    healthCheckOptions.SmscService.Name,
                    tags: healthCheckOptions.SmscService.Tags);
            }

            var healthChecksUIConfig = configuration.GetSection(nameof(HealthChecksUI)).Get<HealthChecksUI>();

            services.AddHealthChecksUI(setupSettings =>
            {
                if (healthChecksUIConfig?.HealthChecks != null)
                {
                    foreach (var healthCheck in healthChecksUIConfig.HealthChecks)
                    {
                        setupSettings.AddHealthCheckEndpoint(healthCheck.Name, healthCheck.Uri);
                    }
                }
            }).AddInMemoryStorage(); // Using in-memory storage for simplicity

            #endregion

            return services;
        }

        public static IApplicationBuilder UseCustomServices(this IApplicationBuilder app)
        {
            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                // إضافة Health Check على مسار "/health"
                endpoints.MapHealthChecks("/health");
            });

            return app;
        }
        public static void AddProjectControllers(this IServiceCollection services)
        {
            services.AddControllers(options =>
            {
                options.Filters.Add(typeof(ValidateModelStateAttribute));
                options.Filters.Add(new ApiExceptionFilter());
            });

            services.AddCors();
        }
        public static void AddCustomFluentValidation(this IServiceCollection services)
        {
            services.AddFluentValidationAutoValidation().AddFluentValidationClientsideAdapters();
        }
    }
}
