# Tamkeen SMPP Gateway Proxy Service - Test Runner Script
# سكريبت تشغيل الاختبارات لمشروع Tamkeen SMPP Gateway Proxy Service

param(
    [string]$TestType = "all",           # نوع الاختبارات: all, unit, integration, specific
    [string]$TestFilter = "",            # فلتر محدد للاختبارات
    [switch]$Coverage = $false,          # تشغيل مع تقرير التغطية
    [switch]$Verbose = $false,           # تشغيل مع تفاصيل مفصلة
    [switch]$GenerateReport = $false,    # إنشاء تقرير HTML
    [string]$OutputFormat = "console"    # تنسيق الإخراج: console, trx, json
)

# ألوان للإخراج
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-ColoredOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Show-Header {
    Write-ColoredOutput "================================================" $Blue
    Write-ColoredOutput "    Tamkeen SMPP Gateway Proxy Service Tests    " $Blue
    Write-ColoredOutput "================================================" $Blue
    Write-ColoredOutput ""
}

function Show-Usage {
    Write-ColoredOutput "الاستخدام:" $Yellow
    Write-ColoredOutput "  .\run-tests.ps1 [المعاملات]" $Yellow
    Write-ColoredOutput ""
    Write-ColoredOutput "المعاملات:" $Yellow
    Write-ColoredOutput "  -TestType <نوع>     نوع الاختبارات (all, unit, integration, specific)" $Yellow
    Write-ColoredOutput "  -TestFilter <فلتر>  فلتر محدد للاختبارات" $Yellow
    Write-ColoredOutput "  -Coverage           تشغيل مع تقرير التغطية" $Yellow
    Write-ColoredOutput "  -Verbose            تشغيل مع تفاصيل مفصلة" $Yellow
    Write-ColoredOutput "  -GenerateReport     إنشاء تقرير HTML" $Yellow
    Write-ColoredOutput "  -OutputFormat       تنسيق الإخراج (console, trx, json)" $Yellow
    Write-ColoredOutput ""
    Write-ColoredOutput "أمثلة:" $Yellow
    Write-ColoredOutput "  .\run-tests.ps1                                    # تشغيل جميع الاختبارات" $Yellow
    Write-ColoredOutput "  .\run-tests.ps1 -TestType unit                     # اختبارات الوحدة فقط" $Yellow
    Write-ColoredOutput "  .\run-tests.ps1 -Coverage -GenerateReport          # مع تقرير التغطية" $Yellow
    Write-ColoredOutput "  .\run-tests.ps1 -TestFilter SendSmscControllerTests # اختبارات محددة" $Yellow
}

function Test-Prerequisites {
    Write-ColoredOutput "فحص المتطلبات الأساسية..." $Blue
    
    # فحص .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-ColoredOutput "✓ .NET SDK: $dotnetVersion" $Green
    }
    catch {
        Write-ColoredOutput "✗ .NET SDK غير مثبت أو غير متاح في PATH" $Red
        return $false
    }
    
    # فحص وجود مشروع الاختبارات
    if (-not (Test-Path "Tamkeen.SMPP.SmscServerGatewayProxyService.Tests\Tamkeen.SMPP.SmscServerGatewayProxyService.Tests.csproj")) {
        Write-ColoredOutput "✗ مشروع الاختبارات غير موجود" $Red
        return $false
    }
    Write-ColoredOutput "✓ مشروع الاختبارات موجود" $Green
    
    return $true
}

function Restore-Packages {
    Write-ColoredOutput "استعادة حزم NuGet..." $Blue
    try {
        dotnet restore --verbosity quiet
        Write-ColoredOutput "✓ تم استعادة الحزم بنجاح" $Green
    }
    catch {
        Write-ColoredOutput "✗ فشل في استعادة الحزم" $Red
        return $false
    }
    return $true
}

function Build-Solution {
    Write-ColoredOutput "بناء المشروع..." $Blue
    try {
        if ($Verbose) {
            dotnet build --no-restore
        } else {
            dotnet build --no-restore --verbosity quiet
        }
        Write-ColoredOutput "✓ تم بناء المشروع بنجاح" $Green
    }
    catch {
        Write-ColoredOutput "✗ فشل في بناء المشروع" $Red
        return $false
    }
    return $true
}

function Get-TestCommand {
    $command = "dotnet test --no-build"
    
    # إضافة فلتر حسب نوع الاختبار
    switch ($TestType.ToLower()) {
        "unit" {
            $command += " --filter `"FullyQualifiedName!~Integration`""
        }
        "integration" {
            $command += " --filter `"FullyQualifiedName~Integration`""
        }
        "specific" {
            if ($TestFilter) {
                $command += " --filter `"FullyQualifiedName~$TestFilter`""
            }
        }
    }
    
    # إضافة فلتر مخصص إذا تم تحديده
    if ($TestFilter -and $TestType.ToLower() -ne "specific") {
        $command += " --filter `"FullyQualifiedName~$TestFilter`""
    }
    
    # إضافة تجميع بيانات التغطية
    if ($Coverage) {
        $command += " --collect:`"XPlat Code Coverage`""
    }
    
    # إضافة مستوى التفاصيل
    if ($Verbose) {
        $command += " --verbosity detailed"
    } else {
        $command += " --verbosity normal"
    }
    
    # إضافة تنسيق الإخراج
    switch ($OutputFormat.ToLower()) {
        "trx" {
            $command += " --logger `"trx;LogFileName=TestResults.trx`""
        }
        "json" {
            $command += " --logger `"json;LogFileName=TestResults.json`""
        }
    }
    
    return $command
}

function Run-Tests {
    $testCommand = Get-TestCommand
    
    Write-ColoredOutput "تشغيل الاختبارات..." $Blue
    Write-ColoredOutput "الأمر: $testCommand" $Yellow
    Write-ColoredOutput ""
    
    try {
        Invoke-Expression $testCommand
        $testExitCode = $LASTEXITCODE
        
        if ($testExitCode -eq 0) {
            Write-ColoredOutput "✓ تم تشغيل جميع الاختبارات بنجاح" $Green
        } else {
            Write-ColoredOutput "✗ فشل في بعض الاختبارات" $Red
        }
        
        return $testExitCode -eq 0
    }
    catch {
        Write-ColoredOutput "✗ خطأ في تشغيل الاختبارات: $($_.Exception.Message)" $Red
        return $false
    }
}

function Generate-CoverageReport {
    if (-not $Coverage) {
        return
    }
    
    Write-ColoredOutput "إنشاء تقرير التغطية..." $Blue
    
    # فحص وجود أداة reportgenerator
    try {
        $reportGeneratorVersion = dotnet tool list -g | Select-String "dotnet-reportgenerator-globaltool"
        if (-not $reportGeneratorVersion) {
            Write-ColoredOutput "تثبيت أداة reportgenerator..." $Yellow
            dotnet tool install -g dotnet-reportgenerator-globaltool
        }
    }
    catch {
        Write-ColoredOutput "تثبيت أداة reportgenerator..." $Yellow
        dotnet tool install -g dotnet-reportgenerator-globaltool
    }
    
    # البحث عن ملفات التغطية
    $coverageFiles = Get-ChildItem -Path "TestResults" -Filter "coverage.cobertura.xml" -Recurse
    
    if ($coverageFiles.Count -eq 0) {
        Write-ColoredOutput "✗ لم يتم العثور على ملفات التغطية" $Red
        return
    }
    
    $coverageReports = $coverageFiles | ForEach-Object { $_.FullName }
    $reportsParam = $coverageReports -join ";"
    
    try {
        reportgenerator -reports:"$reportsParam" -targetdir:"coveragereport" -reporttypes:Html
        Write-ColoredOutput "✓ تم إنشاء تقرير التغطية في مجلد coveragereport" $Green
        
        if ($GenerateReport) {
            $reportPath = Join-Path (Get-Location) "coveragereport\index.html"
            if (Test-Path $reportPath) {
                Write-ColoredOutput "فتح تقرير التغطية..." $Blue
                Start-Process $reportPath
            }
        }
    }
    catch {
        Write-ColoredOutput "✗ فشل في إنشاء تقرير التغطية: $($_.Exception.Message)" $Red
    }
}

function Show-Summary {
    param([bool]$Success)
    
    Write-ColoredOutput ""
    Write-ColoredOutput "================================================" $Blue
    Write-ColoredOutput "                    الملخص                     " $Blue
    Write-ColoredOutput "================================================" $Blue
    
    if ($Success) {
        Write-ColoredOutput "✓ تم تشغيل الاختبارات بنجاح" $Green
    } else {
        Write-ColoredOutput "✗ فشل في تشغيل الاختبارات" $Red
    }
    
    if ($Coverage) {
        $reportPath = Join-Path (Get-Location) "coveragereport\index.html"
        if (Test-Path $reportPath) {
            Write-ColoredOutput "📊 تقرير التغطية: $reportPath" $Blue
        }
    }
    
    # عرض ملفات النتائج
    if ($OutputFormat -eq "trx" -and (Test-Path "TestResults.trx")) {
        Write-ColoredOutput "📄 نتائج TRX: TestResults.trx" $Blue
    }
    
    if ($OutputFormat -eq "json" -and (Test-Path "TestResults.json")) {
        Write-ColoredOutput "📄 نتائج JSON: TestResults.json" $Blue
    }
    
    Write-ColoredOutput ""
}

# التحقق من طلب المساعدة
if ($args -contains "-h" -or $args -contains "--help" -or $args -contains "help") {
    Show-Header
    Show-Usage
    exit 0
}

# تشغيل السكريبت الرئيسي
Show-Header

if (-not (Test-Prerequisites)) {
    exit 1
}

if (-not (Restore-Packages)) {
    exit 1
}

if (-not (Build-Solution)) {
    exit 1
}

$testSuccess = Run-Tests

if ($Coverage) {
    Generate-CoverageReport
}

Show-Summary -Success $testSuccess

if ($testSuccess) {
    exit 0
} else {
    exit 1
}
