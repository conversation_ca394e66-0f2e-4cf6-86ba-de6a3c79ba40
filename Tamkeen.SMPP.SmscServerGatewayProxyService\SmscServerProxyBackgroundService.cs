﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.ProxyServer;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public class SmscServerProxyBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        public readonly SmscServerProxy _smscServerProxy;
        private readonly ILogger _logger;
        private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private readonly Dictionary<string, ConnectionMode> modeMap = new Dictionary<string, ConnectionMode>
        {
            { "transceiver", ConnectionMode.Transceiver },
            { "receiver", ConnectionMode.Receiver },
            { "transmitter", ConnectionMode.Transmitter }
        };
        public SmscServerProxyBackgroundService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _smscServerProxy = new SmscServerProxy(serviceProvider);
            _logger = serviceProvider.GetService<ILogger>() ?? throw new ArgumentNullException(nameof(_logger));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.Information("Background service started");
                await _smscServerProxy.Run(); // Run SmscServerProxy continuously
                await Task.Delay(TimeSpan.FromSeconds(60), stoppingToken);

                while (!stoppingToken.IsCancellationRequested)
                {

                    // Add timeout for the Run task
                    if (!_smscServerProxy._smppProxyServer.IsRun)
                    {
                        _logger.Information("Starting _smscServerProxy.Run()");
                        var runTask = _smscServerProxy.Run();
                        if (await Task.WhenAny(runTask, Task.Delay(TimeSpan.FromSeconds(30))) == runTask)
                        {
                            _logger.Information("Run completed.");
                        }
                        else
                        {
                            _logger.Information("Run timed out.");
                        }
                    }

                    if (_smscServerProxy._smscServerProxyClient._clientConnectionSettings.enable && !_smscServerProxy._smscServerProxyClient._proxyClient.IsClientConnected)
                    {
                        _logger.Information($"Attempting to connect client: {_smscServerProxy._smscServerProxyClient}");

                        if (_smscServerProxy._smscServerProxyClient._clientConnectionSettings.mode != null && modeMap.ContainsKey(_smscServerProxy._smscServerProxyClient._clientConnectionSettings.mode.ToLowerInvariant()))
                        {
                            string lowercasedMode = _smscServerProxy._smscServerProxyClient._clientConnectionSettings.mode.ToLowerInvariant();
                            _smscServerProxy._smscServerProxyClient._clientConnectionSettings.connectionMode = modeMap[lowercasedMode];
                        }
                        await ClientDisconnect();
                        //await _smscServerProxy._smscServerProxyClient._proxyClient.UnbindAsync();
                        //await _smscServerProxy._smscServerProxyClient._proxyClient.DisconnectAsync();                        
                        // Use Task.Run carefully to avoid blocking the main thread
                        var connectTask = Task.Run(() => _smscServerProxy._smscServerProxyClient.RunAsync());
                            //new Uri(_smscServerProxy._smscServerProxyClient._clientConnectionSettings.clientUri),
                            //_smscServerProxy._smscServerProxyClient._clientConnectionSettings.connectionMode,
                            //_smscServerProxy._smscServerProxyClient._clientConnectionSettings.systemId,
                            //_smscServerProxy._smscServerProxyClient._clientConnectionSettings.password,
                            //_smscServerProxy._smscServerProxyClient ._clientConnectionSettings.connectionRecovery));

                        if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(30))) == connectTask)
                        {
                            _logger.Information($"Client {_smscServerProxy._smscServerProxyClient} connected.");
                        }
                        else
                        {
                            _logger.Information($"Client {_smscServerProxy._smscServerProxyClient} connection attempt timed out.");
                        }
                    }


                    await Task.Delay(TimeSpan.FromSeconds(60), stoppingToken);
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Information($"Error in ExecuteAsync: {ex.Message}");
            }
        }

        //// Stop the background service
        //public void StopService()
        //{
        //    _logger.Information("Stopping Background Smsc Server Proxy Service.", "StopService", _smscServerProxy._smscServerProxyClient._clientConnectionSettings.systemId);
        //    _cancellationTokenSource.Cancel();
        //}

        // Restart the background service
        public async void RestartService()
        {
            _logger.Information("Restarting Background Smsc Server Proxy Service.", "RestartService", _smscServerProxy._smscServerProxyClient._clientConnectionSettings.systemId);
            await ClientDisconnect();
            await _smscServerProxy.StopAsync();
            // Cancel the current task
            _cancellationTokenSource.Cancel();

            // Reset the cancellation token source
            _cancellationTokenSource = new CancellationTokenSource();

            // Start the service again
            _ = ExecuteAsync(_cancellationTokenSource.Token);
        }

        //// Get the last run time
        //public DateTime GetLastRunTime()
        //{
        //    return _lastRunTime.ToLocalTime();
        //}

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.Information("Background Smsc Server Proxy Service is stopping.", "StopAsync", _smscServerProxy._smscServerProxyClient._clientConnectionSettings.systemId);
            await ClientDisconnect();
            await _smscServerProxy.StopAsync();
            await _cancellationTokenSource.CancelAsync();
            //_cancellationTokenSource.Cancel();
            await base.StopAsync(cancellationToken);
        }
        private async Task ClientDisconnect()
        {
            _logger.Information("Disconnect from SMPP server");

            if (_smscServerProxy._smscServerProxyClient._proxyClient.Status == ConnectionStatus.Bound)
            {
                await ClientUnBind();
            }

            if (_smscServerProxy._smscServerProxyClient._proxyClient.Status == ConnectionStatus.Open)
            {
                await _smscServerProxy._smscServerProxyClient._proxyClient.DisconnectAsync();
            }
        }
        private async Task ClientUnBind()
        {
            _logger.Information("Unbind SmppClient");
            UnBindResp resp = await _smscServerProxy._smscServerProxyClient._proxyClient.UnbindAsync();

            switch (resp.Header.Status)
            {
                case CommandStatus.ESME_ROK:
                    _logger.Information("UnBind succeeded: Status: {0}", resp.Header.Status);
                    break;
                default:
                    _logger.Information("UnBind failed: Status: {0}", resp.Header.Status);
                    await _smscServerProxy._smscServerProxyClient._proxyClient.DisconnectAsync();
                    break;
            }

        }
    }
}
