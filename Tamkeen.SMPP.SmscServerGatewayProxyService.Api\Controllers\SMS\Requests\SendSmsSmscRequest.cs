﻿using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.BaseClasses;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Controllers.SMS.Requests
{
    public class SendSmsSmscRequest : BaseRequest
    {
        public string transceiverSystemId { get; set; }
        //public DataCodings coding { get; set(DataCodings)Enum.Parse(typeof(DataCodings), string DataCodings); }
        //UCS2 for Arabic 1
        //Latin1 for English 2
        public DataCodings dataCodings { get; set; } = DataCodings.UCS2;
        public string srcAdr { get; set; } = "Cash";
        public AddressNPI srcNpi { get; set; } = AddressNPI.ISDN;
        public AddressTON srcTon { get; set; } = AddressTON.National;
        //public byte srcTon { get; set(byte.Parse(int srcTon)); }
        //public SmeAddress sourceAddress { get; set(string srcAdr, (AddressTON)byte.Parse(int srcTon), (AddressNPI)byte.Parse(int srcNpi)); }
        public string dstAdr { get; set; }
        public AddressNPI dstNpi { get; set; } = AddressNPI.ISDN;
        public AddressTON dstTon { get; set; } = AddressTON.National;
        /// <summary>
        /// SMSC Delivery Receipt (bits 1 and 0 of registered_delivery)
        /// </summary>
        public SMSCDeliveryReceipt deliveryReceipt { get; set; } = SMSCDeliveryReceipt.NotRequested;
        //public byte dstNpi { get; set(byte.Parse(int dstNpi)); }
        //public SmeAddress destinationAddress { get; set(string dstAdr, (AddressTON)byte.Parse(int dstTon), (AddressNPI)byte.Parse(int dstNpi)); }
        public string smsText { get; set; }
        public SubmitMode submitMode { get; set; } = SubmitMode.ShortMessage;
        //public SubmitMode mode  { get; set(SubmitMode)Enum.Parse(typeof(SubmitMode), string SubmitMode); }
        //ShortMessage 1
        //Payload 2
        //ShortMessageWithSAR 3
        public SmsDstType smsDstType { get; set; } = SmsDstType.local;
        //local 1
        //other 2
    }

}
