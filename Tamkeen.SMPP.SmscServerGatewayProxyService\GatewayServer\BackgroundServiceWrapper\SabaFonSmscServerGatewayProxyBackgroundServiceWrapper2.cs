﻿using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public class SabaFonSmscServerGatewayProxyBackgroundServiceWrapper2 : SmscServerGatewayProxyBackgroundService
    {
        //private readonly SmscServerGatewayProxyBackgroundService _smscService;
        //private bool _isRunning;
        public SabaFonSmscServerGatewayProxyBackgroundServiceWrapper2(IServiceProvider serviceProvider, SmscServerGatewayProxySettings settings) : base(serviceProvider, settings) 
        {
        }
       
        //public string TransceiverSystemId => _smscService.TransceiverSystemId;

        //public async Task RestartServiceAsync(CancellationToken cancellationToken)
        //{
        //   await _smscService.RestartServiceAsync(cancellationToken);
        //}

        //public Task StartAsync(CancellationToken cancellationToken)
        //{
        //    return _smscService.StartAsync(cancellationToken);
        //}

        //public Task StopAsync(CancellationToken cancellationToken)
        //{
        //    _isRunning = false;
        //    return _smscService.StopAsync(cancellationToken);
        //}
        //public string GetTransceiverSystemId()
        //{
        //    return _smscService.TransceiverSystemId; // Assuming TransceiverSystemId is a property in the smscService
        //}
        //public bool IsRunning()
        //{
        //    return _isRunning;
        //}
    }
}
