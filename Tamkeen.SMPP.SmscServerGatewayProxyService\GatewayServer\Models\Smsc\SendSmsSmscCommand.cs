﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc
{
    /// <summary>
    /// Command representing the data required to send an SMS through the SMSC.
    /// Implements the IRequest interface for MediatR.
    /// </summary>
    public class SendSmsSmscCommand
    {
        private SmsDstType smsDstType1;

        public string transceiverSystemId { get; set; }
        //public DataCodings coding { get; set(DataCodings)Enum.Parse(typeof(DataCodings), string DataCodings); }
        //UCS2 for Arabic 1
        //Latin1 for English 2
        public DataCodings dataCodings { get; set; }
        public string srcAdr { get; set; }
        public AddressNPI srcNpi { get; set; }
        public AddressTON srcTon { get; set; }
        //public byte srcTon { get; set(byte.Parse(int srcTon)); }
        //public SmeAddress sourceAddress { get; set(string srcAdr, (AddressTON)byte.Parse(int srcTon), (AddressNPI)byte.Parse(int srcNpi)); }
        public string dstAdr { get; set; }
        public AddressNPI dstNpi { get; set; }
        public AddressTON dstTon { get; set; }
        public SMSCDeliveryReceipt deliveryReceipt { get; set; }

        //public byte dstNpi { get; set(byte.Parse(int dstNpi)); }
        //public SmeAddress destinationAddress { get; set(string dstAdr, (AddressTON)byte.Parse(int dstTon), (AddressNPI)byte.Parse(int dstNpi)); }
        public string smsText { get; set; }
        public SubmitMode submitMode { get; set; }
        //public SubmitMode mode  { get; set(SubmitMode)Enum.Parse(typeof(SubmitMode), string SubmitMode); }
        //ShortMessage 1
        //Payload 2
        //ShortMessageWithSAR 3
        public SmsDstType smsDstType { get => smsDstType1; set => smsDstType1 = value; }
        //local 1
        //other 2
        //public SmscServerProxyClient smscServerProxyClient { get; set; }
    }

}
