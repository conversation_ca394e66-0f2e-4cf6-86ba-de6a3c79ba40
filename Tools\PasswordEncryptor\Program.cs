class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("Password Encryptor Tool");
        Console.Write("Enter password to encrypt: ");
        var password = Console.ReadLine();
        
        Console.Write("Enter encryption key: ");
        var key = Console.ReadLine();
        
        var encryptionService = new EncryptionService(key);
        var encrypted = encryptionService.Encrypt(password);
        
        Console.WriteLine($"Encrypted password: {encrypted}");
        Console.WriteLine($"Add this to your configuration:");
        Console.WriteLine($"PASSWORD_ENCRYPTED={encrypted}");
    }
}