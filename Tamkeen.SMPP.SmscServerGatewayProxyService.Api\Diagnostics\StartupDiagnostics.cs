using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text.Json;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Diagnostics
{
    /// <summary>
    /// أداة تشخيص شاملة لمشاكل بدء التشغيل
    /// </summary>
    public class StartupDiagnostics
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<StartupDiagnostics> _logger;

        public StartupDiagnostics(IConfiguration configuration, ILogger<StartupDiagnostics> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// تشخيص شامل لمشاكل بدء التشغيل
        /// </summary>
        public async Task<DiagnosticResult> RunFullDiagnosticsAsync()
        {
            var result = new DiagnosticResult();
            
            _logger.LogInformation("🔍 Starting comprehensive startup diagnostics...");

            try
            {
                // 1. فحص التكوين الأساسي
                result.ConfigurationCheck = await CheckConfigurationAsync();
                
                // 2. فحص الاتصال بالشبكة
                result.NetworkCheck = await CheckNetworkConnectivityAsync();
                
                // 3. فحص خدمات خارجية
                result.ExternalServicesCheck = await CheckExternalServicesAsync();
                
                // 4. فحص متغيرات البيئة
                result.EnvironmentCheck = CheckEnvironmentVariables();
                
                // 5. فحص الأذونات والملفات
                result.FileSystemCheck = CheckFileSystemAccess();

                result.OverallHealth = DetermineOverallHealth(result);
                
                _logger.LogInformation("✅ Startup diagnostics completed. Overall health: {Health}", 
                    result.OverallHealth);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Startup diagnostics failed: {ErrorMessage}", ex.Message);
                result.OverallHealth = HealthStatus.Critical;
                result.Errors.Add($"Diagnostics failed: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// فحص التكوين الأساسي
        /// </summary>
        private async Task<HealthStatus> CheckConfigurationAsync()
        {
            try
            {
                _logger.LogInformation("🔧 Checking configuration...");

                var issues = new List<string>();

                // فحص Vault configuration
                var vaultAddress = _configuration["Vault:Address"];
                var vaultToken = _configuration["Vault:Token"];
                
                if (string.IsNullOrEmpty(vaultAddress))
                    issues.Add("Vault:Address is missing or empty");
                    
                if (string.IsNullOrEmpty(vaultToken))
                    issues.Add("Vault:Token is missing or empty");

                // فحص SMSC settings
                var smscSettings = _configuration.GetSection("SmscSettings");
                if (!smscSettings.Exists())
                    issues.Add("SmscSettings section is missing");

                // فحص Connection strings
                var defaultConnection = _configuration.GetConnectionString("DefaultConnection");
                if (string.IsNullOrEmpty(defaultConnection))
                    issues.Add("DefaultConnection string is missing");

                // فحص تنسيق القيم
                await CheckConfigurationFormatsAsync(issues);

                if (issues.Count == 0)
                {
                    _logger.LogInformation("✅ Configuration check passed");
                    return HealthStatus.Healthy;
                }
                else
                {
                    _logger.LogWarning("⚠️ Configuration issues found: {Issues}", string.Join(", ", issues));
                    return HealthStatus.Degraded;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Configuration check failed");
                return HealthStatus.Critical;
            }
        }

        /// <summary>
        /// فحص تنسيق قيم التكوين
        /// </summary>
        private async Task CheckConfigurationFormatsAsync(List<string> issues)
        {
            try
            {
                // فحص أرقام المنافذ
                var ports = new[]
                {
                    "SmscSettings:SmscYGsmServerGatewayProxySettings:SmscServerSetting:SmscServerPort",
                    "SmscSettings:SmscYouTransmitterServerGatewayProxySettings:SmscServerSetting:SmscServerPort"
                };

                foreach (var portPath in ports)
                {
                    var portValue = _configuration[portPath];
                    if (!string.IsNullOrEmpty(portValue) && !int.TryParse(portValue, out _))
                    {
                        issues.Add($"Invalid port format: {portPath} = {portValue}");
                    }
                }

                // فحص URLs
                var urls = new[]
                {
                    "Vault:Address",
                    "FcmWhatsappSettings:DalUrl"
                };

                foreach (var urlPath in urls)
                {
                    var urlValue = _configuration[urlPath];
                    if (!string.IsNullOrEmpty(urlValue) && !Uri.TryCreate(urlValue, UriKind.Absolute, out _))
                    {
                        issues.Add($"Invalid URL format: {urlPath} = {urlValue}");
                    }
                }

                // فحص Boolean values
                var booleans = new[]
                {
                    "Vault:IgnoreSslErrors",
                    "SmscSettings:SmscYGsmServerGatewayProxySettings:SmscClientSettings:enable"
                };

                foreach (var boolPath in booleans)
                {
                    var boolValue = _configuration[boolPath];
                    if (!string.IsNullOrEmpty(boolValue) && !bool.TryParse(boolValue, out _))
                    {
                        issues.Add($"Invalid boolean format: {boolPath} = {boolValue}");
                    }
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Configuration format check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// فحص الاتصال بالشبكة
        /// </summary>
        private async Task<HealthStatus> CheckNetworkConnectivityAsync()
        {
            try
            {
                _logger.LogInformation("🌐 Checking network connectivity...");

                var issues = new List<string>();

                // فحص الاتصال بالإنترنت
                if (!await CheckInternetConnectivityAsync())
                    issues.Add("No internet connectivity");

                // فحص DNS resolution
                if (!await CheckDnsResolutionAsync())
                    issues.Add("DNS resolution failed");

                // فحص الاتصال بـ Vault
                var vaultConnectivity = await CheckVaultConnectivityAsync();
                if (!vaultConnectivity)
                    issues.Add("Vault connectivity failed");

                if (issues.Count == 0)
                {
                    _logger.LogInformation("✅ Network connectivity check passed");
                    return HealthStatus.Healthy;
                }
                else
                {
                    _logger.LogWarning("⚠️ Network issues found: {Issues}", string.Join(", ", issues));
                    return HealthStatus.Degraded;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Network connectivity check failed");
                return HealthStatus.Critical;
            }
        }

        /// <summary>
        /// فحص الاتصال بالإنترنت
        /// </summary>
        private async Task<bool> CheckInternetConnectivityAsync()
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync("*******", 5000);
                return reply.Status == IPStatus.Success;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص DNS resolution
        /// </summary>
        private async Task<bool> CheckDnsResolutionAsync()
        {
            try
            {
                var vaultAddress = _configuration["Vault:Address"];
                if (string.IsNullOrEmpty(vaultAddress))
                    return true; // Skip if no Vault configured

                if (Uri.TryCreate(vaultAddress, UriKind.Absolute, out var uri))
                {
                    var addresses = await System.Net.Dns.GetHostAddressesAsync(uri.Host);
                    return addresses.Length > 0;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص الاتصال بـ Vault
        /// </summary>
        private async Task<bool> CheckVaultConnectivityAsync()
        {
            try
            {
                var vaultAddress = _configuration["Vault:Address"];
                if (string.IsNullOrEmpty(vaultAddress))
                    return true; // Skip if no Vault configured

                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);
                
                var response = await httpClient.GetAsync($"{vaultAddress}/v1/sys/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص الخدمات الخارجية
        /// </summary>
        private async Task<HealthStatus> CheckExternalServicesAsync()
        {
            try
            {
                _logger.LogInformation("🔗 Checking external services...");

                var issues = new List<string>();

                // فحص قاعدة البيانات
                var dbCheck = await CheckDatabaseConnectivityAsync();
                if (!dbCheck)
                    issues.Add("Database connectivity failed");

                // فحص Redis
                var redisCheck = await CheckRedisConnectivityAsync();
                if (!redisCheck)
                    issues.Add("Redis connectivity failed");

                // فحص RabbitMQ
                var rabbitCheck = await CheckRabbitMQConnectivityAsync();
                if (!rabbitCheck)
                    issues.Add("RabbitMQ connectivity failed");

                if (issues.Count == 0)
                {
                    _logger.LogInformation("✅ External services check passed");
                    return HealthStatus.Healthy;
                }
                else
                {
                    _logger.LogWarning("⚠️ External service issues: {Issues}", string.Join(", ", issues));
                    return HealthStatus.Degraded;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ External services check failed");
                return HealthStatus.Critical;
            }
        }

        private async Task<bool> CheckDatabaseConnectivityAsync()
        {
            // Implementation for database connectivity check
            return true; // Placeholder
        }

        private async Task<bool> CheckRedisConnectivityAsync()
        {
            // Implementation for Redis connectivity check
            return true; // Placeholder
        }

        private async Task<bool> CheckRabbitMQConnectivityAsync()
        {
            // Implementation for RabbitMQ connectivity check
            return true; // Placeholder
        }

        /// <summary>
        /// فحص متغيرات البيئة
        /// </summary>
        private HealthStatus CheckEnvironmentVariables()
        {
            try
            {
                _logger.LogInformation("🌍 Checking environment variables...");

                var issues = new List<string>();

                var requiredEnvVars = new[]
                {
                    "ASPNETCORE_ENVIRONMENT"
                };

                foreach (var envVar in requiredEnvVars)
                {
                    var value = Environment.GetEnvironmentVariable(envVar);
                    if (string.IsNullOrEmpty(value))
                        issues.Add($"Missing environment variable: {envVar}");
                }

                if (issues.Count == 0)
                {
                    _logger.LogInformation("✅ Environment variables check passed");
                    return HealthStatus.Healthy;
                }
                else
                {
                    _logger.LogWarning("⚠️ Environment variable issues: {Issues}", string.Join(", ", issues));
                    return HealthStatus.Degraded;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Environment variables check failed");
                return HealthStatus.Critical;
            }
        }

        /// <summary>
        /// فحص الوصول لنظام الملفات
        /// </summary>
        private HealthStatus CheckFileSystemAccess()
        {
            try
            {
                _logger.LogInformation("📁 Checking file system access...");

                var issues = new List<string>();

                // فحص مجلدات اللوجات
                var logPaths = new[]
                {
                    "c:/logs/TamkeenSMPPSmscServerProxyService",
                    Path.Combine(Directory.GetCurrentDirectory(), "logs")
                };

                foreach (var logPath in logPaths)
                {
                    try
                    {
                        if (!Directory.Exists(logPath))
                            Directory.CreateDirectory(logPath);
                            
                        // اختبار الكتابة
                        var testFile = Path.Combine(logPath, "test.txt");
                        await File.WriteAllTextAsync(testFile, "test");
                        File.Delete(testFile);
                    }
                    catch
                    {
                        issues.Add($"Cannot access log directory: {logPath}");
                    }
                }

                if (issues.Count == 0)
                {
                    _logger.LogInformation("✅ File system access check passed");
                    return HealthStatus.Healthy;
                }
                else
                {
                    _logger.LogWarning("⚠️ File system issues: {Issues}", string.Join(", ", issues));
                    return HealthStatus.Degraded;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ File system access check failed");
                return HealthStatus.Critical;
            }
        }

        /// <summary>
        /// تحديد الحالة العامة للصحة
        /// </summary>
        private HealthStatus DetermineOverallHealth(DiagnosticResult result)
        {
            var statuses = new[]
            {
                result.ConfigurationCheck,
                result.NetworkCheck,
                result.ExternalServicesCheck,
                result.EnvironmentCheck,
                result.FileSystemCheck
            };

            if (statuses.Any(s => s == HealthStatus.Critical))
                return HealthStatus.Critical;
                
            if (statuses.Any(s => s == HealthStatus.Degraded))
                return HealthStatus.Degraded;
                
            return HealthStatus.Healthy;
        }
    }

    /// <summary>
    /// نتيجة التشخيص
    /// </summary>
    public class DiagnosticResult
    {
        public HealthStatus ConfigurationCheck { get; set; }
        public HealthStatus NetworkCheck { get; set; }
        public HealthStatus ExternalServicesCheck { get; set; }
        public HealthStatus EnvironmentCheck { get; set; }
        public HealthStatus FileSystemCheck { get; set; }
        public HealthStatus OverallHealth { get; set; }
        public List<string> Errors { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// حالة الصحة
    /// </summary>
    public enum HealthStatus
    {
        Healthy,
        Degraded,
        Critical
    }
}
