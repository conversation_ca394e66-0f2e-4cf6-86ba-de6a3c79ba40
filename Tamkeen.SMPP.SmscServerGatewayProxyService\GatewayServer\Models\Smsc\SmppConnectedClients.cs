﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.Common;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.Smsc
{
    public class SmppConnectedClients
    {
        public string? ServerSystemId { get; internal set; }
        public string? BindingMode { get; internal set; }
        public string? ConnectedClientName { get; internal set; }
        public string? RemotEndPoint { get; internal set; }
    }
}
