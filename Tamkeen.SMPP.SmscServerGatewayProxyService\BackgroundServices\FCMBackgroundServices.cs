﻿using Microsoft.Identity.Client;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Shared;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{

    public class FCMBackgroundServices
    {

        FcmWhatsappSettings ApiConfig;

        private readonly ITamkeenLogRegister _logRegister;
        public FCMBackgroundServices(ITamkeenLogRegister logRegister, FcmWhatsappSettings appSettings)
        {
            _logRegister = logRegister;
            ApiConfig = appSettings;
        }

        public async Task<BaseResponse> SendFCM(FCMMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                if (ApiConfig.Mode != "Live")
                {
                    info.body = info.body + "\n" + ApiConfig.Mode;
                }
                _logRegister.LogInfo("Cash_SendFCM", info.LogRef, "FCM-WhatsApp", " ", info, 1, 1);

                string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                if (tkn != null)
                {
                    FirebaseMassage messages = new FirebaseMassage()
                    {
                        message = new message()
                        {
                            token = tkn,

                            data = new data()
                            {
                                title = info.title,
                                body = info.body,

                            }//,
                            //notification = new notification
                            //{
                            //    body = info.body,
                            //    title = info.title,
                            //}
                        }
                    };
                    RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);

                    //RedisCalls redisCalls = new RedisCalls("************", 6379);
                    string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");
                    string r = string.Empty;

                    if (!string.IsNullOrEmpty(token))
                    {
                        r = await this.PostFCMFireBase(messages, token, Project.PMB_Cash);
                        _logRegister.LogInfo("Cash_SendFCM", JsonConvert.SerializeObject(r), info.LogRef, "FCM-WhatsApp", "Api", 2, 1);
                        sendFCMResponse.ResultCode = 1;
                        sendFCMResponse.ResultMessage = r;
                        return sendFCMResponse;

                    }
                    else
                    {
                        // _logger.Information("Cash_SendFCM", "FcmTokenNotFound");
                       
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "FcmTokenNotFound";
                        _logRegister.LogInfo("Cash_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 1, 2);

                        return sendFCMResponse;
                    }

                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    _logRegister.LogInfo("Cash_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 1, 3);


                    return sendFCMResponse;
                }

            }
            catch (TimeoutException ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Cash_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);

                return sendFCMResponse;

            }
            catch (Exception ex)
            {
                

                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Cash_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);

                return sendFCMResponse;
            }
        }


        public async Task<string> GetMobileFCM(GetMobileFCMCommand info)
        {
            BaseResponse GetFCMResponse = new BaseResponse();
            try
            {
                _logRegister.LogInfo("Cash_GetMobileFCM", info.msisdn, "FCM-WhatsApp", "Api", info, 1, 1);

                string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.msisdn }, "", Project.PMB_Cash);
                if (tkn != null)
                {
                 
                    return tkn;
                }
                else
                {

                    return null;
                }
            }

            catch (Exception ex)
            {
                _logRegister.LogInfo("Cash_GetMobileFCMException", "FCM-WhatsApp", info.msisdn, ex.Message, 2, 2);

                return null;
            }

        }

        //FCM​/SendFCMIOS


        public async Task<BaseResponse> SendFCMIOS(FCMMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                if (ApiConfig.Mode != "Live")
                {
                    info.body = info.body + "\n" + ApiConfig.Mode;
                }
                _logRegister.LogInfo("Cash_SendFCMIOS", info.LogRef, "FCM-WhatsApp", "Api", info, 1, 1);

                string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                if (tkn != null)
                {
                    FirebaseMassage messages = new FirebaseMassage()
                    {
                        message = new message()
                        {
                            token = tkn,// "fuGKGJnzJ0qaj5uJiRnTih:APA91bGldlxWZleIPEtoWMgjH4k11e1yCP-p-X-V4dt3W6GPPHfQyWMUx0CP21cWyEN1exfdFQTZ-7OxacV5TV7GZ7wkXtmIs4oRYq1av6LVUQqLtak8uosfBbAiqRHYZ2aHedtdfVPj",

                            data = new data()
                            {
                                title = info.title,
                                body = info.body,
                            },
                            notification = new notification
                            {
                                body = info.body,
                                title = info.title,
                            }
                        }
                    };

                    RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                    string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");
                    string r = string.Empty;

                    if (!string.IsNullOrEmpty(token))
                    {
                        r = await this.PostFCMFireBase(messages, token, Project.PMB_Cash);
                        _logRegister.LogInfo("Cash_SendFCMIOS", JsonConvert.SerializeObject(r), r, " ", 2, 1);
                        sendFCMResponse.ResultCode = 1;
                        sendFCMResponse.ResultMessage = r;
                        _logRegister.LogInfo("Cash_SendFCMIOS", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                        return sendFCMResponse;

                    }
                    else
                    {
                       // _logRegister.LogInfo("Cash_SendFCMIOS", "FCMTokenNotFound", r, " ", 2, 2);
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "FCMTokenNotFound";
                        _logRegister.LogInfo("Cash_SendFCMIOS", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                        return sendFCMResponse;
                    }
                    
                }
                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    _logRegister.LogInfo("Cash_SendFCMIOS", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                    return sendFCMResponse;
                }

            }
            catch (TimeoutException ex)
            {

                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
              //  _logRegister.LogInfo("Cash_SendFCMIOSTimeoutException", info.LogRef, ex.Message, " ", 2, 2);
                _logRegister.LogInfo("Cash_SendFCMIOSTimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                return sendFCMResponse;
            }
            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Cash_SendFCMIOSTimeoutException", info.LogRef, ex.Message, " ", 2, 2);
                return sendFCMResponse;
            }

        }

        public async Task<BaseResponse> SendFCMIOSV2(ExpMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                if (ApiConfig.Mode != "Live")
                {
                    info.body = info.body + "\n" + ApiConfig.Mode;
                }
                _logRegister.LogInfo("Cash_FCMIOSV2", info.LogRef, "FCM-WhatsApp", "", info, 1, 1);

                if (string.IsNullOrEmpty(info.fcmToken))
                {

                    string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                    if (tkn != null)
                    {
                        info.fcmToken = tkn;
                    }
                    else
                    {
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                        return sendFCMResponse;
                    }

                }

                FirebaseMassage messages = new FirebaseMassage()
                {
                    message = new message()
                    {
                        token = info.fcmToken,// "fuGKGJnzJ0qaj5uJiRnTih:APA91bGldlxWZleIPEtoWMgjH4k11e1yCP-p-X-V4dt3W6GPPHfQyWMUx0CP21cWyEN1exfdFQTZ-7OxacV5TV7GZ7wkXtmIs4oRYq1av6LVUQqLtak8uosfBbAiqRHYZ2aHedtdfVPj",

                        data = new data()
                        {
                            title = info.title,
                            body = info.body,
                        },
                        notification = new notification
                        {
                            body = info.body,
                            title = info.title,
                        }
                    }
                };

                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");
                string r = string.Empty;

                if (!string.IsNullOrEmpty(token))
                {
                    r = await this.PostFCMFireBase(messages, token, Project.PMB_Cash);
                    _logRegister.LogInfo("SendFCMIOSV2", JsonConvert.SerializeObject(r), r, " ", 1, 2);
                    sendFCMResponse.ResultCode = 1;
                    sendFCMResponse.ResultMessage = r;
                    return sendFCMResponse;
                }
                else
                {
                  //  _logRegister.LogInfo("Cash_FCMIOSV2", "FCMTokenNotFound", r, " ", 2, 2);
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "FCMTokenNotFound";
                    _logRegister.LogInfo("SendFCMIOSV2", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);

                    return sendFCMResponse;
                }
                            }
            catch (TimeoutException ex)
            {

                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
              //  _logRegister.LogInfo("Cash_SendFCMTimeoutException", info.LogRef, " ", ex.Message, 2, 2);
                _logRegister.LogInfo("SendFCMIOSV2TimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);

                return sendFCMResponse;
            }
            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("SendFCMIOSV2TimeoutException", info.LogRef, " ", ex.Message, 2, 2);
                return sendFCMResponse;
            }

        }



        public async Task<BaseResponse> ExpSendFCM(ExpMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                if (ApiConfig.Mode != "Live")
                {
                    info.body = info.body + "\n" + ApiConfig.Mode;
                }
                _logRegister.LogInfo("ExpSendFCM", info.LogRef, "FCM-WhatsApp", " ", info, 1, 1);

                if (string.IsNullOrEmpty(info.fcmToken))
                {
                    string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "E", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Expatriate);
                    if (tkn != null)
                    {
                        info.fcmToken = tkn;
                        //  info.fcmToken = "eVehZ0OaQ8q1FSykxzdJiV:APA91bH61FeI9L2WaiCtQmnaECKsKRTQLFTaHxiErKIZNemaOnRw9y8cpj8Sp8zSa6DMDW3nn00xzt4asencfpciZMzRmfZApHzYMWrq8N4QDDCL7EATNn28E1xhXjxtsmmhhomSKkxj";
                    }
                    else
                    {
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                        return sendFCMResponse;
                    }
                }
                FirebaseMassage messages = new FirebaseMassage()
                {
                    message = new message()
                    {
                        token = info.fcmToken,

                        data = new data()
                        {
                            title = info.title,
                            body = info.body,
                        }
                    }
                };

                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("ExpatriateFirebase");
                string r = string.Empty;

                if (!string.IsNullOrEmpty(token))
                {
                    r = await this.PostFCMFireBase(messages, token, Project.PMB_Expatriate);
                    _logRegister.LogInfo("Exp_SendFCM", JsonConvert.SerializeObject(r), r, " ", 1, 2);
                    sendFCMResponse.ResultCode = 1;
                    sendFCMResponse.ResultMessage = r;
                    return sendFCMResponse;

                }
                else
                {
                    _logRegister.LogInfo("Exp_SendFCM", JsonConvert.SerializeObject(r), "TokenNotFound", " ", 1, 2);
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                    _logRegister.LogInfo("Exp_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);

                    return sendFCMResponse;

                }


            }
            catch (TimeoutException ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Exp_SendFCMTimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);
                return sendFCMResponse;
            }
            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Exp_SendFCMTimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);
                return sendFCMResponse;
            }
        }

        public async Task<BaseResponse> NewSendFCM(ExpMessageInfoCommand info)
        {

            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {

                if (ApiConfig.Mode != "Live")
                {
                    info.body = info.body + "\n" + "(" + ApiConfig.Mode + ")";
                }
                _logRegister.LogInfo("CashApp_SendFCM", info.LogRef, "FCM-WhatsApp", "Api", info, 1, 1);

                if (string.IsNullOrEmpty(info.fcmToken))
                {

                    string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                    if (tkn != null)
                    {
                        info.fcmToken = tkn;
                    }
                    else
                    {
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                        _logRegister.LogInfo("CashApp_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);

                        return sendFCMResponse;
                    }

                }
                FirebaseMassage messages = new FirebaseMassage()
                {
                    message = new message()
                    {
                        token = info.fcmToken,
                        //notification = new notification
                        //{
                        //    title = info.title,
                        //    body = info.body,
                        //},
                        data = new data()
                        {
                            title = info.title,
                            body = info.body,
                        }
                    }
                };
                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");

                var r = await this.PostFCMFireBase(messages, token, Project.PMB_Cash); //change to PMB CashExp after create project in firebase
                _logRegister.LogInfo("CashApp_SendFCM", info.LogRef, r, " ", 2, 1);
                sendFCMResponse.ResultCode = 1;
                sendFCMResponse.ResultMessage = r;
                return sendFCMResponse;
            }
            catch (TimeoutException ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
               //_logRegister.LogInfo("CashApp_SendFCMTimeoutException", info.LogRef, ex.Message, " ", 2, 2);
                _logRegister.LogInfo("CashApp_SendFCMTimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);
                return sendFCMResponse;
            }
            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                //_logRegister.LogInfo("CashApp_SendFCMTimeoutException", info.LogRef, ex.Message, " ", 2, 2);
                _logRegister.LogInfo("CashApp_SendFCMTimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 3);
                return sendFCMResponse;
            }

        }


        public async Task<BaseResponse> BusSendFCM(ExpMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                if (ApiConfig.Mode != "Live")
                {
                    info.body = info.body + "\n" + "(" + ApiConfig.Mode + ")";
                }
                _logRegister.LogInfo("Bus_SendFCM", info.LogRef, "FCM-WhatsApp", " ", 1, 1);

                if (string.IsNullOrEmpty(info.fcmToken))
                {

                    string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "B", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Business);
                    if (tkn != null)
                    {
                        info.fcmToken = tkn;
                    }
                    else
                    {
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                        _logRegister.LogInfo("Bus_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);
                        return sendFCMResponse;
                    }
                }
                FirebaseMassage messages = new FirebaseMassage()
                {
                    message = new message()
                    {
                        token = info.fcmToken,
                        notification = new notification
                        {
                            title = info.title,
                            body = info.body,
                        },
                        data = new data()
                        {
                            title = info.title,
                            body = info.body,
                        }

                    }
                };

                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("BusinessFirebase");

                var r = await this.PostFCMFireBase(messages, token, Project.PMB_Business);
                _logRegister.LogInfo("Bus_SendFCM", info.LogRef, r, " ", 1, 2);
                sendFCMResponse.ResultCode = 1;
                sendFCMResponse.ResultMessage = r;
                //JObject jObject = JObject.Parse(r);}
                return sendFCMResponse;

            }
            catch (TimeoutException ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Bus_SendFCMTomeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                return sendFCMResponse;
            }
            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Bus_SendFCMTimeoutException", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                return sendFCMResponse;
            }

        }

        public async Task<BaseResponse> RemittanceSendFCM(ExpMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                _logRegister.LogInfo("Remittance_SendFCM", info.LogRef, "FCM-WhatsApp", "Api", info, 1, 1);
                if (string.IsNullOrEmpty(info.fcmToken))
                {

                    string tkn = await this.GetFCMToken(new GetFCMTokenCommand { AppVersion = "C", MSISDN = info.msisdn, LogRef = info.LogRef }, "", Project.PMB_Cash);
                    if (tkn != null)
                    {
                        info.fcmToken = tkn;
                    }
                    else
                    {
                        sendFCMResponse.ResultCode = -1;
                        sendFCMResponse.ResultMessage = "There is no FCM token for this msisdn";
                        _logRegister.LogInfo("Remittance_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                        return sendFCMResponse;
                    }
                }
                FirebaseMassage messages = new FirebaseMassage()
                {
                    message = new message()
                    {
                        token = info.fcmToken,
                        //notification = new notification
                        //{
                        //    title = info.title,
                        //    body = info.body,
                        //},
                        data = new data()
                        {
                            title = info.title,
                            body = info.body,
                        }
                    }
                };
                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");

                var r = await this.PostFCMFireBase(messages, token, Project.PMB_Cash); //change to PMB CashExp after create project in firebase
                _logRegister.LogInfo("Remittance_SendFCM", info.LogRef, r, " ", 1, 2);

                //JObject jObject = JObject.Parse(r);
                sendFCMResponse.ResultCode = 1;
                sendFCMResponse.ResultMessage = r;
                //JObject jObject = JObject.Parse(r);}
                _logRegister.LogInfo("Remittance_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);

                return sendFCMResponse;

            }

            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("Remittance_SendFCM", info.msisdn, JsonConvert.SerializeObject(sendFCMResponse), "", 2, 2);
                return sendFCMResponse;
            }

        }



        public async Task<BaseResponse> RemittanceConditionSendFCM(ConditionMessageInfoCommand info)
        {
            BaseResponse sendFCMResponse = new BaseResponse();
            try
            {
                FirebaseMessageConditions messagesifo = new FirebaseMessageConditions();

                _logRegister.LogInfo("RemittanceConditionSendFCM", info.LogRef, "RemittanceConditionSendFCM", "Api", info, 1, 1);
                if (info.Condition != null && info.Condition == "com.tamkeen.sms")
                {


                    messagesifo = new FirebaseMessageConditions()
                    {
                        message = new messageRem
                        {

                            data = new data()
                            {
                                title = info.title,
                                body = info.body,

                            },
                            topic = info.Condition// $"('{info.Condition}' in topics)",
                                                  // content_available=true

                        }

                    };
                }
                else if (info.Condition != null && info.Condition == "com.tamkeen.ios")
                {
                    messagesifo = new FirebaseMessageConditions()
                    {
                        message = new messageRem
                        {
                            notification = new notification
                            {
                                title = info.title,
                                body = info.body,
                            },
                            data = new data()
                            {
                                title = info.title,
                                body = info.body,

                            },
                            topic = info.Condition// $"('{info.Condition}' in topics)",
                                                  // content_available=true

                        }

                    };
                }

                else
                {
                    sendFCMResponse.ResultCode = -1;
                    sendFCMResponse.ResultMessage = "Condition info is wrong";
                    return sendFCMResponse;
                }

                RedisCalls redisCalls = new RedisCalls(ApiConfig.RedisHost, ApiConfig.RedisPort);
                string token = await redisCalls.RetrieveFromRedis<string>("CashAppFirebase");

                var r = await this.PostFCMFireBase(messagesifo, token, Project.PMB_Cash); //change to PMB CashExp after create project in firebase
                _logRegister.LogInfo("RemittanceConditionSendFCM", info.LogRef, r, " ", 1, 2);
                sendFCMResponse.ResultCode = 1;
                sendFCMResponse.ResultMessage = r;

                return sendFCMResponse;

            }
            catch (TimeoutException ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("RemittanceConditionSendFCMTimeoutException", info.LogRef, ex.Message, " ", 1, 2);

                return sendFCMResponse;
            }
            catch (Exception ex)
            {
                sendFCMResponse.ResultCode = -1;
                sendFCMResponse.ResultMessage = ex.Message;
                _logRegister.LogInfo("RemittanceConditionSendFCMTimeoutException", info.LogRef, ex.Message, " ", 1, 2);

                return sendFCMResponse;
            }
        }
        public async Task<string> PostFCMFireBase<T>(T req, string token, Project project)
        {
            RestRequest restRequest = new RestRequest();
            RestClient restClient = new RestClient();
            if (project == Project.PMB_Cash)
            {
                restClient = new RestClient("https://fcm.googleapis.com/v1/projects/cash-3a041/messages:send");
                restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/cash-3a041/messages:send", Method.Post);
                restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            }
            else if (project == Project.PMB_Business)
            {
                restClient = new RestClient("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send");
                restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send", Method.Post);
                restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            }
            else if (project == Project.PMB_Expatriate)
            {
                restClient = new RestClient("https://fcm.googleapis.com/v1/projects/expatriate-be128/messages:send");
                restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/expatriate-be128/messages:send", Method.Post);
                restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            }
            //else if (project == Project.PMB_CashIOS)
            //{
            //    restClient = new RestClient("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send");
            //    restRequest = new RestRequest("https://fcm.googleapis.com/v1/projects/new-app-4b629/messages:send", Method.POST);
            //    restRequest.AddHeader("Authorization", "Bearer " + token.Trim('"'));
            //}

            restRequest.AddHeader("Content-Type", "application/json");

            restRequest.AddBody(req);
            restRequest.RequestFormat = DataFormat.Json;

            var response = await restClient.ExecuteAsync(restRequest,Method.Post);
            string re = response.Content;
            return re;
        }

        public async Task<string> GetFCMToken(GetFCMTokenCommand req, string token, Project project)
        {
            try
            {
                // Configure RestClient to bypass SSL certificate validation
                var options = new RestClientOptions(ApiConfig.DalUrl + "/api/ExpatriateApp/GetFcm")
                {
                    RemoteCertificateValidationCallback = (sender, cert, chain, sslPolicyErrors) => true // Ignore SSL errors
                };

                var restClient = new RestClient(options);
                var restRequest = new RestRequest
                {
                    Method = Method.Post // Use Method enum for the HTTP method
                };

                restRequest.AddHeader("Content-Type", "application/json");
                restRequest.AddJsonBody(req);

                var response = await restClient.ExecuteAsync(restRequest);

                if (!response.IsSuccessful)
                {
                    _logRegister.LogInfo("GetFcmTimeoutException", null, response.ErrorMessage, " ", 1, 2);
                    return null;
                }

                var res = JsonConvert.DeserializeObject<GetFCMRespons>(response.Content);
                return res?.FCM;
            }
            catch (Exception e)
            {
                _logRegister.LogInfo("GetFcmException", null, e.Message, " ", 2, 2);
                return null;
            }
        }


        public enum Project
        {
            PMB_Business = 1,
            PMB_Expatriate = 2,
            PMB_Cash = 3,
            PMB_CashIOS = 4
        }


    }

}
