﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck
{
    public class DalUrlHealthCheck : IHealthCheck
    {
        private readonly string _host;
        private readonly int _port;

        public DalUrlHealthCheck(IConfiguration configuration)
        {
            var dalUrl = configuration.GetSection("FcmWhatsappSettings")["DalUrl"];

            var uri = new Uri(dalUrl);
            _host = uri.Host;
            _port = uri.Port;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                using (var tcpClient = new TcpClient())
                {
                    var connectTask = tcpClient.ConnectAsync(_host, _port);
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(2), cancellationToken);

                    var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                    if (completedTask == connectTask && tcpClient.Connected)
                    {
                        return HealthCheckResult.Healthy($"Successfully connected to DalUrl {_host}:{_port}");
                    }
                    else
                    {
                        return HealthCheckResult.Unhealthy($"Failed to connect to DalUrl {_host}:{_port}");
                    }
                }
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy($"Exception while connecting to {_host}:{_port} - {ex.Message}");
            }
        }
    }
}
