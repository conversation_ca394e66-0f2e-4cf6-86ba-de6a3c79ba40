﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.Json;
using Tamkeen.Inetlab.SMPP.Builders;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Logging;


namespace Tamkeen.SMPP.SmscServerGatewayProxyServiceTestLiveProduction
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            IConfiguration config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
                //.AddJsonFile("appsettings.Production.json", optional: true, reloadOnChange: true)
                .Build();

            var smscSettings = config.GetSection("SmscSettings").GetChildren();
            Dictionary<string, string> testNumbers = new()
            {
                { "YemenMobile", "967775333203" },
                { "YGsm", "967700990621" },
                { "YouTrans", "967737333203" },
                { "SabaFonTrans", "967711336800" }
            };

            List<Task> tasks = new();
            foreach (var smsc in smscSettings)
            {
                var clientSettings = smsc.GetSection("SmscClientSettings");

                bool enable = clientSettings.GetValue<bool>("enable");
                if (!enable) continue;

                string systemId = clientSettings.GetValue<string>("transceiverSystemId");
                string password = clientSettings.GetValue<string>("password");
                string mode = clientSettings.GetValue<string>("mode");
                string clientUri = clientSettings.GetValue<string>("clientUri");
                string transceiverSystemId = clientSettings.GetValue<string>("transceiverSystemId");

                string testNumber = testNumbers.ContainsKey(transceiverSystemId) ? testNumbers[transceiverSystemId] : null;
                if (string.IsNullOrEmpty(testNumber))
                {
                    Console.WriteLine($"No test number configured for {transceiverSystemId}, skipping...");
                    continue;
                }

                SmscClient smscClient = new(systemId, password, mode, clientUri);
                Console.WriteLine($"Connecting to {transceiverSystemId}...");

                if (await smscClient.ConnectAsync())
                {
                    tasks.Add(smscClient.SendTestMessageAsync(transceiverSystemId, testNumber));
                }
                else
                {
                    Console.WriteLine($"Failed to connect to {transceiverSystemId}");
                }
            }
            OldSmscClient oldSmscClient = new OldSmscClient();
            Uri url = new Uri("tcp://*************:29000");
            await oldSmscClient._client.RetryUntilConnectedAsync(url.Host, url.Port, TimeSpan.FromSeconds(5));
            BindResp bindResp = await oldSmscClient._client.BindAsync("YemenMobile", "Ym2021", ConnectionMode.Transceiver);
            switch (bindResp.Header.Status)
            {
                case CommandStatus.ESME_ROK:
                    oldSmscClient._client.Logger.Info("Bind succeeded: Status: {0}, SystemId: {1}", bindResp.Header.Status, bindResp.SystemId);


                    break;
                default:
                    oldSmscClient._client.Logger.Warn("Bind failed: Status: {0}", bindResp.Header.Status);

                    break;
            }
            var sourceAddress = new SmeAddress("Cash", AddressTON.Alphanumeric, AddressNPI.Unknown);
            var destinationAddress = new SmeAddress("967737333203", AddressTON.International, AddressNPI.ISDN);
            ISubmitSmBuilder builder = SMS.ForSubmit()
              .From(sourceAddress)
              .To(destinationAddress)
              .Coding(DataCodings.UCS2)
              .Text("Test from YemenMobile")
              //Or you can set data 
              //.Data(HexStringToByteArray("024A3A6949A59194813D988151A004004D215D2690568698A22820C49A4106288A126A8A22C2A820C22820C2A82342AC30820C4984106288A12628A22C2A420800"))

              //Apply italic style for all text  (mobile device should support basic EMS)
              //.Set(delegate(SubmitSm sm)
              //         {
              //             sm.UserDataPdu.Headers.Add(
              //                 InformationElementIdentifiers.TextFormatting, new byte[] {0, 1, ToByte("00100011")});
              //         })

              // Set ValidityPeriod expired in 2 days
              .ExpireIn(TimeSpan.FromMinutes(300));

            //Request delivery receipt
            builder.DeliveryReceipt();
            //Add custom TLV parameter
            //.AddParameter(0x1403, "free")

            //byte[] bytesarr = Encoding.UTF8.GetBytes("967012175");

            //ushort.TryParse("0x2603", NumberStyles.HexNumber, CultureInfo.InvariantCulture, out ushort tagAsUshort);
            //TLV tlv = new TLV(0x2603, bytesarr);// as they advice for 0x2603
            //builder.AddParameter(tlv);
            try
            {
                IList<SubmitSmResp> resp = await oldSmscClient._client.SubmitAsync(builder);
                //IList<SubmitSmResp> resp = null;

                if (resp.All(x => x.Header.Status == CommandStatus.ESME_ROK))
                {
                    oldSmscClient._client.Logger.Info("Submit succeeded. MessageIds: {0}", string.Join(",", resp.Select(x => x.MessageId)));
                }
                else
                {
                    oldSmscClient._client.Logger.Warn("Submit failed. Status: {0}", string.Join(",", resp.Select(x => x.Header.Status.ToString())));
                }
            }
            catch (Exception ex)
            {
                oldSmscClient._client.Logger.Error("Submit failed. Error: {0}", ex.Message);
                await Task.WhenAll(tasks);
                Console.WriteLine("All test messages sent.");
            }
        }
    }
}
