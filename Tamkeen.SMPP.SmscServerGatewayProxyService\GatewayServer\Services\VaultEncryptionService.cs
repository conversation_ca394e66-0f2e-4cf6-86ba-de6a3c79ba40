﻿using System.Text;
using Microsoft.Extensions.Configuration;
using VaultSharp;
using VaultSharp.V1.SecretsEngines.Transit;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Services
{
    /// <summary>
    /// واجهة خدمة التشفير عبر Vault.
    /// </summary>
    public interface IVaultEncryptionService : IEncryptionService
    {
        Task<string> EncryptWithVaultAsync(string plainText, string keyName = null);
        Task<string> DecryptWithVaultAsync(string cipherText, string keyName = null);
    }

    /// <summary>
    /// تنفيذ التشفير/فك التشفير باستخدام Transit Engine.
    /// </summary>
    public sealed class VaultEncryptionService : IVaultEncryptionService
    {
        private readonly IVaultClient _vaultClient;
        private readonly string _defaultKeyName;

        public VaultEncryptionService(IVaultClient vaultClient, IConfiguration configuration)
        {
            // فحص القيم الممرَّرة
            _vaultClient = vaultClient ?? throw new ArgumentNullException(nameof(vaultClient));
            if (configuration is null)
                throw new ArgumentNullException(nameof(configuration));

            // اسم المفتاح الافتراضي
            _defaultKeyName = configuration["Encryption:KeyName"] ?? "tamkeen-encryption-key";
        }

        #region IEncryptionService (متزامن)
        public string Encrypt(string plainText) =>
            EncryptWithVaultAsync(plainText).GetAwaiter().GetResult();

        public string Decrypt(string cipherText) =>
            DecryptWithVaultAsync(cipherText).GetAwaiter().GetResult();
        #endregion

        /// <summary>
        /// تشفير نص باستخدام Vault Transit.
        /// </summary>
        public async Task<string> EncryptWithVaultAsync(string plainText, string keyName = null)
        {
            if (string.IsNullOrWhiteSpace(plainText))
                throw new ArgumentException("النص المراد تشفيره لا يجوز أن يكون فارغاً.", nameof(plainText));

            keyName ??= _defaultKeyName;

            var request = new EncryptRequestOptions
            {
                // الخاصية الصحيحة في VaultSharp
                Base64EncodedPlainText = Convert.ToBase64String(Encoding.UTF8.GetBytes(plainText))
            };

            var response = await _vaultClient.V1.Secrets.Transit
                                              .EncryptAsync(keyName, request)
                                              .ConfigureAwait(false);

            return response?.Data?.CipherText
                   ?? throw new InvalidOperationException("Vault أعاد قيمة CipherText فارغة.");
        }

        /// <summary>
        /// فك تشفير نص باستخدام Vault Transit.
        /// </summary>
        public async Task<string> DecryptWithVaultAsync(string cipherText, string keyName = null)
        {
            if (string.IsNullOrWhiteSpace(cipherText))
                throw new ArgumentException("النص المشفَّر لا يجوز أن يكون فارغاً.", nameof(cipherText));

            keyName ??= _defaultKeyName;

            var request = new DecryptRequestOptions
            {
                CipherText = cipherText
            };

            var response = await _vaultClient.V1.Secrets.Transit
                                              .DecryptAsync(keyName, request)
                                              .ConfigureAwait(false);

            var encoded = response?.Data?.Base64EncodedPlainText
                         ?? throw new InvalidOperationException("Vault أعاد نصاً مفكوكاً فارغاً.");

            return Encoding.UTF8.GetString(Convert.FromBase64String(encoded));
        }
    }
}
