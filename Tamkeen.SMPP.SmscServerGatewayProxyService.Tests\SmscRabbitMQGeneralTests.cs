using Serilog;
using System;
using System.Threading.Tasks;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ;

public class SmscRabbitMQGeneralTests
{
    [Fact]
    public async Task EnsureChannelIsOpen_ReturnsFalse_ForInvalidConnection()
    {
        var settings = new RabbitMQSettings
        {
            hostName = "invalid-host",
            userName = "user",
            password = "pass",
            port = 5672
        };
        ILogger logger = new LoggerConfiguration().CreateLogger();
        var general = new SmscRabbitMQGeneral(settings, logger);

        await general.InitializeAsync();
        var result = await general.EnsureChannelIsOpen();

        Assert.False(result);
    }

    [Fact]
    public async Task EnsureChannelIsOpen_ThrowsAfterDispose()
    {
        var settings = new RabbitMQSettings
        {
            hostName = "invalid-host",
            userName = "user",
            password = "pass",
            port = 5672
        };
        ILogger logger = new LoggerConfiguration().CreateLogger();
        var general = new SmscRabbitMQGeneral(settings, logger);
        general.Dispose();

        await Assert.ThrowsAsync<ObjectDisposedException>(() => general.EnsureChannelIsOpen());
    }
}
