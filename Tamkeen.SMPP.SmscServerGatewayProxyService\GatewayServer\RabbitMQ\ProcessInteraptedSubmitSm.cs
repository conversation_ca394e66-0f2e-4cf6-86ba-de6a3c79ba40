﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP.PDU;
using Tamkeen.Inetlab.SMPP;
using Newtonsoft.Json;
using RabbitMQ.Client;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.RabbitMQ
{
    /// <summary>
    /// Handles the processing of interrupted SubmitSm operations.
    /// This class is responsible for handling scenarios where a SubmitSm operation was interrupted.
    /// </summary>
    public class ProcessInteraptedSubmitSm
    {
        private readonly IServiceProvider _serviceProvider;
        public SmscRabbitMQSendSmsServiceOld _smscRabbitMQSendSmsService;
        /// <summary>
        /// Initializes a new instance of the <see cref="ProcessInteraptedSubmitSm"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider to resolve dependencies.</param>
        public ProcessInteraptedSubmitSm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Asynchronously processes an interrupted SubmitSm operation.
        /// </summary>
        /// <param name="submitSm">The SubmitSm PDU that was interrupted.</param>
        /// <param name="serverClient">The SMPP server client associated with the operation.</param>
        public async Task ProcessInteraptedSubmitSmAsync(SubmitSm submitSm, SmppServerClient serverClient)
        {
            var smscRabbitMQSaveSMSCommand = CreateSmscRabbitMQSaveSMSCommand(submitSm, serverClient);
            var message = JsonConvert.SerializeObject(smscRabbitMQSaveSMSCommand);
            var body = Encoding.UTF8.GetBytes(message);

            // Ensure the channel is open before publishing the message
            await _smscRabbitMQSendSmsService.ReinitializeChannelIfNeeded();

            // Attach timestamp to the message so we can evaluate its age later
            var properties = _smscRabbitMQSendSmsService._channel.CreateBasicProperties();
            properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());

            // Publish message to RabbitMQ with timestamp
            _smscRabbitMQSendSmsService._channel.BasicPublish(
                exchange: "",
                routingKey: _smscRabbitMQSendSmsService._queueName,
                mandatory: false,
                basicProperties: properties,
                body: body);
        }

        /// <summary>
        /// Creates a command to save the SMS message to RabbitMQ.
        /// </summary>
        /// <param name="submitSm">The SubmitSm PDU containing the SMS message.</param>
        /// <param name="serverClient">The SMPP server client associated with the operation.</param>
        /// <returns>A command object to save the SMS message to RabbitMQ.</returns>
        private SmscRabbitMQSaveSMSCommand CreateSmscRabbitMQSaveSMSCommand(SubmitSm submitSm, SmppServerClient serverClient)
        {
            var smscRabbitMQSaveSMSCommand = new SmscRabbitMQSaveSMSCommand()
            {
                DataCodings = submitSm.DataCoding.ToString(),
                dstAdr = submitSm.DestinationAddress.Address,
                dstNpi = ((byte)submitSm.DestinationAddress.NPI).ToString(),
                dstTon = ((byte)submitSm.DestinationAddress.TON).ToString(),
                smsDstType = SmsDstType.local,
                smsText = submitSm.GetMessageText(serverClient.EncodingMapper),
                srcAdr = submitSm.SourceAddress.Address,
                srcNpi = ((byte)submitSm.SourceAddress.NPI).ToString(),
                srcTon = ((byte)submitSm.SourceAddress.TON).ToString(),
                transceiverSystemId = serverClient.SystemID
            };

            if (submitSm.Parameters.Count > 0)
            {
                if (submitSm.Parameters[0].ToString().Contains("MessagePayloadParameter"))
                {
                    smscRabbitMQSaveSMSCommand.SubmitMode = 2;
                }
                else
                {
                    smscRabbitMQSaveSMSCommand.SubmitMode = 3;
                }
            }

            return smscRabbitMQSaveSMSCommand;
        }
    }
}
