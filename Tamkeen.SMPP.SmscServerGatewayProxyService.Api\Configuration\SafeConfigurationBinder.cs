using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Globalization;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.Configuration
{
    /// <summary>
    /// مربط تكوين آمن يتعامل مع الأخطاء بشكل أفضل
    /// </summary>
    public static class SafeConfigurationBinder
    {
        /// <summary>
        /// ربط آمن للتكوين مع معالجة الأخطاء
        /// </summary>
        public static T SafeBind<T>(this IConfiguration configuration, string sectionName, ILogger logger = null) where T : new()
        {
            try
            {
                var instance = new T();
                var section = configuration.GetSection(sectionName);
                
                if (!section.Exists())
                {
                    logger?.LogWarning("Configuration section '{SectionName}' not found, using default values", sectionName);
                    return instance;
                }

                // ربط آمن مع معالجة الأخطاء
                SafeBindInternal(section, instance, logger, sectionName);
                
                logger?.LogInformation("Successfully bound configuration section '{SectionName}'", sectionName);
                return instance;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Failed to bind configuration section '{SectionName}', using default values", sectionName);
                return new T();
            }
        }

        /// <summary>
        /// الحصول على قيمة آمنة من التكوين
        /// </summary>
        public static T SafeGetValue<T>(this IConfiguration configuration, string key, T defaultValue = default, ILogger logger = null)
        {
            try
            {
                var value = configuration[key];
                
                if (string.IsNullOrEmpty(value))
                {
                    logger?.LogDebug("Configuration key '{Key}' not found or empty, using default value: {DefaultValue}", key, defaultValue);
                    return defaultValue;
                }

                // محاولة تحويل القيمة
                var convertedValue = ConvertValue<T>(value);
                logger?.LogDebug("Successfully retrieved configuration value '{Key}': {Value}", key, convertedValue);
                return convertedValue;
            }
            catch (Exception ex)
            {
                logger?.LogWarning(ex, "Failed to get configuration value '{Key}', using default: {DefaultValue}", key, defaultValue);
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على connection string آمن
        /// </summary>
        public static string SafeGetConnectionString(this IConfiguration configuration, string name, string defaultValue = "", ILogger logger = null)
        {
            try
            {
                var connectionString = configuration.GetConnectionString(name);
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    logger?.LogWarning("Connection string '{Name}' not found, using default", name);
                    return defaultValue;
                }

                // فحص صحة connection string
                if (IsValidConnectionString(connectionString))
                {
                    logger?.LogInformation("Successfully retrieved connection string '{Name}'", name);
                    return connectionString;
                }
                else
                {
                    logger?.LogWarning("Invalid connection string format for '{Name}', using default", name);
                    return defaultValue;
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Failed to get connection string '{Name}', using default", name);
                return defaultValue;
            }
        }

        /// <summary>
        /// ربط داخلي آمن
        /// </summary>
        private static void SafeBindInternal(IConfigurationSection section, object instance, ILogger logger, string sectionName)
        {
            var type = instance.GetType();
            var properties = type.GetProperties();

            foreach (var property in properties)
            {
                try
                {
                    if (!property.CanWrite)
                        continue;

                    var key = property.Name;
                    var configValue = section[key];

                    if (string.IsNullOrEmpty(configValue))
                    {
                        // البحث في الأقسام الفرعية
                        var subSection = section.GetSection(key);
                        if (subSection.Exists() && property.PropertyType.IsClass && property.PropertyType != typeof(string))
                        {
                            var subInstance = Activator.CreateInstance(property.PropertyType);
                            SafeBindInternal(subSection, subInstance, logger, $"{sectionName}:{key}");
                            property.SetValue(instance, subInstance);
                        }
                        continue;
                    }

                    // تحويل وتعيين القيمة
                    var convertedValue = ConvertValue(configValue, property.PropertyType);
                    property.SetValue(instance, convertedValue);
                    
                    logger?.LogDebug("Bound property '{SectionName}:{PropertyName}' = '{Value}'", 
                        sectionName, property.Name, configValue);
                }
                catch (Exception ex)
                {
                    logger?.LogWarning(ex, "Failed to bind property '{SectionName}:{PropertyName}', skipping", 
                        sectionName, property.Name);
                }
            }
        }

        /// <summary>
        /// تحويل آمن للقيم
        /// </summary>
        private static T ConvertValue<T>(string value)
        {
            return (T)ConvertValue(value, typeof(T));
        }

        /// <summary>
        /// تحويل آمن للقيم مع نوع محدد
        /// </summary>
        private static object ConvertValue(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value))
                return GetDefaultValue(targetType);

            try
            {
                // معالجة الأنواع الخاصة
                if (targetType == typeof(string))
                    return value;

                if (targetType == typeof(bool) || targetType == typeof(bool?))
                {
                    return ParseBoolean(value);
                }

                if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    return int.Parse(value, CultureInfo.InvariantCulture);
                }

                if (targetType == typeof(long) || targetType == typeof(long?))
                {
                    return long.Parse(value, CultureInfo.InvariantCulture);
                }

                if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    return double.Parse(value, CultureInfo.InvariantCulture);
                }

                if (targetType == typeof(decimal) || targetType == typeof(decimal?))
                {
                    return decimal.Parse(value, CultureInfo.InvariantCulture);
                }

                if (targetType == typeof(DateTime) || targetType == typeof(DateTime?))
                {
                    return DateTime.Parse(value, CultureInfo.InvariantCulture);
                }

                if (targetType == typeof(TimeSpan) || targetType == typeof(TimeSpan?))
                {
                    return TimeSpan.Parse(value, CultureInfo.InvariantCulture);
                }

                if (targetType == typeof(Uri))
                {
                    return new Uri(value);
                }

                if (targetType.IsEnum)
                {
                    return Enum.Parse(targetType, value, true);
                }

                // استخدام TypeConverter كحل أخير
                var converter = TypeDescriptor.GetConverter(targetType);
                if (converter.CanConvertFrom(typeof(string)))
                {
                    return converter.ConvertFromString(value);
                }

                throw new NotSupportedException($"Cannot convert '{value}' to type '{targetType.Name}'");
            }
            catch (Exception ex)
            {
                throw new FormatException($"Failed to convert '{value}' to type '{targetType.Name}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحليل آمن للقيم المنطقية
        /// </summary>
        private static bool ParseBoolean(string value)
        {
            var normalizedValue = value.Trim().ToLowerInvariant();
            
            return normalizedValue switch
            {
                "true" or "1" or "yes" or "on" or "enabled" => true,
                "false" or "0" or "no" or "off" or "disabled" => false,
                _ => throw new FormatException($"Cannot parse '{value}' as boolean")
            };
        }

        /// <summary>
        /// الحصول على القيمة الافتراضية للنوع
        /// </summary>
        private static object GetDefaultValue(Type type)
        {
            if (type.IsValueType)
                return Activator.CreateInstance(type);
            return null;
        }

        /// <summary>
        /// فحص صحة connection string
        /// </summary>
        private static bool IsValidConnectionString(string connectionString)
        {
            try
            {
                // فحص أساسي لوجود مكونات connection string
                return !string.IsNullOrWhiteSpace(connectionString) && 
                       (connectionString.Contains("Server=") || 
                        connectionString.Contains("Data Source=") ||
                        connectionString.Contains("Host=") ||
                        connectionString.Contains("localhost") ||
                        connectionString.Contains("127.0.0.1"));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة التكوين
        /// </summary>
        public static ConfigurationValidationResult ValidateConfiguration(IConfiguration configuration, ILogger logger = null)
        {
            var result = new ConfigurationValidationResult();
            
            try
            {
                logger?.LogInformation("Starting configuration validation...");

                // فحص الأقسام المطلوبة
                var requiredSections = new[]
                {
                    "SmscSettings",
                    "FcmWhatsappSettings", 
                    "TamkeenLoggingConfig"
                };

                foreach (var section in requiredSections)
                {
                    if (!configuration.GetSection(section).Exists())
                    {
                        result.Errors.Add($"Required section '{section}' is missing");
                    }
                }

                // فحص القيم المطلوبة
                var requiredValues = new Dictionary<string, Type>
                {
                    { "Vault:Address", typeof(string) },
                    { "Vault:Token", typeof(string) },
                    { "FcmWhatsappSettings:DalUrl", typeof(string) }
                };

                foreach (var kvp in requiredValues)
                {
                    try
                    {
                        var value = configuration[kvp.Key];
                        if (string.IsNullOrEmpty(value))
                        {
                            result.Warnings.Add($"Required value '{kvp.Key}' is missing or empty");
                        }
                        else
                        {
                            // محاولة تحويل القيمة للتأكد من صحتها
                            ConvertValue(value, kvp.Value);
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Invalid format for '{kvp.Key}': {ex.Message}");
                    }
                }

                result.IsValid = result.Errors.Count == 0;
                
                logger?.LogInformation("Configuration validation completed. Valid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}",
                    result.IsValid, result.Errors.Count, result.Warnings.Count);

                return result;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Configuration validation failed");
                result.Errors.Add($"Validation failed: {ex.Message}");
                result.IsValid = false;
                return result;
            }
        }
    }

    /// <summary>
    /// نتيجة التحقق من صحة التكوين
    /// </summary>
    public class ConfigurationValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public override string ToString()
        {
            var status = IsValid ? "✅ VALID" : "❌ INVALID";
            return $"Configuration Validation: {status}\n" +
                   $"Errors: {Errors.Count}\n" +
                   $"Warnings: {Warnings.Count}";
        }
    }
}
