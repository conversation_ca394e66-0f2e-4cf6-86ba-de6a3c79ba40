﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Client;
using RestSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Monitor.Core.TmakeenLogsWithElastick;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.FCM;
using Tamkeen.SMPP.SmscServerGatewayProxyService.GatewayServer.Models.WhatsApp;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService
{
    public class WhatsAppBackgroundServices
    {
        FcmWhatsappSettings ApiConfig;
        private readonly ITamkeenLogRegister _logRegister;
        public WhatsAppBackgroundServices(ITamkeenLogRegister logRegister, FcmWhatsappSettings appSettings)
        {
            _logRegister = logRegister;
            ApiConfig = appSettings;
        }

        
        public async Task<BaseResponse> SendToWhatsApp(SendToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("Exp_SendToWhatsApp", req.To, "Fcm-Whatsapp", req.To, 1, 1);

                var cloudApiTemplateRequest = new CloudApiTemplateRequest()
                {
                    to = req.To,
                    template = new Template()
                    {
                        name = "cash_help",
                        language = new Language()
                        {
                            code = "en"
                        },
                        components = new List<Components>()
                    {
                        new Components()
                        {
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                    text=req.Message
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberID}/messages";
                // " https://graph.facebook.com/v20.0/147248695141121/messages"
                //"https://graph.facebook.com/v20.0/110898085304010/messages"
                // url = "https://graph.facebook.com/v20.0/147248695141121/messages";
                var client = new RestClient(url);
                var request = new RestRequest(url);
                request.RequestFormat = DataFormat.Json;
                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);

                _logRegister.LogInfo("Exp_SendToWhatsApp", req.To, response.Content, "1", 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401
                    //OK = 200
                };
            }

            catch (TimeoutException ex)
            {
                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage =ex.Message,

                };
                _logRegister.LogInfo("Exp_SendToWhatsAppTimeoutException", req.To, ex.Message, "1", 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("Exp_SendToWhatsAppException", req.To, "", ex.Message, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                return baseResponse;
            }
            return baseResponse;

        }
        
        public async Task<BaseResponse> SendToWhatsAppExp(SendToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("Exp_SendToWhatsApp", req.To, "Fcm-Whatsapp", "1", 1, 1);

                var cloudApiTemplateRequest = new CloudApiTemplateRequest()
                {
                    to = req.To,
                    template = new Template()
                    {
                        name = "cash_help3",
                        language = new Language()
                        {
                            code = "en"
                        },
                        components = new List<Components>()
                    {
                        new Components()
                        {
                            parameters=new List<Parameters>()
                            { new Parameters()
                                {
                                    text=req.Message
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberIDExp}/messages";
                // " https://graph.facebook.com/v20.0/147248695141121/messages"
                //"https://graph.facebook.com/v20.0/110898085304010/messages"
                // url = "https://graph.facebook.com/v20.0/147248695141121/messages";
                var client = new RestClient(url);
                var request = new RestRequest(url);

                request.RequestFormat = DataFormat.Json;

                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);

                _logRegister.LogInfo("Exp_SendToWhatsApp", req.To, response.Content, "1", 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401
                    //OK = 200
                };
            }

            catch (TimeoutException ex)
            {
                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                _logRegister.LogInfo("Exp_SendToWhatsAppTimeoutException", req.To, ex.Message, "1", 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("Exp_SendToWhatsAppException", req.To, ex.Message, "1", 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage =ex.Message,

                };
                return baseResponse;
            }
            return baseResponse;

        }

        
        public async Task<BaseResponse> SendToWhatsAppCash(SendToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("SendToWhatsAppCash", req.To, "", "1", 1, 1);

                var cloudApiTemplateRequest = new CloudApiTemplateRequest()
                {
                    to = req.To,
                    template = new Template()
                    {
                        name = "cash_help",
                        language = new Language()
                        {
                            code = "en"
                        },
                        components = new List<Components>()
                    {
                        new Components()
                        {
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                    text=req.Message
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberID}/messages";
                // " https://graph.facebook.com/v20.0/147248695141121/messages"
                //"https://graph.facebook.com/v20.0/110898085304010/messages"
                // url = "https://graph.facebook.com/v20.0/147248695141121/messages";
                var client = new RestClient(url);
                var request = new RestRequest(url);

                request.RequestFormat = DataFormat.Json;

                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);

                _logRegister.LogInfo("SendToWhatsAppCash", req.To, response.Content, "1", 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401
                    //OK = 200
                };
            }

            catch (TimeoutException ex)
            {


                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage =  ex.Message,

                };
                _logRegister.LogInfo("SendToWhatsAppCashTimeoutException", req.To, ex.Message, "1", 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("SendToWhatsAppCashException", req.To, ex.Message, "1", 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                return baseResponse;
            }
            return baseResponse;

        }

        /// <summary>
        /// send PDf file 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
      
        public async Task<BaseResponse> SendPDFToWhatsApp(SendPDFToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("SendPDFToWhatsApp", req.To, "Fcm-Whatsapp", "1", 1, 1);

                var cloudApiTemplateRequest = new CloudApiTemplateRequest()
                {
                    to = req.To,
                    template = new Template()
                    {
                        name = "cash_pdf_4",
                        language = new Language()
                        {
                            code = "en",
                            policy = "deterministic"
                        },
                        components = new List<Components>()
                    {
                        new Components()
                        {type ="header",
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                    type="document",
                                    document=new Documents
                                    {
                                        link=req.Link

                                    }
                                    //text=req.Message
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberID}/messages";

                var client = new RestClient(url);
                var request = new RestRequest(url);

                request.RequestFormat = DataFormat.Json;

                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);

                _logRegister.LogInfo("SendPDFToWhatsApp", req.To, "", response.Content, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401
                    //OK = 200
                };
            }

            catch (TimeoutException ex)
            {


                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage =ex.Message,

                };
                _logRegister.LogInfo("SendPDFToWhatsAppTimeoutException", req.To, "", ex.Message, 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("SendPDFToWhatsAppException", req.To, "", ex.Message, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                return baseResponse;
            }
            return baseResponse;

        }


        
        public async Task<BaseResponse> SendToWhatsAppAlert(SendToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("Alert_SendToWhatsApp", req.To, "", "1", req, 1, 1);

                var cloudApiTemplateRequest = new CloudApiTemplateRequest()
                {
                    to = req.To,
                    template = new Template()
                    {
                        name = "triggers",
                        language = new Language()
                        {
                            code = "en"
                        },
                        components = new List<Components>()
                    {
                        new Components()
                        {
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                    text=req.Message
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberID}/messages";

                var client = new RestClient(url);
                var request = new RestRequest(url);

                request.RequestFormat = DataFormat.Json;

                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);

                _logRegister.LogInfo("Alert_SendToWhatsApp2", req.To, "", "1", response.Content, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401
                    //OK = 200
                };
            }

            catch (TimeoutException ex)
            {

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                _logRegister.LogInfo("Alert_SendToWhatsAppTimeoutException", req.To, "", ex.Message, 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("Alert_SendToWhatsAppTimeoutException", req.To, "", ex.Message, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message

                };
                return baseResponse;
            }
            return baseResponse;

        }
       
        public async Task<BaseResponse> SendToWhatsAppOTP(SendToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("Exp_SendToWhatsAppOTP", req.To, "", "1", req, 1, 1);
                string mess = req.Message;
                if (ApiConfig.Mode != "Live")
                {
                    mess = req.Message + " -" + ApiConfig.Mode;
                }
                var cloudApiTemplateRequest = new CloudApiTemplateRequest2()
                {
                    to = req.To,
                    template = new Template2()
                    {
                        name = "cash2",
                        language = new Language()
                        {
                            code = "ar",
                            policy = "deterministic"
                        },
                        components = new List<Components2>()
                    {
                        new Components2()
                        {
                             type="body",
                             index=null,
                             sub_type=null,
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                     type = "text",
                                    text=mess,
                                     currency=null,
                                    date_time=null,
                                    document=null
                                }
                            }
                        },
                         new Components2()
                        {
                             type="button",
                             index="0",
                             sub_type="url",
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                    type = "text",
                                    text=req.Message,
                                    currency=null,
                                    date_time=null,
                                    document=null
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberID}/messages";

                var client = new RestClient(url);
                var request = new RestRequest(url);

                request.RequestFormat = DataFormat.Json;

                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);
                _logRegister.LogInfo("Exp_SendToWhatsAppOTP", req.To, "", "1", response.Content, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401 
                    //OK = 200
                };
            }
            catch (TimeoutException ex)
            {

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                _logRegister.LogInfo("Exp_SendToWhatsAppOTPTimeoutException", req.To, "", ex.Message, 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("Exp_SendToWhatsAppOTPException", req.To, "", ex.Message, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                return baseResponse;
            }
            return baseResponse;

        }

       
        public async Task<BaseResponse> SendToWhatsAppCKR(SendToWhatsAppCommand req)
        {
            BaseResponse baseResponse = new BaseResponse();

            try
            {
                _logRegister.LogInfo("CKR_SendToWhatsAppCKR", req.To, "", "1", req, 1, 1);
                string mess = req.Message;
                if (ApiConfig.Mode != "Live")
                {
                    mess = req.Message + " -" + ApiConfig.Mode;
                }
                var cloudApiTemplateRequest = new CloudApiTemplateRequest2()
                {
                    to = req.To,
                    template = new Template2()
                    {
                        name = "cashCKR",
                        language = new Language()
                        {
                            code = "ar",
                            policy = "deterministic"
                        },
                        components = new List<Components2>()
                    {
                        new Components2()
                        {
                             type="body",
                             index=null,
                             sub_type=null,
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                     type = "text",
                                    text=mess,
                                     currency=null,
                                    date_time=null,
                                    document=null
                                }
                            }
                        },
                         new Components2()
                        {
                             type="button",
                             index="0",
                             sub_type="url",
                            parameters=new List<Parameters>()
                            {
                                new Parameters()
                                {
                                    type = "text",
                                    text=req.Message,
                                    currency=null,
                                    date_time=null,
                                    document=null
                                }
                            }
                        }
                    }
                    }
                };

                string url = $"https://graph.facebook.com/{ApiConfig.Version}/{ApiConfig.PhoneNumberID}/messages";

                var client = new RestClient(url);
                var request = new RestRequest(url);

                request.RequestFormat = DataFormat.Json;

                request.AddHeader("Authorization", string.Format("Bearer {0}", ApiConfig.authToken));

                request.AddJsonBody(cloudApiTemplateRequest);

                var response = await client.ExecuteAsync(request,Method.Post);
                _logRegister.LogInfo("CKR_SendToWhatsAppCKR", req.To, "", "1", response.Content, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = (response.IsSuccessful) ? 1 : -1,
                    ResultMessage = response.Content,
                    //BadRequest = UnRegistered = 400
                    //Unauthorized = ExpiredToken = 401
                    //OK = 200
                };
            }
            catch (TimeoutException ex)
            {

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                _logRegister.LogInfo("CKR_SendToWhatsAppCKRTimeoutException", req.To, "", ex.Message, 1, 2);

                return baseResponse;
            }
            catch (Exception ex)
            {

                _logRegister.LogInfo("CKR_SendToWhatsAppCKRException", req.To, "", ex.Message, 1, 2);

                baseResponse = new BaseResponse()
                {
                    ResultCode = -1,
                    ResultMessage = ex.Message,

                };
                return baseResponse;
            }
            return baseResponse;

        }

        //[HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        //[Route("SendToWhatsAppOTP")]
        //public async Task<BaseResponse> SendToWhatsAppOTP(SendToWhatsAppReq req)
        //{
        //    var cloudApiTemplateRequest = new CloudApiTemplateRequest()
        //    {
        //        to = req.To,
        //        template = new Template()
        //        {
        //            name = "cash_otp_ar",
        //            language = new Language()
        //            {
        //                code = "ar"
        //            },
        //            components = new List<Components>()
        //            {
        //                new Components()
        //                {
        //                    parameters=new List<Parameters>()
        //                    {
        //                        new Parameters()
        //                        {
        //                            text=req.Message
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    };

        //    string url = $"https://graph.facebook.com/{ConfigurationManager.AppSettings["Version"]}/{ConfigurationManager.AppSettings["PhoneNumberID"]}/messages";

        //    var client = new RestClient(url);
        //    var request = new RestRequest(url, Method.POST);

        //    request.RequestFormat = DataFormat.Json;

        //    request.AddHeader("Authorization", string.Format("Bearer {0}", ConfigurationManager.AppSettings["authToken"]));

        //    request.AddJsonBody(cloudApiTemplateRequest);

        //    var response = await client.ExecuteAsync(request);

        //    var baseResponse = new BaseResponse()
        //    {
        //        ResultCode = (response.IsSuccessful) ? 1 : -1,
        //        ResultMessage = response.StatusCode,
        //        //BadRequest = UnRegistered = 400
        //        //Unauthorized = ExpiredToken = 401
        //        //OK = 200
        //    };
        //    return baseResponse;
        //}

        //[HttpPost]
        //[Authorized(Roles = "PMBExptriate,PMBBusiness")]
        //[Route("SendToWhatsAppmedia")]
        //public async Task<BaseResponse> SendToWhatsAppMedia(SendToWhatsAppReq req)
        //{
        //    var cloudApiTemplateRequest = new CloudApiTemplateRequest()
        //    {
        //        to = req.To,
        //        template = new Template()
        //        {
        //            name= "cash",
        //            language = new Language() { },
        //            components = new List<Components>()
        //            {

        //                new Components()
        //                {
        //                    type="header",
        //                    parameters=new List<Parameters>()
        //                    {                                
        //                        new Parameters()
        //                        {
        //                            type = "document",
        //                            document=new Document()
        //                            {
        //                                link=req.Link ,
        //                                filename= req.Filename
        //                            }
        //                       }
        //                    }
        //                },
        //                new Components()
        //                {
        //                    type="body",
        //                    parameters=new List<Parameters>()
        //                    {        
        //                        new Parameters()
        //                        {
        //                            type = "text",
        //                            text=req.Message
        //                        }                                                                  
        //                    }
        //                }
        //            }
        //        }
        //    };


        //    string url = $"https://graph.facebook.com/{ConfigurationManager.AppSettings["Version"]}/{ConfigurationManager.AppSettings["PhoneNumberID"]}/messages";

        //    var client = new RestClient(url);
        //    var request = new RestRequest(url, Method.POST);

        //    request.RequestFormat = DataFormat.Json;

        //    request.AddHeader("Authorization", string.Format("Bearer {0}", ConfigurationManager.AppSettings["authToken"]));

        //    request.AddJsonBody(cloudApiTemplateRequest);

        //    var response = await client.ExecuteAsync(request);

        //    var baseResponse = new BaseResponse()
        //    {
        //        ResultCode = (response.IsSuccessful) ? 1 : -1,
        //        ResultMessage = response.StatusCode,
        //        //BadRequest = UnRegistered = 400
        //        //Unauthorized = ExpiredToken = 401
        //        //OK = 200
        //    };
        //    return baseResponse;
        //}

    }
}
