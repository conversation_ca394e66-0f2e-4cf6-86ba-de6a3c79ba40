﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.Inetlab.SMPP;
using Tamkeen.Inetlab.SMPP.Builders;
using Tamkeen.Inetlab.SMPP.Common;
using Tamkeen.Inetlab.SMPP.Logging;
using Tamkeen.Inetlab.SMPP.PDU;

namespace Tamkeen.SMPP.SmscServerGatewayProxyServiceTestLiveProduction
{
    internal class OldSmscClient
    {
        private readonly MessageComposer _messageComposer;
        private readonly ILog _log;

        public readonly SmppClient _client;
        public OldSmscClient()
        {
            LogManager.SetLoggerFactory(new ConsoleLogFactory(LogLevel.Info));
            _client = new SmppClient();
            _client.ResponseTimeout = TimeSpan.FromSeconds(60);
            _client.EnquireLinkInterval = TimeSpan.FromSeconds(20);

            _client.evDisconnected += new DisconnectedEventHandler(client_evDisconnected);
            _client.evDeliverSm += new DeliverSmEventHandler(client_evDeliverSm);
            _client.evEnquireLink += new EnquireLinkEventHandler(client_evEnquireLink);
            _client.evUnBind += new UnBindEventHandler(client_evUnBind);
            _client.evDataSm += new DataSmEventHandler(client_evDataSm);
            _client.evRecoverySucceeded += ClientOnRecoverySucceeded;

            _client.evServerCertificateValidation += OnCertificateValidation;


            _messageComposer = new MessageComposer();
            _messageComposer.evFullMessageReceived += OnFullMessageReceived;
            _messageComposer.evFullMessageTimeout += OnFullMessageTimeout;
        }
        private void client_evDisconnected(object sender)
        {
            _log.Info("SmppClient disconnected");

        }
        private void client_evDeliverSm(object sender, DeliverSm data)
        {
            try
            {
                //Check if we received Delivery Receipt
                if (data.MessageType == MessageTypes.SMSCDeliveryReceipt)
                {
                    //Get MessageId of delivered message
                    string messageId = data.Receipt.MessageId;
                    MessageState deliveryStatus = data.Receipt.State;

                    _log.Info("Delivery Receipt received: {0}", data.Receipt.ToString());
                }
                else
                {

                    // Receive incoming message and try to concatenate all parts
                    if (data.Concatenation != null)
                    {
                        _messageComposer.AddMessage(data);

                        _log.Info("DeliverSm part received: Sequence: {0}, SourceAddress: {1}, Concatenation ( {2} )" +
                                " Coding: {3}, Text: {4}",
                                data.Header.Sequence, data.SourceAddress, data.Concatenation, data.DataCoding, _client.EncodingMapper.GetMessageText(data));
                    }
                    else
                    {
                        _log.Info("DeliverSm received : Sequence: {0}, SourceAddress: {1}, Coding: {2}, Text: {3}",
                            data.Header.Sequence, data.SourceAddress, data.DataCoding, _client.EncodingMapper.GetMessageText(data));
                    }

                    // Check if an ESME acknowledgement is required
                    if (data.Acknowledgement != SMEAcknowledgement.NotRequested)
                    {
                        // You have to clarify with SMSC support what kind of information they request in ESME acknowledgement.

                        string messageText = data.GetMessageText(_client.EncodingMapper);

                        var smBuilder = SMS.ForSubmit()
                            .From(data.DestinationAddress)
                            .To(data.SourceAddress)
                            .Coding(data.DataCoding)
                            .Concatenation(ConcatenationType.UDH8bit, _client.SequenceGenerator.NextReferenceNumber())
                            .Set(m => m.MessageType = MessageTypes.SMEDeliveryAcknowledgement)
                            .Text(new Receipt
                            {
                                DoneDate = DateTime.Now,
                                State = MessageState.Delivered,
                                //  MessageId = data.Response.MessageId,
                                ErrorCode = "0",
                                SubmitDate = DateTime.Now,
                                Text = messageText.Substring(0, Math.Min(20, messageText.Length))
                            }.ToString()
                            );



                        _client.SubmitAsync(smBuilder).ConfigureAwait(false);
                    }
                }
            }
            catch (Exception ex)
            {
                data.Response.Header.Status = CommandStatus.ESME_RX_T_APPN;
                _log.Error(ex, "Failed to process DeliverSm");
            }
        }
        private void client_evDataSm(object sender, DataSm data)
        {
            _log.Info("DataSm received : Sequence: {0}, SourceAddress: {1}, DestAddress: {2}, Coding: {3}, Text: {4}",
                data.Header.Sequence, data.SourceAddress, data.DestinationAddress, data.DataCoding, data.GetMessageText(_client.EncodingMapper));
        }
        private void OnFullMessageTimeout(object sender, MessageEventHandlerArgs args)
        {
            _log.Info("Incomplete message received From: {0}, Text: {1}", args.GetFirst<DeliverSm>().SourceAddress, args.Text);
        }

        private void OnFullMessageReceived(object sender, MessageEventHandlerArgs args)
        {
            _log.Info("Full message received From: {0}, To: {1}, Text: {2}", args.GetFirst<DeliverSm>().SourceAddress, args.GetFirst<DeliverSm>().DestinationAddress, args.Text);
        }

        private void client_evEnquireLink(object sender, EnquireLink data)
        {
            _log.Info("EnquireLink received");
        }


        private void client_evUnBind(object sender, UnBind data)
        {
            _log.Info("UnBind request received");
        }
        private void ClientOnRecoverySucceeded(object sender, BindResp data)
        {
            _log.Info("Connection has been recovered.");

        }
        private void OnCertificateValidation(object sender, CertificateValidationEventArgs args)
        {
            //accept all certificates
            args.Accepted = true;
        }
    }
}
