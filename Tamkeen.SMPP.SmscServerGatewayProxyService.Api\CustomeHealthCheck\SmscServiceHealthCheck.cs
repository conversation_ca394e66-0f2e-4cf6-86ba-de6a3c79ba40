﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Tamkeen.SMPP.SmscServerGatewayProxyService.Api.CustomeHealthCheck
{
    public class SmscServiceHealthCheck : IHealthCheck
    {
        private readonly SmscServiceManager _smscServiceManager;
        private readonly Serilog.ILogger _logger;
        private static DateTime? _lastUnhealthyTime; // Static field for shared state
        private readonly HealthCheckSettings _healthCheckSettings; // Injected settings

        public SmscServiceHealthCheck(SmscServiceManager smscServiceManager, Serilog.ILogger logger, IOptions<HealthCheckSettings> healthCheckSettings)
        {
            _smscServiceManager = smscServiceManager ?? throw new ArgumentNullException(nameof(smscServiceManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _healthCheckSettings = healthCheckSettings?.Value
                                  ?? throw new ArgumentNullException(nameof(healthCheckSettings));
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                bool isRunning = _smscServiceManager.IsSmscServiceRunning();

                if (isRunning)
                {
                    // Reset the last unhealthy time if the service is running
                    _lastUnhealthyTime = null;
                    return HealthCheckResult.Healthy("Smsc service is running.");
                }

                // Service is not running
                _logger.Warning("Smsc service is not running.");

                // Track downtime
                if (_lastUnhealthyTime == null)
                {
                    _lastUnhealthyTime = DateTime.UtcNow;
                }
                else if ((DateTime.UtcNow - _lastUnhealthyTime.Value).TotalSeconds > _healthCheckSettings.RestartThresholdSeconds) // 60 seconds threshold
                {
                    _logger.Warning("Smsc service has been down for more than 5 seconds. Attempting to restart...");
                    await _smscServiceManager.RestartServiceAsync(cancellationToken);

                    // Check again after restart
                    isRunning = _smscServiceManager.IsSmscServiceRunning();
                    if (isRunning)
                    {
                        _logger.Information("Smsc service restarted successfully.");
                        return HealthCheckResult.Healthy("Smsc service was restarted and is now running.");
                    }

                    _logger.Error("Smsc service restart failed.");
                }

                return HealthCheckResult.Unhealthy("Smsc service is not running.");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "An error occurred while checking the Smsc service health.");
                return HealthCheckResult.Unhealthy("An exception occurred while checking the Smsc service health.", ex);
            }
        }
    }
}
